using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Extensions;
using Binit.Framework.Interfaces.ExceptionHandling;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.BusinessLogic.DTOs.Genetic;
using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HenStageLang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.HenStageChartsBusinessLogic;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.LayingChartsBusinessLogic;
using StatsLang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.StatisticsBusinessLogic;

namespace Domain.Logic.BusinessLogic.ChartsBusinessLogic
{
    public class SearaLayingChartsBusinessLogic : ISearaLayingChartsBusinessLogic
    {
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IExceptionManager exceptionManager;
        private readonly IUnitOfWork unitOfWork;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IHenBatchService henBatchService;
        private readonly IHatcheringReportService hatcheringReportService;
        private readonly IHenReportService henReportService;
        private readonly IEggQualityReportService eggQualityReportService;
        private readonly ICommonChartsBusinessLogic chartFunctions;

        public SearaLayingChartsBusinessLogic(
            IStringLocalizer<SharedResources> localizer,
            IExceptionManager exceptionManager,
            IUnitOfWork unitOfWork,
            ICapacityUnitService capacityUnitService,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IHenBatchService henBatchService,
            IHatcheringReportService hatcheringReportService,
            IHenReportService henReportService,
            IEggQualityReportService eggQualityReportService,
            ICommonChartsBusinessLogic chartFunctions)
        {
            this.localizer = localizer;
            this.exceptionManager = exceptionManager;
            this.unitOfWork = unitOfWork;
            this.capacityUnitService = capacityUnitService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.henBatchService = henBatchService;
            this.hatcheringReportService = hatcheringReportService;
            this.henReportService = henReportService;
            this.eggQualityReportService = eggQualityReportService;
            this.chartFunctions = chartFunctions;
        }
        #region Colors
        public const string redColor = "#E74C3C";
        public const string blueColor = "#2471A3";
        public const string greenColor = "#2ECC71";
        public const string lightblueColor = "#85C1E9";
        public const string orangeColor = "#F18A2E";
        public const string yellowColor = "#EDFB42";
        public const string violetColor = "#C042FB";
        public const string uniformitySTDColor = "#145A32"; // dark green
        public const string cvSTDColor = "#633974"; // violet
        public const string femaleSeriesColor = redColor;
        public const string maleSeriesColor = blueColor;
        public const string productionColor = redColor;
        public const string contaminatedEggsColor = redColor;
        public const string hatcheringColor = greenColor;
        public const string fertilityColor = lightblueColor;
        #endregion

        #region Seara Laying Charts

        public DashboardLineOrBarChartDTO GetSearaLayingFeedIntakeGADChart(FilterDataDTO filters = null)
        {
            int weekStart = filters.StartWeek ?? 22;
            int weekEnd = filters.EndWeek ?? 70;
            int totalWeeks = (weekEnd - weekStart + 1);

            var henBatchPerformancesQuery = GetSearaLayingPerformances(filters)
                .Where(hbp => hbp.WeekNumber >= weekStart && hbp.WeekNumber <= weekEnd);

            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";

            if (!henBatchPerformancesQuery.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(localizer["Não há dados de desempenho para o lote de aves."]));
            }

            var feedIntakeData = henBatchPerformancesQuery
                .Where(hbp => isMale ? hbp.WeekInitialHenAmountMale > 0 : hbp.WeekInitialHenAmountFemale > 0)
                .Select(hbp => new LayingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = isMale ?
                            (hbp.WeekInitialHenAmountMale > 0 ? hbp.FeedIntakeMale * 1000M / hbp.WeekInitialHenAmountMale / 7M : 0) :
                            (hbp.WeekInitialHenAmountFemale > 0 ? hbp.FeedIntakeFemale * 1000M / hbp.WeekInitialHenAmountFemale / 7M : 0),
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + (isBoth ? (isMale ? " (M)" : " (F)") : ""),
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.WeekInitialHenAmountMale : hbp.WeekInitialHenAmountFemale,
                })
                .ToList();

            if (isBoth)
            {
                var femaleFeedIntakeData = henBatchPerformancesQuery
                    .Where(hbp => hbp.WeekInitialHenAmountFemale > 0)
                    .Select(hbp => new LayingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = hbp.FeedIntakeFemale * 1000M / hbp.WeekInitialHenAmountFemale / 7M,
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name,
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.WeekInitialHenAmountFemale,
                    })
                    .ToList();

                if (isMale)
                {
                    feedIntakeData.AddRange(femaleFeedIntakeData);
                }
                else if (!feedIntakeData.Any())
                {
                    feedIntakeData = henBatchPerformancesQuery
                        .Where(hbp => hbp.WeekInitialHenAmountMale > 0)
                        .Select(hbp => new LayingDataPointDTO
                        {
                            Date = hbp.Date,
                            WeekNumber = hbp.WeekNumber,
                            Value = hbp.FeedIntakeMale * 1000M / hbp.WeekInitialHenAmountMale / 7M,
                            ParentHenBatchId = hbp.HenBatch.ParentId,
                            ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (M)",
                            WarehouseId = hbp.HenBatch.Line.WarehouseId,
                            WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                            LineId = hbp.HenBatch.Line.Id,
                            LineName = hbp.HenBatch.Line.Name,
                            GeneticId = hbp.HenBatch.GeneticId,
                            AveragingWeight = hbp.WeekInitialHenAmountMale,
                        })
                        .ToList();
                }
            }

            if (!feedIntakeData.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(localizer["Não há dados de consumo de ração para o lote de aves."]));
            }

            LayingChartOptionsDTO options = new LayingChartOptionsDTO
            {
                Title = this.localizer[Lang.FeedIntakeChartTitle] + (isBoth ? " (machos e fêmeas)" : (isMale ? " (machos)" : " (fêmeas)")),
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.FeedIntakeAxis],
                YMin = 0,
                YMax = 300,
                YTickAmount = 14,
                TotalWeeks = totalWeeks
            };

            LayingChart chart = new LayingChart(options);

            chart.WeekOffset = weekStart;

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            if (feedIntakeData.Any() && feedIntakeData.First().GeneticId.HasValue)
            {
                AddStandardSeries(chart, feedIntakeData.First().GeneticId.Value,
                    isBoth ? this.localizer[Lang.GADMaleSTD] + " / " + this.localizer[Lang.GADFemaleSTD] :
                            (isMale ? this.localizer[Lang.GADMaleSTD] : this.localizer[Lang.GADFemaleSTD]),
                    isMale ? maleSeriesColor : femaleSeriesColor,
                    p => isMale ? (p.FeedIntakeMale * 1000M / 7M) : (p.FeedIntakeFemale * 1000M / 7M),
                    "g");
            }

            AddGroupedSeriesToChart(filters, chart, feedIntakeData,
                isBoth ? this.localizer[Lang.GADMaleReal] + " / " + this.localizer[Lang.GADFemaleReal] :
                        (isMale ? this.localizer[Lang.GADMaleReal] : this.localizer[Lang.GADFemaleReal]),
                "g", CalculateSimpleAverage);

            var chartResult = chart.GetChart();
            chartResult.WeekStart = weekStart;
            chartResult.WeekEnd = weekEnd;

            return chartResult;
        }


        public DashboardLineOrBarChartDTO GetSearaMaleFemaleChart(FilterDataDTO filters = null)
        {
            var henBatchPerformancesQuery = GetSearaLayingPerformances(filters);

            var maleFemaleData = henBatchPerformancesQuery
                .Select(hbp => new LayingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    HenAmountMale = hbp.HenAmountMale,
                    HenAmountFemale = hbp.HenAmountFemale,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                })
                .ToList();

            var (weekStart, weekEnd) = GetDefaultWeekRange(filters);
            int totalWeeks = weekEnd - weekStart + 1;

            LayingChartOptionsDTO options = new LayingChartOptionsDTO
            {
                Title = "Relação M/F",
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = "Machos/Fêmeas (%)",
                YMin = 1,
                YMax = 15,
                YTickAmount = 15,
                TotalWeeks = totalWeeks,
            };

            LayingChart chart = new LayingChart(options);

            chart.WeekOffset = weekStart;

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            AddGroupedSeriesToChart(filters, chart, maleFemaleData, "Machos/Fêmeas (%)", "%", CalculateMaleFemaleRatio);

            AddStandardSeries(chart, maleFemaleData.First().GeneticId.Value, "Machos/Fêmeas STD (%)", greenColor, p => p.MaleFemaleRatio);

            return chart.GetChart();
        }


        private decimal CalculateMaleFemaleRatio(IGrouping<int, LayingDataPointDTO> dataPoints)
        {
            var maleCount = dataPoints.Sum(point => point.HenAmountMale);
            var femaleCount = dataPoints.Sum(point => point.HenAmountFemale);
            return femaleCount == 0 ? 0 : (100M * maleCount / femaleCount);
        }

        public GeneticGraphicResponseDTO GetGeneticChartData(FilterDataDTO filters)
        {
            var henBatchId = filters.HenBatchId;

            if (henBatchId == null)
                throw exceptionManager.Handle(new NotFoundException("É necessário selecionar um lote"));

            // Get genetic id from hen batch
            var geneticId = henBatchService.GetAll()
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => hb.GeneticId)
                .Distinct()
                .FirstOrDefault();

            if (geneticId == null)
                throw exceptionManager.Handle(new NotFoundException("Não há dados para exibir"));

            // Get genetics parameters by genetic id
            var geneticsParametersList = geneticsParameterService.GetAll()
                .Where(gp => gp.GeneticsId == geneticId)
                .Where(gp => gp.HenStage == HenStage.Laying)
                .Where(gp => gp.WeightFemale > 0 || gp.WeightMale > 0 || gp.MortalityFemaleWeek > 0) // only weeks with relevant data
                .Include(gp => gp.Genetics)
                .OrderBy(gp => gp.TimePeriodValue)
                .ToList();

            // Get hen batch performances
            var henBatchPerformances = henBatchPerformanceService.GetAll();

            if (filters.LineId != null)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatch.ParentId == henBatchId)
                    .Where(hbp => hbp.HenBatch.LineId == filters.LineId);
            }
            else if (filters.WarehouseId != null)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatch.ParentId == henBatchId)
                    .Where(hbp => hbp.HenBatch.Line.WarehouseId == filters.WarehouseId);
            }
            else
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatchId == henBatchId);
            }

            var henBatchPerformancesList = henBatchPerformances
                .Include(hbp => hbp.HenBatch)
                .OrderBy(hbp => hbp.WeekNumber)
                .ToList();

            // Set period from Genetic Parameters
            int firstWeek = geneticsParametersList.First().TimePeriodValue;
            int lastWeek = geneticsParametersList.Last().TimePeriodValue;

            //Initialize DTO
            GeneticGraphicResponseDTO geneticGraphicDTOs = new GeneticGraphicResponseDTO()
            {
                HenStage = HenStage.Laying,
                Data = new List<GeneticGraphicDTO>()
            };

            //Build graphic data -> one DTO per week. 
            for (int week = firstWeek; week <= lastWeek; week++)
            {
                GeneticGraphicDTO dto = new GeneticGraphicDTO() { WeekNumber = week };

                //Set current values
                //Mortality is accumulative.
                var hbp = henBatchPerformancesList.Where(h => h.WeekNumber == week).FirstOrDefault();
                if (hbp != null)
                {
                    dto.TotalProducedEggsWeekly = Math.Round((((double)hbp.TotalEggs / hbp.WeekInitialHenAmountFemale) * 100) / 7, 2);
                    double hatchableEggsWeekly = hbp.IncubatedEggs == 0 ? 0 : ((double)hbp.HatchedEggs / hbp.IncubatedEggs) * 100;
                    dto.HatchableEggsWeekly = Math.Round(hatchableEggsWeekly, 2);
                    dto.FertileEggsWeekly = Math.Round((double)hbp.FertileEggsPercentage, 2);

                    dto.BodyWeightFemale = hbp.AvgFemaleBirdWeight != null ? Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, hbp.AvgFemaleBirdWeight.Value)) : 0;
                    dto.BodyWeightMale = hbp.AvgMaleBirdWeight != null ? Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, hbp.AvgMaleBirdWeight.Value)) : 0;
                    dto.MortalityFemale = hbp.HenBatch.InitialHenAmountFemale != 0 ? Math.Round(((double)hbp.DeadFemale / (double)hbp.HenBatch.InitialHenAmountFemale) * 100, 2) : 0;
                    dto.MaxTemp = Decimal.ToDouble(hbp.MaxTemp);
                    dto.MinTemp = Decimal.ToDouble(hbp.MinTemp);
                }

                //STD: standard -> genetic parameters reference
                //Set standard values
                GeneticsParametersReference gpr = geneticsParametersList.Where(g => g.TimePeriodValue == week).FirstOrDefault();
                if (gpr != null)
                {
                    dto.TotalProducedEggsWeeklySTD = (double)gpr.TotalProducedEggsWeeklyPercentage;
                    dto.HatchingEggsWeeklySTD = (double)gpr.HatchingWeeklyPercentage;
                    dto.FertileEggsWeeklySTD = (double)gpr.FertileEggsPercentage;
                    dto.BodyWeightSTDMaxFemale = Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, gpr.WeightFemale));
                    dto.BodyWeightSTDMaxMale = Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, gpr.WeightMale));
                    dto.MortalitySTDFemale = Math.Round((double)gpr.MortalityFemaleWeek, 2);
                }

                geneticGraphicDTOs.Data.Add(dto);
            }

            return geneticGraphicDTOs;
        }

        public DashboardLineOrBarChartDTO GetSaleableChicksChart(FilterDataDTO filters)
        {
            var (weekStart, weekEnd) = GetDefaultWeekRange(filters);

            var hatcheringReportsQuery = GetHatcheringReports(filters);
            var performancesQuery = GetSearaLayingPerformances(filters)
                .Where(hbp => hbp.WeekNumber >= weekStart && hbp.WeekNumber <= weekEnd);

            // Handle gender filtering
            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";
            var isFemale = filters.Gender == "2" || (!isMale && !isBoth); // Default to female if not specified

            // Filter performances based on gender - only include records with the appropriate gender data
            if (isMale)
            {
                performancesQuery = performancesQuery.Where(hbp => hbp.HenAmountMale > 0);
            }
            else if (isFemale && !isBoth)
            {
                performancesQuery = performancesQuery.Where(hbp => hbp.HenAmountFemale > 0);
            }

            // Get marketable chicks data from hatchering reports
            var marketableChicksData = hatcheringReportsQuery
                .Select(hr => new
                {
                    WeekNumber = hr.Week,
                    MarketableChicks = isMale ? hr.ViableMales :
                                      hr.ViableFemales + hr.ViableMales + hr.ViableMixed,
                    ParentHenBatchId = hr.HenBatch.ParentId ?? hr.HenBatchId, // Use HenBatchId if ParentId is null
                    ParentHenBatchName = hr.HenBatch.Parent != null ?
                                        (hr.HenBatch.Parent.DetailedName ?? hr.HenBatch.Parent.Name ?? "Lote pai desconhecido") :
                                        (hr.HenBatch.DetailedName ?? hr.HenBatch.Name ?? "Lote desconhecido"),
                    WarehouseId = hr.HenBatch.Line.WarehouseId,
                    WarehouseName = hr.HenBatch.Line.Warehouse.Name,
                    LineId = hr.HenBatch.Line.Id,
                    LineName = hr.HenBatch.Line.Name,
                    GeneticId = hr.HenBatch.GeneticId,
                    HenBatchId = hr.HenBatchId,
                    HenBatchType = hr.HenBatch.ParentId.HasValue ? "Child" : (hr.HenBatch.LineId.HasValue ? "Single" : "Parent"),
                    OriginalParentId = hr.HenBatch.ParentId,
                    OriginalHenBatchId = hr.HenBatchId
                })
                .Where(dp => dp.WeekNumber >= weekStart && dp.WeekNumber <= weekEnd)
                .ToList();

            // Get capitalized females data from performances - use the capitalization date value from parent batch
            var capitalizedFemalesData = performancesQuery
                .Select(hbp => new
                {
                    WeekNumber = hbp.WeekNumber,
                    CapitalizedFemales = hbp.HenBatch.Parent != null ? hbp.HenBatch.Parent.FemalesOnFirstProductionDate : hbp.HenBatch.FemalesOnFirstProductionDate,
                    ParentHenBatchId = hbp.HenBatch.ParentId ?? hbp.HenBatchId, // Use HenBatchId if ParentId is null to match marketableChicksData
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    LineId = hbp.HenBatch.Line.Id,
                    GeneticId = hbp.HenBatch.GeneticId,
                    HenBatchId = hbp.HenBatchId
                })
                .ToList();

            // Get the total capitalized females for each batch (from capitalization date)
            var totalCapitalizedFemalesByBatch = capitalizedFemalesData
                .GroupBy(cf => cf.ParentHenBatchId)
                .ToDictionary(
                    g => g.Key,
                    g => g.First().CapitalizedFemales // Use the first record's value as it represents the capitalization date value
                );



            if (!marketableChicksData.Any() && !capitalizedFemalesData.Any())
            {
                throw exceptionManager.Handle(
                    new NotFoundException("Não há dados de pintos vendáveis nem de fêmeas capitalizadas para o período selecionado.")
                );
            }
            else if (!marketableChicksData.Any())
            {
                throw exceptionManager.Handle(
                    new NotFoundException("Não há dados de pintos vendáveis (HatcheringReports) para o período selecionado.")
                );
            }
            else if (!capitalizedFemalesData.Any())
            {
                throw exceptionManager.Handle(
                    new NotFoundException("Não há dados de fêmeas capitalizadas (HenBatchPerformances) para o período selecionado.")
                );
            }

            // Create a combined dataset - only need marketable chicks data since we have total capitalized females by batch
            var combinedData = marketableChicksData
                .Where(mc => totalCapitalizedFemalesByBatch.ContainsKey(mc.ParentHenBatchId))
                .Select(mc => new
                {
                    mc.WeekNumber,
                    mc.MarketableChicks,
                    TotalCapitalizedFemales = totalCapitalizedFemalesByBatch[mc.ParentHenBatchId],
                    mc.ParentHenBatchId,
                    mc.ParentHenBatchName,
                    mc.WarehouseId,
                    mc.WarehouseName,
                    mc.LineId,
                    mc.LineName,
                    mc.GeneticId,
                    mc.HenBatchId
                });

            // Group by hen batch and calculate cumulative values for each batch
            var weeklyData = combinedData
                .GroupBy(x => new { x.ParentHenBatchId, x.ParentHenBatchName })
                .SelectMany(batchGroup =>
                {
                    decimal cumulativeValue = 0;
                    var totalCapitalizedFemales = batchGroup.First().TotalCapitalizedFemales;

                    return batchGroup
                        .GroupBy(x => x.WeekNumber)
                        .OrderBy(weekGroup => weekGroup.Key)
                        .Select(weekGroup =>
                        {
                            var totalMarketableChicks = weekGroup.Sum(x => x.MarketableChicks);

                            // Calculate weekly percentage: marketable chicks / total capitalized females (from capitalization date)
                            decimal weeklyPercentage = totalCapitalizedFemales > 0
                                ? (decimal)totalMarketableChicks / (decimal)totalCapitalizedFemales
                                : 0;

                            // Add to cumulative value (cumulative weekly calculation)
                            cumulativeValue += weeklyPercentage;

                            var firstRecord = weekGroup.First();
                            return new LayingDataPointDTO
                            {
                                WeekNumber = weekGroup.Key,
                                Value = cumulativeValue,
                                ParentHenBatchId = firstRecord.ParentHenBatchId,
                                ParentHenBatchName = firstRecord.ParentHenBatchName,
                                WarehouseId = firstRecord.WarehouseId,
                                WarehouseName = firstRecord.WarehouseName,
                                LineId = firstRecord.LineId,
                                LineName = firstRecord.LineName,
                                GeneticId = firstRecord.GeneticId,
                                AveragingWeight = (int)totalCapitalizedFemales
                            };
                        });
                })
                .ToList();

            if (!weeklyData.Any())
            {
                throw exceptionManager.Handle(
                    new NotFoundException("Não foi possível calcular dados de Vendáveis para o período selecionado.")
                );
            }

            int totalWeeks = weekEnd - weekStart + 1;

            var chartOptions = new LayingChartOptionsDTO
            {
                Title = "Pintos Vendáveis / F",
                XAxisLabel = $"Semanas ({weekStart} a {weekEnd})",
                YAxisLabel = "Pintos Vendáveis",
                YMin = 0,
                YTickAmount = 5,
                TotalWeeks = totalWeeks
            };

            var chart = new LayingChart(chartOptions);
            chart.WeekOffset = weekStart;

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            if (weeklyData.Any() && weeklyData.First().GeneticId.HasValue)
            {
                // Get genetics parameters for the STD line
                var geneticsParameters = geneticsParameterService.GetAll()
                    .Where(gpr => gpr.HenStage == HenStage.Laying && gpr.GeneticsId == weeklyData.First().GeneticId.Value)
                    .ToList();

                // Create STD series using direct values from CumulativeViableBirthsByHen
                var stdData = geneticsParameters
                    .Where(p => p.TimePeriodValue >= weekStart && p.TimePeriodValue <= weekEnd)
                    .OrderBy(p => p.TimePeriodValue)
                    .Select(p => new LayingChartPointDTO()
                    {
                        WeekNumber = p.TimePeriodValue,
                        Value = p.CumulativeViableBirthsByHen
                    })
                    .ToList();

                chart.AddSeries(new LayingChartSeriesDTO()
                {
                    SeriesLabel = "Pintos Vendáveis (STD)",
                    Data = stdData,
                    Color = redColor,
                    Type = "line",
                    UnitSymbol = "",
                    IsSecondaryAxis = false
                });
            }


            // Use the same logic as GetOIFCChart for consistency
            AddGroupedSeriesToChartWithColor(filters, chart, weeklyData, "Pintos Vendáveis (Real)", blueColor);

            var chartResult = chart.GetChart();
            chartResult.WeekStart = weekStart;
            chartResult.WeekEnd = weekEnd;

            return chartResult;
        }

        public DashboardLineOrBarChartDTO GetOIFCChart(FilterDataDTO filters = null)
        {
            var (weekStart, weekEnd) = GetDefaultWeekRange(filters);
            int totalWeeks = (weekEnd - weekStart + 1);

            var performancesQuery = GetSearaLayingPerformances(filters)
                .Where(hbp => hbp.WeekNumber >= weekStart && hbp.WeekNumber <= weekEnd);

            // get hen batch ids associated with the performances
            var henBatchIds = performancesQuery.Select(p => p.HenBatchId).Distinct();
            // get accumulated pre-capitalization hatchable eggs
            // this is used to get the accumulated hatchable eggs after capitalization
            var preCapitalizationHatchableEggs = henReportService.GetAll(asNoTracking: true)
                .Where(hr => henBatchIds.Contains(hr.HenBatchId))
                .Where(hr => hr.Date < hr.HenBatch.CapitalizationDate)
                .Select(hr => new
                {
                    hr.HenBatchId,
                    hr.HatchableEggs
                })
                .ToList()
                .GroupBy(x => x.HenBatchId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Sum(x => x.HatchableEggs)
                );

            var realData = performancesQuery
                .Select(hbp => new
                {
                    WeekNumber = hbp.WeekNumber,
                    Date = hbp.Date,
                    FemalesOnFirstProductionDate = hbp.FemalesOnFirstProductionDate,
                    HatchableEggsAccum = hbp.HatchableEggsAccum,
                    HenBatchId = hbp.HenBatchId,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = (int)hbp.FemalesOnFirstProductionDate
                })
                .ToList()
                .Select(p => new LayingDataPointDTO
                {
                    WeekNumber = p.WeekNumber,
                    Date = p.Date,
                    Value = (p.FemalesOnFirstProductionDate > 0)
                            ? (decimal)(p.HatchableEggsAccum - preCapitalizationHatchableEggs.GetValueOrDefault(p.HenBatchId, 0)) / (decimal)p.FemalesOnFirstProductionDate
                            : 0,
                    ParentHenBatchId = p.ParentHenBatchId,
                    ParentHenBatchName = p.ParentHenBatchName,
                    WarehouseId = p.WarehouseId,
                    WarehouseName = p.WarehouseName,
                    LineId = p.LineId,
                    LineName = p.LineName,
                    GeneticId = p.GeneticId,
                    AveragingWeight = p.AveragingWeight
                })
                .ToList();

            if (!realData.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(
                    localizer["Não há dados de OIFC (ovos incubáveis/fêmea) para o lote de aves nesse intervalo."]));
            }

            var chartOptions = new LayingChartOptionsDTO
            {
                Title = "OI/FC",
                XAxisLabel = $"Semanas ({weekStart} a {weekEnd})",
                YAxisLabel = "OIFC",
                YMin = 0,
                YMax = 220,
                YTickAmount = 11,
                TotalWeeks = totalWeeks
            };

            var chart = new LayingChart(chartOptions)
            {
                WeekOffset = weekStart
            };

            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            if (realData.Any() && realData.First().GeneticId.HasValue)
            {
                AddStandardSeries(
                    chart,
                    realData.First().GeneticId.Value,
                    "OIFC (STD)",
                    redColor,
                    p => p.CumulativeIncubatedEggsByHen,
                    ""
                );
            }

            AddGroupedSeriesToChart(filters, chart, realData, "OIFC (Real)", blueColor);

            var chartResult = chart.GetChart();
            chartResult.WeekStart = weekStart;
            chartResult.WeekEnd = weekEnd;

            return chartResult;
        }

        public async Task<DashboardLineOrBarChartDTO> GetBedEggsChartAsync(FilterDataDTO filters, CancellationToken cancellationToken)
        {
            var (weekStart, weekEnd) = GetDefaultWeekRange(filters);

            var henBatchIds = await GetHenBatchIdsFromFilters(filters).ToListAsync(cancellationToken);
            if (!henBatchIds.Any())
                throw exceptionManager.Handle(new NotFoundException("Nenhum lote encontrado."));

            var reportsQuery = henReportService.GetAll(asNoTracking: true)
                .Include(hr => hr.ClassifiedEggs)
                .ThenInclude(ce => ce.Material)
                .Include(hr => hr.HenBatch)
                .ThenInclude(hb => hb.Parent)
                .Include(hr => hr.HenBatch)
                .ThenInclude(hb => hb.Line)
                .ThenInclude(l => l.Warehouse)
                .Where(hr => hr.HenBatch.HenStage == HenStage.Laying);

            if (henBatchIds.Any())
                reportsQuery = reportsQuery.Where(hr => henBatchIds.Contains(hr.HenBatchId));

            if (filters?.MinDate.HasValue == true && filters?.MaxDate.HasValue == true)
            {
                reportsQuery = reportsQuery.Where(hr => hr.Date >= filters.MinDate.Value && hr.Date <= filters.MaxDate.Value);
            }

            var reports = await reportsQuery.ToListAsync(cancellationToken);

            if (!reports.Any())
                throw exceptionManager.Handle(new NotFoundException("Não há HenReports para esse filtro."));

            var firstHenBatchId = henBatchIds.First();
            WeekNumberAndStart weekNumber = GetWeeNumberAndStart(firstHenBatchId);

            var bedEggsData = reports
                .Select(hr => new
                {
                    HenBatchWeek = CurrentWeek(weekNumber, hr.Date),
                    TotalEggs = hr.TotalEggs,
                    FloorEggs = hr.ClassifiedEggs
                        .Where(ce => ce.Material.Name == "Cama")
                        .Sum(ce => ce.Quantity),
                    HenBatchId = hr.HenBatchId,
                    ParentHenBatchId = hr.HenBatch.ParentId ?? hr.HenBatchId,
                    ParentHenBatchName = hr.HenBatch.Parent?.DetailedName ?? hr.HenBatch.DetailedName,
                    WarehouseId = hr.HenBatch.Line.WarehouseId,
                    WarehouseName = hr.HenBatch.Line.Warehouse.Name,
                    LineId = hr.HenBatch.Line.Id,
                    LineName = hr.HenBatch.Line.Name,
                    GeneticId = hr.HenBatch.GeneticId,
                    Date = hr.Date
                })
                .Where(x => x.HenBatchWeek >= weekStart && x.HenBatchWeek <= weekEnd)
                .GroupBy(x => new { x.HenBatchWeek, x.ParentHenBatchId })
                .Select(g => new LayingDataPointDTO
                {
                    WeekNumber = g.Key.HenBatchWeek,
                    Date = g.First().Date,
                    Value = g.Sum(x => x.TotalEggs) > 0 
                        ? 100m * g.Sum(x => x.FloorEggs) / g.Sum(x => x.TotalEggs) 
                        : 0m,
                    ParentHenBatchId = g.Key.ParentHenBatchId,
                    ParentHenBatchName = g.First().ParentHenBatchName,
                    WarehouseId = g.First().WarehouseId,
                    WarehouseName = g.First().WarehouseName,
                    LineId = g.First().LineId,
                    LineName = g.First().LineName,
                    GeneticId = g.First().GeneticId,
                    AveragingWeight = 1 // Use 1 for simple averaging
                })
                .ToList();

            if (!bedEggsData.Any())
                throw exceptionManager.Handle(new NotFoundException("Nenhum registro de ovos de cama no intervalo especificado."));

            int totalWeeks = weekEnd - weekStart + 1;

            var chart = new LayingChart(new LayingChartOptionsDTO
            {
                Title = "% Ovos Cama",
                XAxisLabel = $"Semanas ({weekStart} a {weekEnd})",
                YAxisLabel = "% Ovos Cama",
                YMin = 0,
                YMax = 25,
                YTickAmount = 5,
                TotalWeeks = totalWeeks
            });

            chart.SetChartWithWeekRange(weekStart, weekEnd);

            AddGroupedSeriesToChartWithColor(filters, chart, bedEggsData, "Ovos Cama (Real)", blueColor, "%", 
                CalculateSimpleAverage);

            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetViabilityChart(FilterDataDTO filters = null)
        {
            filters ??= new FilterDataDTO();

            var (weekStart, weekEnd) = GetDefaultWeekRange(filters, defaultEnd: 66);

            filters.StartWeek = weekStart;
            filters.EndWeek = weekEnd;

            var henBatchPerformancesQuery = GetSearaLayingPerformances(filters);

            var viabilityData = henBatchPerformancesQuery
                .Where(hbp => hbp.HenBatchLifeTimeFemaleViability != 0)
                .Select(hbp => new LayingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = hbp.HenBatchLifeTimeFemaleViability,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = hbp.HenAmountFemale,
                })
                .ToList();


            LayingChart chart = new LayingChart(new LayingChartOptionsDTO
            {
                Title = this.localizer[Lang.ViabilityChartTitle],
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.ViabilityAxis] + " (%)",
                YMin = 90,
                YMax = 102,
                YTickAmount = 13,
                TotalWeeks = weekEnd - weekStart + 1
            });

            chart.WeekOffset = weekStart;
            for (int week = weekStart; week <= weekEnd; week++)
            {
                chart.GetChart().Categories.Add(week.ToString());
            }

            AddStandardSeries(chart, viabilityData.First().GeneticId.Value, localizer[Lang.ViableFemaleSTD], femaleSeriesColor, p => p.ViableFemalesPercentage);

            AddGroupedSeriesToChart(filters, chart, viabilityData, this.localizer[Lang.ViableFemaleReal], "%", CalculateSimpleAverage);

            var chartResult = chart.GetChart();
            chartResult.WeekStart = weekStart;
            chartResult.WeekEnd = weekEnd;

            return chartResult;
        }


        private IQueryable<HenBatchPerformance> GetLayingHatcheringAndFertilityPerformancesQuery(FilterDataDTO filters)
        {

            // Get hen batch performance data
            IQueryable<HenBatchPerformance> query = henBatchPerformanceService.GetAll(asNoTracking: true)
                .Include(hbp => hbp.HenBatch)
                    .ThenInclude(hb => hb.Parent) // Include Parent relationship
                .Include(hbp => hbp.HenBatch)
                    .ThenInclude(hb => hb.Line)
                    .ThenInclude(l => l.Warehouse); // Include Line and Warehouse for completeness

            if (filters != null)
            {
                if (filters.MinDate != null && filters.MaxDate != null)
                {
                    query = query.Where(hbp => hbp.Date >= filters.MinDate.Value && hbp.Date <= filters.MaxDate.Value);
                }

                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(hbp =>
                        hbp.WeekNumber >= filters.StartWeek.Value &&
                        hbp.WeekNumber <= filters.EndWeek.Value);
                }
            }

            List<Guid> henBatchIds = new List<Guid>();
            if (filters?.HenBatchId.HasValue == true)
            {
                henBatchIds.Add(filters.HenBatchId.Value);

                // Include parent and child batches when a specific batch is selected
                var selectedBatch = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == filters.HenBatchId.Value);

                if (selectedBatch != null)
                {
                    if (selectedBatch.ParentId.HasValue)
                    {
                        // If selected batch has a parent, include the parent and all its children (siblings)
                        var parentId = selectedBatch.ParentId.Value;
                        henBatchIds.Add(parentId);
                        var siblingIds = henBatchService.GetAll()
                            .Where(hb => hb.ParentId == parentId && hb.Id != filters.HenBatchId.Value)
                            .Select(hb => hb.Id)
                            .ToList();
                        henBatchIds.AddRange(siblingIds);
                    }
                    else
                    {
                        // If selected batch is a parent, include all its children
                        var childIds = henBatchService.GetAll()
                            .Where(hb => hb.ParentId == filters.HenBatchId.Value)
                            .Select(hb => hb.Id)
                            .ToList();
                        henBatchIds.AddRange(childIds);
                    }
                }

                henBatchIds = henBatchIds.Distinct().ToList();
            }
            else if (filters != null)
            {
                henBatchIds = GetHenBatchIdsFromFilters(filters).ToList();
            }
            else
            {
                // Get all active laying hen batches
                henBatchIds = henBatchService.GetAll()
                    .Where(hb => hb.HenStage == HenStage.Laying && hb.Active)
                    .Select(hb => hb.Id)
                    .ToList();
            }

            query = query.Where(hbp => henBatchIds.Contains(hbp.HenBatchId) && hbp.HenBatch.HenStage == HenStage.Laying);

            return query;
        }

        /// <summary>
        /// Get data for laying hatchering and fertility chart.
        /// </summary>
        public async Task<DashboardLineOrBarChartDTO> GetLayingHatcheringAndFertilityChartAsync(FilterDataDTO filters, CancellationToken cancellationToken = default)
        {
            filters.SetDefaultWeekRange(70, 24);

            var query = GetLayingHatcheringAndFertilityPerformancesQuery(filters);

            var dataPoints = await query
                .Select(hbp => new LayingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName ?? hbp.HenBatch.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    HenBatchPerformance = hbp
                })
                .ToListAsync(cancellationToken);

            var layingChart = LayingChart.CreateWithWeekRange(
                new LayingChartOptionsDTO
                {
                    Title = localizer["Produção, Eclosão e Fertilidade"],
                    XAxisLabel = localizer[Lang.WeekXAxisLabel],
                    YAxisLabel = localizer[Lang.PercentageAxis],
                    YMin = 0,
                    YMax = 100,
                    TotalWeeks = filters.TotalWeeks,
                },
                filters.StartWeek, 
                filters.EndWeek
            );

            if (dataPoints.Any() && dataPoints.First().GeneticId.HasValue)
            {
                var genetics = await geneticsParameterService
                    .GetAll(asNoTracking: true)
                    .Where(gpr => gpr.HenStage == HenStage.Laying && gpr.GeneticsId == dataPoints.FirstOrDefault().GeneticId)
                    .ToListAsync(cancellationToken);

                AddStandardSeriesByGenetics(
                    layingChart,
                    genetics,
                    localizer[Lang.LayingSTD],
                    productionColor,
                    p => p.TotalProducedEggsWeeklyPercentage.RoundPercentage(),
                    "%"
                );

                AddStandardSeriesByGenetics(
                    layingChart,
                    genetics,
                    localizer[Lang.HatcheringSTD],
                    hatcheringColor,
                    p => p.HatchingWeeklyPercentage.RoundPercentage(),
                    "%"
                );

                AddStandardSeriesByGenetics(
                    layingChart,
                    genetics,
                    localizer[Lang.FertilitySTD],
                    fertilityColor,
                    p => p.FertileEggsPercentage.RoundPercentage(),
                    "%"
                );
            }

            AddGroupedSeriesToChart(
                filters,
                layingChart,
                dataPoints,
                localizer[Lang.LayingReal],
                "%",
                group =>
                {
                    var weekInitialHenAmountFemaleSum = group
                        .Where(d => d.HenBatchPerformance.DistinctDayCount != 0)
                        .Sum(d => d.HenBatchPerformance.WeekInitialHenAmountFemale);

                    return weekInitialHenAmountFemaleSum > 0
                        ? 100M * group.Sum(d => d.HenBatchPerformance.TotalEggs) / (7M * weekInitialHenAmountFemaleSum)
                        : 0;
                },
                consolidatedColor: productionColor,
                groupByParentHenBatchOnly: false,
                fillEmptySpacesInBetween: true
            );

            AddGroupedSeriesToChart(
                filters,
                layingChart,
                dataPoints,
                localizer[Lang.HatcheringReal],
                "%",
                group =>
                {
                    var incubatedEggsSum = group.Sum(d => d.HenBatchPerformance.IncubatedEggs);

                    return incubatedEggsSum > 0
                        ? 100M * group.Sum(d => d.HenBatchPerformance.HatchedEggs) / incubatedEggsSum
                        : 0;
                },
                consolidatedColor: hatcheringColor,
                groupByParentHenBatchOnly: false,
                fillEmptySpacesInBetween: true
            );

            AddGroupedSeriesToChart(
                filters,
                layingChart,
                dataPoints,
                localizer[Lang.FertilityReal],
                "%",
                group => group.Any(d => d.HenBatchPerformance.FertileEggsPercentage != 0)
                        ? group.Sum(d => d.HenBatchPerformance.FertileEggsPercentage) /
                          group.Count(d => d.HenBatchPerformance.FertileEggsPercentage != 0)
                        : 0,
                consolidatedColor: fertilityColor,
                groupByParentHenBatchOnly: false,
                fillEmptySpacesInBetween: true
            );

            return layingChart.GetChart();
        }

        /// <summary>
        /// Get data for egg quality.
        /// </summary>
        public async Task<DashboardLineOrBarChartDTO> GetDataForEggQualityChartAsync(FilterDataDTO filters, CancellationToken cancellationToken)
        {
            filters.SetDefaultWeekRange(68, 24);

            var layingChart = LayingChart.CreateWithWeekRange(new LayingChartOptionsDTO()
                {
                    Title = localizer[Lang.EggQualityChartTitle],
                    XAxisLabel = localizer[Lang.WeekXAxisLabel],
                    YMin = 0,
                    YAxisLabel = localizer[Lang.PercentageAxis]
                }, 
                filters.StartWeek, 
                filters.EndWeek
            );

            var modelEggQuality = layingChart.GetChart();

            // Get hen batch IDs from filters - handle both single batch and multiple batch scenarios
            var henBatchIds = new List<Guid>();

            if (filters?.HenBatchId.HasValue == true)
            {
                henBatchIds.Add(filters.HenBatchId.Value);
            }
            else if (filters != null)
            {
                henBatchIds = await GetHenBatchIdsFromFilters(filters).ToListAsync(cancellationToken);
            }

            if (!henBatchIds.Any())
                return NoDataAvilable(modelEggQuality);

            var query = eggQualityReportService
                .GetAll(asNoTracking: true)
                .Where(eqr => henBatchIds.Contains(eqr.HenBatchId));

            if (!await query.AnyAsync(cancellationToken))
                return NoDataAvilable(modelEggQuality);

            query = query
                .Include(eqr => eqr.EggType)
                    .ThenInclude(et => et.Standards)
                .Include(eqr => eqr.HenBatch)
                    .ThenInclude(hb => hb.Farm)
                .Select(eqr => new EggQualityReport
                {
                    ProductionDate = eqr.ProductionDate,
                    Samples = eqr.Samples,
                    InconsistentQuantity = eqr.InconsistentQuantity,
                    EggType = eqr.EggType,
                    EggTypeId = eqr.EggTypeId,
                    HenBatchId = eqr.HenBatchId,
                    HenBatch = eqr.HenBatch
                });

            var eggQualityReports = await query.ToListAsync(cancellationToken);

            var formattedReports = eggQualityReports
               .Select(eqr => new
               {
                   eqr.ProductionDate,
                   HenBatchWeek = CurrentWeek(GetWeekNumberAndStartByHenBatch(eqr.HenBatch), eqr.ProductionDate),
                   eqr.Samples,
                   eqr.InconsistentQuantity,
                   eqr.EggType,
                   eqr.EggTypeId,
                   eqr.HenBatch,
                   eqr.HenBatchId
               });

            // For multiple batches, we need to calculate week numbers relative to each batch
            var data = formattedReports
                .GroupBy(eqr => new { eqr.EggTypeId, eqr.HenBatchWeek })
                .Select(geqr =>
                {
                    var eggType = geqr.FirstOrDefault().EggType;
                    var samplesSum = geqr.Sum(eqr => eqr.Samples);

                    return new
                    {
                        Week = geqr.Key.HenBatchWeek,
                        geqr.Key.EggTypeId,
                        MQOReal = samplesSum > 0 ? 100M * geqr.Sum(eqr => eqr.InconsistentQuantity) / samplesSum : 0,
                        MQOSTD = eggType.Standards.FirstOrDefault()?.OptimalIntervalValueMax ?? 0,
                        EggName = eggType.Name,
                    };
                });

            // Current week - use the first batch for reference, or default to a reasonable value
            var currentWeek = henBatchIds.Any() ? CurrentWeek(GetWeeNumberAndStart(henBatchIds.First()), DateTime.Now) : 50;

            // color list 
            var colors = new List<string> { violetColor, greenColor, orangeColor, blueColor, lightblueColor, yellowColor, redColor };

            var eggsType = data.GroupBy(eqr => eqr.EggTypeId);
            var colorIndex = colors.Count - 1;

            foreach (var anEggType in eggsType)
            {
                var mqoReal = new List<decimal?>(new decimal?[filters.TotalWeeks]);
                var mqoSTD = new List<decimal>(new decimal[filters.TotalWeeks]);
                // if more types of eggs are added, the colors are repeated
                colorIndex = colorIndex < 0 ? colors.Count() - 1 : colorIndex;

                foreach (var egg in anEggType)
                {
                    int index = egg.Week <= filters.EndWeek && egg.Week >= filters.StartWeek ? egg.Week - (int)filters.StartWeek : -1;

                    if (index >= 0)
                    {
                        decimal mqoRealValue = decimal.Round(egg.MQOReal, DashboardDecimalPrecision.TwoDecimalPrecision);
                        mqoReal[index] = mqoReal[index] == null ? mqoRealValue : mqoReal[index] + mqoRealValue;
                        mqoSTD[index] += decimal.Round(egg.MQOSTD, DashboardDecimalPrecision.TwoDecimalPrecision);
                    }
                }

                FillEmptyDataUpToCurrentWeek(currentWeek - (int)filters.StartWeek, mqoReal);
                FillEmptyDataUpToCurrentWeek(currentWeek - (int)filters.StartWeek, mqoSTD);
                
                if(filters.IsConsolidatedReportType)
                {
                    modelEggQuality.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = anEggType.Min(et => et.EggName),
                        Data = mqoReal,
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        ToolTip = new ToolTipDTO() { ValueSuffix = "" },
                        Color = colors[colorIndex],
                    });
                }

                modelEggQuality.Series.Add(new DecimalSeriesDTO
                {
                    Name = localizer[Lang.EggLabelSTD, anEggType.Min(et => et.EggName)],
                    Type = "line",
                    Data = mqoSTD.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" },
                    ShowInLegend = true,
                    Visible = true,
                    YAxis = 0,
                    Color = colors[colorIndex],
                });

                colorIndex--;
            }

            if (!filters.IsConsolidatedReportType)
            {
                var eggsByType = formattedReports
                    .GroupBy(eqr => new { eqr.EggTypeId, eqr.HenBatchWeek, eqr.HenBatchId })
                    .Select(geqr =>
                    {
                        var first = geqr.FirstOrDefault();

                        var eggType = first.EggType;
                        var henBatch = first.HenBatch;

                        var samplesSum = geqr.Sum(eqr => eqr.Samples);

                        return new
                        {
                            Week = geqr.Key.HenBatchWeek,
                            geqr.Key.EggTypeId,
                            geqr.Key.HenBatchId,
                            HenBatch = henBatch,
                            MQOReal = samplesSum > 0 ? 100M * geqr.Sum(eqr => eqr.InconsistentQuantity) / samplesSum : 0,
                            EggName = eggType.Name,
                        };
                    })
                    .GroupBy(e => e.EggTypeId);

                colorIndex = colors.Count - 1;

                foreach (var typeEgg in eggsByType)
                {
                    var eggTypeId = typeEgg.Key;
                    string eggTypeName = typeEgg.FirstOrDefault().EggName;
                    var eggTypeByHenBatch = typeEgg.GroupBy(e => e.HenBatchId);

                    colorIndex = colorIndex < 0 ? colors.Count() - 1 : colorIndex;

                    foreach (var henBatchEgg in eggTypeByHenBatch)
                    {
                        var mqoReal = new List<decimal?>(new decimal?[filters.TotalWeeks]);
                        string henBatchName = henBatchEgg.FirstOrDefault().HenBatch.DetailedName;

                        foreach (var egg in henBatchEgg)
                        {
                            int index = egg.Week <= filters.EndWeek && egg.Week >= filters.StartWeek ? egg.Week - (int)filters.StartWeek : -1;

                            if (index >= 0)
                            {
                                decimal mqoRealValue = decimal.Round(egg.MQOReal, DashboardDecimalPrecision.TwoDecimalPrecision);
                                mqoReal[index] = mqoReal[index] == null ? mqoRealValue : mqoReal[index] + mqoRealValue;
                            }
                        }

                        FillEmptyDataUpToCurrentWeek(currentWeek - (int)filters.StartWeek, mqoReal);

                        var seriesName = filters.HenBatchId.HasValue ? eggTypeName : $"{eggTypeName} - {henBatchName}";

                        modelEggQuality.Series.Add(new DecimalSeriesDTO()
                        {
                            Name = seriesName,
                            Data = mqoReal,
                            ShowInLegend = true,
                            Visible = true,
                            Type = "spline",
                            YAxis = 0,
                            ToolTip = new ToolTipDTO() { ValueSuffix = "" },
                            Color = colors[colorIndex],
                        });
                    }

                    colorIndex--;
                }
            } 

            return modelEggQuality;
        }

        #endregion

        #region Helper Methods

        private DashboardLineOrBarChartDTO NoDataAvilable(DashboardLineOrBarChartDTO model)
        {
            model.NoDataAvilableMessage = this.localizer[Lang.NoDataAvilableMessage];
            return model;
        }

        /// <summary>
        /// This method is a customization of henBatchService.GetCurrentWeekNumberForDate()
        /// </summary>
        private WeekNumberAndStart GetWeeNumberAndStart(Guid henBatchId)
        {
            var henBatch = henBatchService.GetAll(asNoTracking: true)
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => new HenBatch { DateStart = hb.DateStart, BatchWeekNumber = hb.BatchWeekNumber, Farm = new Farm { DayOfWeek = hb.Farm.DayOfWeek } })
                .FirstOrDefault();

            return GetWeekNumberAndStartByHenBatch(henBatch);
        }

        public WeekNumberAndStart GetWeekNumberAndStartByHenBatch(HenBatch henBatch)
        {
            DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek);

            return new WeekNumberAndStart(weekStart, henBatch.BatchWeekNumber);
        }

        private Func<WeekNumberAndStart, DateTime, int> CurrentWeek = (weekNumber, date) => weekNumber.Number + (date - weekNumber.Start).Days / 7;

        private void FillEmptyDataUpToCurrentWeek<T>(int currentWeek, List<T> data)
        {
            var lastIndexWithDate = data.FindLastIndex(x => !x.Equals(default(T)));
            for (int potentialWeekToUpdate = 0; potentialWeekToUpdate < currentWeek; potentialWeekToUpdate++)
            {
                if (potentialWeekToUpdate <= lastIndexWithDate)
                {
                    decimal value = Convert.ToDecimal(data[potentialWeekToUpdate]);

                    if (value == 0)
                    {
                        int weekWithData = potentialWeekToUpdate > 0 ? potentialWeekToUpdate - 1 : 0;
                        data[potentialWeekToUpdate] = data[weekWithData];
                    }

                }
            }
        }

        private void FillEmtyData(int currentWeek, List<decimal> data)
        {
            var firstValue = data.FirstOrDefault(nv => nv != 0);
            var lastValue = data.LastOrDefault(nv => nv != 0);
            var firstValuePosition = data.IndexOf(firstValue);
            var lastValuePosition = data.IndexOf(lastValue);
            var lastIndexWithDate = data.FindLastIndex(x => x != 0);
            for (int potentialWeekToUpdate = 0; potentialWeekToUpdate < currentWeek; potentialWeekToUpdate++)
            {
                decimal nextValueDecimal = 0;
                if (lastValuePosition != potentialWeekToUpdate && potentialWeekToUpdate >= firstValuePosition && potentialWeekToUpdate <= lastIndexWithDate)
                {
                    decimal value = data[potentialWeekToUpdate];
                    if (value == 0)
                    {
                        var subList = data.Skip(potentialWeekToUpdate).Take(data.Count() - 1).ToList();
                        decimal nextValue = subList.FirstOrDefault(nv => nv != 0);
                        nextValueDecimal = (data[potentialWeekToUpdate - 1] + nextValue) / 2;
                        data[potentialWeekToUpdate] = nextValueDecimal;
                    }

                }
            }
        }

        public struct WeekNumberAndStart
        {
            public DateTime Start;
            public int Number;

            public WeekNumberAndStart(DateTime date, int value)
            {
                Start = date;
                Number = value;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the default week range from filters or returns default values (22-70)
        /// </summary>
        /// <param name="filters">Filter data containing StartWeek and EndWeek</param>
        /// <returns>Tuple containing weekStart and weekEnd values</returns>
        private static (int weekStart, int weekEnd) GetDefaultWeekRange(FilterDataDTO filters, int defaultStart = 22, int defaultEnd = 70)
        {
            int weekStart = filters?.StartWeek ?? defaultStart;
            int weekEnd = filters?.EndWeek ?? defaultEnd;

            return (weekStart, weekEnd);
        }

        #endregion

        #region Data queries        
        /// <summary>
        /// Get list of hen batch Ids contained in the given container. Only parents or only children (self contained batches belong to all categories)
        /// </summary>
        public IQueryable<Guid> GetHenBatchIdsFromFilters(FilterDataDTO filters)
        {
            // set max date to last day of the month
            if (filters?.MaxDate != null)
            {
                filters.MaxDate = filters.MaxDate.Value.AddMonths(1).AddDays(-1);
            }

            if (!(filters.StartWeek.HasValue && filters.EndWeek.HasValue) && !(filters.MinDate.HasValue && filters.MaxDate.HasValue))
            {
                throw exceptionManager.Handle(new ValidationException("Date range is required"));
            }

            var henBatchesQuery = henBatchService.GetAll()
                .Where(hb => hb.HenStage == HenStage.Laying);

            if (filters.HenBatchStatus == "active" || (!filters.HenBatchId.HasValue && string.IsNullOrEmpty(filters.HenBatchStatus)))
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Active && hb.DateEnd == null);
            }
            else if (filters.HenBatchStatus == "closed")
            {
                henBatchesQuery = henBatchesQuery.Where(hb => !hb.Active || hb.DateEnd != null);
            }

            if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb =>

                    hb.BatchWeekNumber <= filters.EndWeek.Value &&
                    (
                        (hb.DateEnd != null &&
                         (hb.BatchWeekNumber + (int)Math.Ceiling(EF.Functions.DateDiffDay(hb.DateStart.Value, hb.DateEnd.Value) / 7.0)) >= filters.StartWeek.Value) ||
                        (hb.DateEnd == null &&
                         (hb.BatchWeekNumber + (int)Math.Ceiling(EF.Functions.DateDiffDay(hb.DateStart.Value, DateTime.Today) / 7.0)) >= filters.StartWeek.Value)
                    )
                );
            }

            if (filters.ProductorId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.FarmId == filters.ProductorId);
            }

            if (filters.HenBatchId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Id == filters.HenBatchId.Value || hb.ParentId == filters.HenBatchId.Value);
            }

            if (filters.WarehouseId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Line.WarehouseId == filters.WarehouseId.Value);
            }

            if (filters.LineId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.LineId == filters.LineId.Value);
            }

            if (filters.RegionalId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.Company.RegionalId == filters.RegionalId);
            }

            if (filters.UnitId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.CompanyId == filters.UnitId);
            }

            if (filters.SupervisorId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.SupervisorId == filters.SupervisorId);
            }

            if (filters.ExtensionistId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.TechnicianId == filters.ExtensionistId);
            }

            if (filters.MinDate.HasValue && filters.MaxDate.HasValue)
            {
                DateTime minDate = filters.MinDate.Value.Date;
                DateTime maxDate = filters.MaxDate.Value.Date;

                henBatchesQuery = henBatchesQuery.Where(hb =>
                    hb.DateStart <= maxDate &&
                    (hb.DateEnd ?? DateTime.Today) >= minDate
                );
            }

            if (filters.GeneticsId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.GeneticId == filters.GeneticsId);
            }

            return henBatchesQuery.Select(hb => hb.Id);
        }


        /// <summary>
        /// query for getting all the laying performances for the given filters
        /// </summary>
        private IQueryable<HatcheringReport> GetHatcheringReports(FilterDataDTO filters)
        {
            // set max date to last day of the month
            if (filters?.MaxDate != null)
            {
                filters.MaxDate = filters.MaxDate.Value.AddMonths(1).AddDays(-1);
            }

            IQueryable<HatcheringReport> query = hatcheringReportService.GetAll(asNoTracking: true)
            .Include(hr => hr.HenBatch)
                .ThenInclude(hb => hb.Parent) // Include Parent relationship
            .Include(hr => hr.HenBatch)
                .ThenInclude(hb => hb.Line)
                .ThenInclude(l => l.Warehouse) // Include Line and Warehouse for completeness
            .Where(hr => hr.HenBatch.HenStage == HenStage.Laying);

            if (filters != null)
            {
                if (filters.MinDate != null && filters.MaxDate != null)
                {
                    query = query.Where(hr => hr.HatcheringDate >= filters.MinDate.Value && hr.HatcheringDate <= filters.MaxDate.Value);
                }

                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(hr =>
                        hr.Week >= filters.StartWeek.Value &&
                        hr.Week <= filters.EndWeek.Value);
                }
            }

            var henBatchIds = GetHenBatchIdsFromFilters(filters);
            query = query.Where(hr => henBatchIds.Contains(hr.HenBatch.ParentId.Value) || henBatchIds.Contains(hr.HenBatchId));

            return query;
        }

        private IQueryable<HenBatchPerformance> GetSearaLayingPerformances(FilterDataDTO filters)
        {

            // Get hen batch performance data
            IQueryable<HenBatchPerformance> query = henBatchPerformanceService.GetAll(asNoTracking: true)
                .Include(hbp => hbp.HenBatch)
                    .ThenInclude(hb => hb.Parent) // Include Parent relationship
                .Include(hbp => hbp.HenBatch)
                    .ThenInclude(hb => hb.Line)
                    .ThenInclude(l => l.Warehouse) // Include Line and Warehouse for completeness
                .Where(hbp => hbp.HenBatch.HenStage == HenStage.Laying && hbp.ParentId != null);

            if (filters != null)
            {
                if (filters.MinDate != null && filters.MaxDate != null)
                {
                    query = query.Where(hbp => hbp.Date >= filters.MinDate.Value && hbp.Date <= filters.MaxDate.Value);
                }

                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(hbp =>
                        hbp.WeekNumber >= filters.StartWeek.Value &&
                        hbp.WeekNumber <= filters.EndWeek.Value);
                }
            }

            var henBatchIds = GetHenBatchIdsFromFilters(filters);
            query = query.Where(hbp => henBatchIds.Contains(hbp.HenBatch.ParentId.Value) || henBatchIds.Contains(hbp.HenBatchId));

            return query;
        }
        #endregion

        #region Chart data grouping
        private void AddGroupedSeriesToChart(
            FilterDataDTO filters,
            LayingChart chart,
            List<LayingDataPointDTO> dataPoints,
            string seriesLabel,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null,
            bool isSecondaryAxis = false,
            string consolidatedColor = null,
            bool groupByParentHenBatchOnly = true,
            bool fillEmptySpacesInBetween = false
        )
        {
            // consolidated
            if (filters.ReportType == "1")
            {
                LayingChartSeriesDTO consolidatedSeries = GetChartSeriesConsolidated(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                consolidatedSeries.IsSecondaryAxis = isSecondaryAxis;
                consolidatedSeries.Color = consolidatedColor;
                
                if (fillEmptySpacesInBetween)
                {
                    consolidatedSeries.FillEmptySpacesInBetweenWithLastValue();
                }

                chart.AddSeries(consolidatedSeries);
            }
            // open
            else if (filters.ReportType == "2")
            {
                // if batch is not selected, display data by batch
                if (!filters.HenBatchId.HasValue)
                {
                    var batchSeries = GetChartSeriesByBatch(dataPoints, seriesLabel, unitSymbol, averagingFunction, groupByParentHenBatchOnly);
                    batchSeries.ForEach(s =>
                    {
                        s.IsSecondaryAxis = isSecondaryAxis;

                        if (fillEmptySpacesInBetween)
                        {
                            s.FillEmptySpacesInBetweenWithLastValue();
                        }
                    });

                    chart.AddSeries(batchSeries);
                }
                // if batch is selected, display data by warehouse
                else
                {
                    var warehouseSeries = GetChartSeriesByWarehouse(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    warehouseSeries.ForEach(s =>
                    {
                        s.IsSecondaryAxis = isSecondaryAxis;

                        if (fillEmptySpacesInBetween)
                        {
                            s.FillEmptySpacesInBetweenWithLastValue();
                        }
                    });
                    chart.AddSeries(warehouseSeries);

                    // if warehouse is selected, also display data by line
                    if (filters.WarehouseId.HasValue)
                    {
                        var lineSeries = GetChartSeriesByLine(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                        lineSeries.ForEach(s =>
                        {
                            s.IsSecondaryAxis = isSecondaryAxis;

                            if (fillEmptySpacesInBetween)
                            {
                                s.FillEmptySpacesInBetweenWithLastValue();
                            }
                        });
                        chart.AddSeries(lineSeries);
                    }
                }
            }
        }

        #region Chart data grouping
        private void AddGroupedSeriesToChartWithColor(
            FilterDataDTO filters,
            LayingChart chart,
            List<LayingDataPointDTO> dataPoints,
            string seriesLabel,
            string color = null,
            string unitSymbol = "",
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null,
            bool isSecondaryAxis = false
        )
        {
            // consolidated
            if (filters.ReportType == "1")
            {
                var consolidatedSeries = GetChartSeriesConsolidated(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                consolidatedSeries.IsSecondaryAxis = isSecondaryAxis;
                consolidatedSeries.Color = color; // Set the color
                chart.AddSeries(consolidatedSeries);
            }
            // open
            else if (filters.ReportType == "2")
            {
                // if batch is not selected, display data by batch
                if (!filters.HenBatchId.HasValue)
                {
                    var batchSeries = GetChartSeriesByBatch(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    batchSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                    chart.AddSeries(batchSeries);
                }
                // if batch is selected, display data by warehouse and line
                else
                {
                    var warehouseSeries = GetChartSeriesByWarehouse(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    warehouseSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                    chart.AddSeries(warehouseSeries);

                    // if warehouse is selected, also display data by line
                    if (filters.WarehouseId.HasValue)
                    {
                        var lineSeries = GetChartSeriesByLine(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                        lineSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                        chart.AddSeries(lineSeries);
                    }
                }
            }
        }


        private List<LayingChartSeriesDTO> GetChartSeriesByBatch(
            List<LayingDataPointDTO> dataPoints,
            string seriesLabel,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null,
            bool groupByParentHenBatchOnly = true
        )
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => groupByParentHenBatchOnly ? point.ParentHenBatchId : point.ParentHenBatchId ?? point.HenBatchPerformance.HenBatchId,
                point =>
                {
                    var batchName = groupByParentHenBatchOnly ? point.ParentHenBatchName : point.ParentHenBatchName ?? point.HenBatchPerformance.HenBatch.DetailedName;
                    
                    return $"{seriesLabel} - {batchName}";
                },
                unitSymbol,
                averagingFunction
            );
        }

        private List<LayingChartSeriesDTO> GetChartSeriesByWarehouse(
            List<LayingDataPointDTO> dataPoints,
            string seriesLabel,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null
        )
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.WarehouseId,
                point => $"{seriesLabel} - {point.WarehouseName}",
                unitSymbol,
                averagingFunction
            );
        }

        private List<LayingChartSeriesDTO> GetChartSeriesByLine(
            List<LayingDataPointDTO> dataPoints,
            string seriesLabel,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null
        )
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.LineId,
                point => $"{seriesLabel} - {point.LineName}",
                unitSymbol,
                averagingFunction
            );
        }

        private LayingChartSeriesDTO GetChartSeriesConsolidated(
            List<LayingDataPointDTO> dataPoints,
            string seriesLabel,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null
        )
        {
            return new LayingChartSeriesDTO
            {
                SeriesLabel = seriesLabel,
                UnitSymbol = unitSymbol,
                Data = GroupDataPointsByWeek(dataPoints.ToList(), averagingFunction)
            };
        }

        private List<LayingChartSeriesDTO> GetChartSeriesByProperty<TKey>(
            List<LayingDataPointDTO> dataPoints,
            Func<LayingDataPointDTO, TKey> groupingSelector,
            Func<LayingDataPointDTO, string> labelSelector,
            string unitSymbol,
            Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return dataPoints
                .GroupBy(groupingSelector)
                .Select(groupedPoints => new LayingChartSeriesDTO
                {
                    SeriesLabel = labelSelector(groupedPoints.FirstOrDefault()),
                    UnitSymbol = unitSymbol,
                    Data = GroupDataPointsByWeek(groupedPoints.ToList(), averagingFunction)
                })
                .OrderBy(w => w.SeriesLabel)
                .ToList();
        }

        private List<LayingChartPointDTO> GroupDataPointsByWeek(List<LayingDataPointDTO> dataPoints, Func<IGrouping<int, LayingDataPointDTO>, decimal> averagingFunction = null)
        {
            return dataPoints
                .GroupBy(point => point.WeekNumber)
                .Select(a => new LayingChartPointDTO
                {
                    WeekNumber = a.Key,
                    Date = a.FirstOrDefault()?.Date ?? DateTime.MinValue,
                    Value = averagingFunction?.Invoke(a) ?? CalculateWeightedAverage(a)
                })
                .OrderBy(p => p.WeekNumber)
                .ToList();
        }

        private decimal CalculateWeightedAverage(IGrouping<int, LayingDataPointDTO> group)
        {
            var totalWeight = group.Sum(point => point.Value != 0 ? point.AveragingWeight : 0);
            if (totalWeight == 0) return 0;

            return group.Sum(point => point.Value * point.AveragingWeight) / totalWeight;
        }

        private decimal CalculateSimpleAverage(IGrouping<int, LayingDataPointDTO> dataPoints)
        {
            var validDataPoints = dataPoints.Where(point => point.Value != 0).ToList();
            if (!validDataPoints.Any())
                return 0M;

            var sum = validDataPoints.Sum(point => point.Value);
            var count = validDataPoints.Count;
            return Math.Round(sum / count, 2);
        }

        private void AddStandardSeries(
            LayingChart chart, 
            Guid geneticId, 
            string seriesLabel, 
            string color,
            Func<GeneticsParametersReference, decimal> valueSelector, 
            string unitSymbol = "%", 
            bool isSecondaryAxis = false, 
            string seriesType = "line"
        )
        {
            chart.AddSeries(new LayingChartSeriesDTO()
            {
                SeriesLabel = seriesLabel,
                Data = geneticsParameterService.GetAll(asNoTracking: true)
                    .Where(gpr => gpr.HenStage == HenStage.Laying && gpr.GeneticsId == geneticId)
                    .ToList()
                    .Select(p => new LayingChartPointDTO()
                    {
                        WeekNumber = p.TimePeriodValue,
                        Value = valueSelector(p)
                    }).ToList(),
                Color = color,
                Type = seriesType,
                UnitSymbol = unitSymbol,
                IsSecondaryAxis = isSecondaryAxis
            });
        }

        private void AddStandardSeriesByGenetics(
            LayingChart chart,
            List<GeneticsParametersReference> genetics,
            string seriesLabel,
            string color,
            Func<GeneticsParametersReference, decimal> valueSelector,
            string unitSymbol = "%",
            bool isSecondaryAxis = false,
            string seriesType = "line"
        )
        {
            chart.AddSeries(new LayingChartSeriesDTO()
            {
                SeriesLabel = seriesLabel,
                Data = genetics
                    .Select(p => new LayingChartPointDTO()
                    {
                        WeekNumber = p.TimePeriodValue,
                        Value = valueSelector(p)
                    }).ToList(),
                Color = color,
                Type = seriesType,
                UnitSymbol = unitSymbol,
                IsSecondaryAxis = isSecondaryAxis
            });
        }

        public Tuple<List<List<HenBatchStatusDTO>>, DashboardLineOrBarChartDTO> PerformanceChart(FilterDataDTO filters, HenStage? henStage = null)
        {
            // Extract parameters from filters
            DateTime minDate = filters?.MinDate ?? DateTime.Today.AddDays(-15);
            DateTime maxDate = filters?.MaxDate ?? DateTime.Today;

            List<Guid> henBatchIds = new List<Guid>();
            if (filters?.HenBatchId.HasValue == true)
            {
                henBatchIds.Add(filters.HenBatchId.Value);

                // When a specific batch is selected, also include its related batches (parent or children)
                // First, get the selected batch to understand its parent/child relationship
                var selectedBatch = henBatchService.GetAll()
                    .FirstOrDefault(hb => hb.Id == filters.HenBatchId.Value);

                if (selectedBatch != null)
                {
                    // If the selected batch has a parent, include all children of that parent
                    if (selectedBatch.ParentId.HasValue)
                    {
                        var siblingBatches = henBatchService.GetAll()
                            .Where(hb => hb.ParentId == selectedBatch.ParentId.Value &&
                                         hb.HenStage == HenStage.Laying &&
                                         hb.Active &&
                                         hb.DateStart <= maxDate &&
                                         (hb.DateEnd == null || hb.DateEnd >= minDate))
                            .Select(hb => hb.Id)
                            .ToList();
                        henBatchIds.AddRange(siblingBatches);

                        // Also include the parent batch
                        henBatchIds.Add(selectedBatch.ParentId.Value);
                    }
                    else
                    {
                        // If the selected batch is a parent, include all its children
                        var childBatches = henBatchService.GetAll()
                            .Where(hb => hb.ParentId == selectedBatch.Id &&
                                         hb.HenStage == HenStage.Laying &&
                                         hb.Active &&
                                         hb.DateStart <= maxDate &&
                                         (hb.DateEnd == null || hb.DateEnd >= minDate))
                            .Select(hb => hb.Id)
                            .ToList();
                        henBatchIds.AddRange(childBatches);
                    }

                    henBatchIds = henBatchIds.Distinct().ToList();
                }
            }
            else if (filters != null)
            {
                henBatchIds = GetHenBatchIdsFromFilters(filters).ToList();
            }

            // Ensure we have hen batches to work with
            if (!henBatchIds.Any())
            {
                // If no specific hen batches are found, get all active laying hen batches within the date range
                henBatchIds = henBatchService.GetAll()
                    .Where(hb => hb.HenStage == HenStage.Laying &&
                                 hb.Active &&
                                 hb.DateStart <= maxDate &&
                                 (hb.DateEnd == null || hb.DateEnd >= minDate))
                    .Select(hb => hb.Id)
                    .ToList();
            }

            // Initialize chart DTO
            DashboardLineOrBarChartDTO dto = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[HenStageLang.PerformanceChartTitle],
                XAxisLabel = this.localizer[HenStageLang.PerformanceChartXAxisLabel],
                Series = new List<DecimalSeriesDTO>(),
                YAxis = new List<YAxis>(),
                Categories = new List<string>(),
                NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage]
            };

            // Units symbol
            string eggsUnitSymbol = this.capacityUnitService.Get(CapacityUnits.Units).Symbol;
            //GAD symbol
            string feedIntakeUnitSymbol = this.localizer[HenStageLang.PerformanceChartGADSymbol];

            // Get hen reports which are new (not reverted) from hen batches of the selected stage
            // between date bounds making shure to consider only date and not time.
            IQueryable<HenReport> henReports = this.henReportService.GetAllFullForDashboard()
                .Where(hr => henBatchIds.Contains(hr.HenBatchId) && hr.ReportEnum == ReportEnum.New && minDate.Date <= hr.Date.Date && hr.Date.Date <= maxDate.Date);

            bool validHenStage = henStage.HasValue;

            List<HenBatchStatusDTO> henReportDTOs = henReports
                .Select(hr => new HenBatchStatusDTO
                {
                    DateStart = hr.Date.Date,
                    HenAmount = hr.HenAmountFemale + hr.HenAmountMale,
                    HenAmountFemale = hr.HenAmountFemale,
                    HenAmountMale = hr.HenAmountMale,
                    Dead = hr.DeadFemale + hr.DeadMale,
                    DeadFemale = hr.DeadFemale,
                    DeadMale = hr.DeadMale,
                    FeedIntakeFemale = hr.FeedIntakeFemale,
                    FeedIntakeMale = hr.FeedIntakeMale,
                    Eggs = hr.TotalEggs,
                    HatchableEggs = hr.HatchableEggs,
                    CommercialEggs = hr.CommercialEggs,
                    BrokenEggs = hr.BrokenEggs,
                    Id = hr.HenBatchId,
                    HenBatchName = hr.HenBatch.DetailedName,
                    LineId = hr.HenBatch.LineId.Value,
                    LineName = hr.HenBatch.Line.DetailedName,
                    WarehouseId = hr.HenBatch.Line.WarehouseId,
                    WarehouseName = hr.HenBatch.Line.Warehouse.DetailedName,
                    ClusterId = hr.HenBatch.Line.Warehouse.ClusterId,
                    ClusterName = hr.HenBatch.Line.Warehouse.Cluster.Name,
                    FarmId = hr.HenBatch.Line.Warehouse.Cluster.Farm.Id,
                    FarmName = hr.HenBatch.Line.Warehouse.Cluster.Farm.Name
                })
                .ToList();

            // We can't be sure there is only one hen report for every day
            // for a hen batch. Hence, accumulate data for every hen batch at
            // every date
            henReportDTOs = henReportDTOs
                .GroupBy(hr => new { hr.DateStart, hr.Id })
                .Select(g => new HenBatchStatusDTO
                {
                    DateStart = g.Key.DateStart,
                    HenAmount = g.Min(hr => hr.HenAmount),
                    HenAmountFemale = g.Min(hr => hr.HenAmountFemale),
                    HenAmountMale = g.Min(hr => hr.HenAmountMale),
                    Dead = g.Sum(hr => hr.Dead),
                    DeadFemale = g.Sum(hr => hr.DeadFemale),
                    DeadMale = g.Sum(hr => hr.DeadMale),
                    FeedIntakeFemale = g.Sum(hr => hr.FeedIntakeFemale),
                    FeedIntakeMale = g.Sum(hr => hr.FeedIntakeMale),
                    Eggs = decimal.Round(g.Sum(hr => hr.Eggs)),
                    HatchableEggs = (uint)g.Sum(hr => hr.HatchableEggs),
                    CommercialEggs = (uint)g.Sum(hr => hr.CommercialEggs),
                    BrokenEggs = (uint)g.Sum(hr => hr.BrokenEggs),
                    Id = g.Key.Id,
                    HenBatchName = g.Min(hr => hr.HenBatchName),
                    LineId = g.Min(hr => hr.LineId),
                    LineName = g.Min(hr => hr.LineName),
                    WarehouseId = g.Min(hr => hr.WarehouseId),
                    WarehouseName = g.Min(hr => hr.WarehouseName),
                    ClusterId = g.Min(hr => hr.ClusterId),
                    ClusterName = g.Min(hr => hr.ClusterName),
                    FarmId = g.Min(hr => hr.FarmId),
                    FarmName = g.Min(hr => hr.FarmName)
                })
                .ToList();

            // Vertical axis
            if (!validHenStage || (validHenStage && henStage == HenStage.Laying))
            {
                // Production axis
                dto.YAxis.Add(new YAxis()
                {
                    Min = 0,
                    GridLineWidth = 1,
                    Opposite = false,
                    Title = new TitleDTO() { Text = this.localizer[HenStageLang.PerformanceChartProductionAxisLabel] + " (" + eggsUnitSymbol + ")" }
                });
            }
            // Dead axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[HenStageLang.PerformanceChartDeadAxisLabel] }
            });
            // Hen Amount axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[HenStageLang.PerformanceChartHenAmountAxisLabel] }
            });
            // Feed Intake axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = false,
                Title = new TitleDTO() { Text = this.localizer[HenStageLang.PerformanceChartFeedIntakeAxisLabel] + " (" + feedIntakeUnitSymbol + ")" },
                UnitSymbol = feedIntakeUnitSymbol
            });
            // Moratality and viability
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[HenStageLang.PerformanceChartMortalityAndViabilityAxisLabel] },
                UnitSymbol = "%"
            });

            // Horizontal axis
            // Generate date range from minDate to maxDate
            List<DateTime> dateTimeAxis = new List<DateTime>();
            int range = (maxDate.AddDays(1) - minDate).Days;
            if (range == 0)
                dateTimeAxis.Add(minDate);
            else
            {
                for (int i = 0; i < range; i++)
                {
                    dateTimeAxis.Add(minDate.AddDays(i));
                }
            }
            foreach (DateTime date in dateTimeAxis)
                dto.Categories.Add(date.ToShortDateString());

            #region Generate data for client side grouping
            // Initialize list of lists
            List<List<HenBatchStatusDTO>> series = new List<List<HenBatchStatusDTO>>();

            // Group by date
            List<IGrouping<DateTime, HenBatchStatusDTO>> groupedHenReportDTOs = henReportDTOs
                .GroupBy(hr => hr.DateStart).ToList();
            // build list
            foreach (DateTime date in dateTimeAxis)
            {
                // Extract data from IGrouping element
                List<HenBatchStatusDTO> group = groupedHenReportDTOs.Where(g => g.Key == date).SelectMany(hr => hr).ToList();
                // Initialize hen batch data series
                series.Add(new List<HenBatchStatusDTO>());
                // Add every hen batch data to that specific date
                foreach (HenBatchStatusDTO hr in group)
                    series.Last().Add(hr);
            }
            #endregion

            // Initialize data lists
            List<decimal> henAmount = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> henAmountFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> henAmountMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> deadFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> deadMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> mortalityFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> mortalityMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> viabilityFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> viabilityMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> feedIntakeFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> feedIntakeMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> hatchableEggs = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> commercialEggs = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> brokenEggs = new List<decimal>(new decimal[dateTimeAxis.Count()]);

            for (int i = 0; i < series.Count; i++)
            {
                henAmount[i] = series[i].Sum(hr => hr.HenAmount);
                henAmountFemale[i] = series[i].Sum(hr => hr.HenAmountFemale);
                henAmountMale[i] = series[i].Sum(hr => hr.HenAmountMale);
                deadFemale[i] = series[i].Sum(hr => hr.DeadFemale);
                deadMale[i] = series[i].Sum(hr => hr.DeadMale);
                mortalityFemale[i] = series[i].Sum(hr => hr.DeadFemale) + series[i].Sum(hr => hr.HenAmountFemale) != 0 ? decimal.Round((100 * series[i].Sum(hr => hr.DeadFemale)) / (series[i].Sum(hr => hr.DeadFemale) + series[i].Sum(hr => hr.HenAmountFemale)), 2) : 0;
                mortalityMale[i] = series[i].Sum(hr => hr.DeadMale) + series[i].Sum(hr => hr.HenAmountMale) != 0 ? decimal.Round((100 * series[i].Sum(hr => hr.DeadMale)) / (series[i].Sum(hr => hr.DeadMale) + series[i].Sum(hr => hr.HenAmountMale)), 2) : 0;
                viabilityFemale[i] = 100 - mortalityFemale[i];
                viabilityMale[i] = 100 - mortalityMale[i];
                feedIntakeFemale[i] = series[i].Sum(hr => hr.HenAmountFemale) != 0 ? series[i].Sum(hr => hr.FeedIntakeFemale) / series[i].Sum(hr => hr.HenAmountFemale) * 1000 : 0;
                feedIntakeMale[i] = series[i].Sum(hr => hr.HenAmountMale) != 0 ? series[i].Sum(hr => hr.FeedIntakeMale) / series[i].Sum(hr => hr.HenAmountMale) * 1000 : 0;
                hatchableEggs[i] = series[i].Sum(hr => hr.HatchableEggs);
                commercialEggs[i] = series[i].Sum(hr => hr.CommercialEggs);
                brokenEggs[i] = series[i].Sum(hr => hr.BrokenEggs);
            }

            if (!validHenStage || (validHenStage && henStage == HenStage.Laying))
            {
                dto.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[HenStageLang.PerformanceChartHatchableEggsSeriesName],
                    Data = hatchableEggs.Cast<decimal?>().ToList(),
                    Stack = "Eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    Type = "column",
                    ShowInLegend = true,
                    Visible = false,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggsUnitSymbol }
                });
                dto.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[HenStageLang.PerformanceChartCommercialEggsSeriesName],
                    Data = commercialEggs.Cast<decimal?>().ToList(),
                    Stack = "Eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    Type = "column",
                    Visible = false,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggsUnitSymbol }
                });
                dto.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[HenStageLang.PerformanceChartBrokenEggsSeriesName],
                    Data = brokenEggs.Cast<decimal?>().ToList(),
                    Stack = "Eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    Type = "column",
                    Visible = false,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggsUnitSymbol }
                });
            }

            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartMortalityFemaleSeriesName],
                Data = mortalityFemale.Cast<decimal?>().ToList(),
                Stack = "Mortality",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartViabilityFemaleSeriesName],
                Data = viabilityFemale.Cast<decimal?>().ToList(),
                Stack = "Viability",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartMortalityMaleSeriesName],
                Data = mortalityMale.Cast<decimal?>().ToList(),
                Stack = "Mortality",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartViabilityMaleSeriesName],
                Data = viabilityMale.Cast<decimal?>().ToList(),
                Stack = "Viability",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartDeadFemaleSeriesName],
                Data = deadFemale.Cast<decimal?>().ToList(),
                Stack = "Dead",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 1 : 0,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartDeadMaleSeriesName],
                Data = deadMale.Cast<decimal?>().ToList(),
                Stack = "Dead",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 1 : 0,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartHenAmountSeriesName],
                Data = henAmount.Cast<decimal?>().ToList(),
                Stack = "HenAmount",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 2 : 1,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartHenAmountFemaleSeriesName],
                Data = henAmountFemale.Cast<decimal?>().ToList(),
                Stack = "HenAmountFemale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 2 : 1,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartHenAmountMaleSeriesName],
                Data = henAmountMale.Cast<decimal?>().ToList(),
                Stack = "HenAmountMale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 2 : 1,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartFeedIntakeFemaleSeriesName],
                Data = feedIntakeFemale.Cast<decimal?>().ToList(),
                Stack = "FeedIntakeFemale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 3 : 2,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = feedIntakeUnitSymbol }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[HenStageLang.PerformanceChartFeedIntakeMaleSeriesName],
                Data = feedIntakeMale.Cast<decimal?>().ToList(),
                Stack = "FeedIntakeMale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 3 : 2,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = feedIntakeUnitSymbol }
            });

            // If all data is zero, clear lists so no available data message pops up
            if (dto.Series.All(s => s.Data.All(p => p == 0)))
                foreach (DecimalSeriesDTO data in dto.Series)
                    data.Data.Clear();

            return new Tuple<List<List<HenBatchStatusDTO>>, DashboardLineOrBarChartDTO>(series, dto);
        }
        #endregion
    }

    public class LayingDataPointDTO
    {
        public DateTime Date { get; set; }
        public int WeekNumber { get; set; }
        public decimal Value { get; set; }
        public string SeriesLabel { get; set; }
        public int AveragingWeight { get; set; }
        public Guid? ParentHenBatchId { get; set; }
        public string ParentHenBatchName { get; set; }
        public Guid? WarehouseId { get; set; }
        public string WarehouseName { get; set; }
        public Guid? LineId { get; set; }
        public string LineName { get; set; }
        public Guid? GeneticId { get; set; }
        public int HenAmountMale { get; set; }
        public int HenAmountFemale { get; set; }
        public HenBatchPerformance HenBatchPerformance { get; set; }
    }

    public class LayingChartPointDTO
    {
        public DateTime Date { get; set; }
        public int WeekNumber { get; set; }
        public decimal Value { get; set; }
    }

    public class LayingChartSeriesDTO
    {
        public string SeriesLabel { get; set; }
        public string UnitSymbol { get; set; } = "%";
        public string Color { get; set; }
        public string Type { get; set; } = "spline";
        public List<LayingChartPointDTO> Data { get; set; }
        public bool IsSecondaryAxis { get; set; } = false;

        public void FillEmptySpacesInBetweenWithLastValue()
        {
            if (Data == null || Data.Count == 0)
                return;

            int firstIndexWithValue = Data.FindIndex(d => d?.Value is decimal v && v != 0);
            int lastIndexWithValue = Data.FindLastIndex(d => d?.Value is decimal v && v != 0);

            if (firstIndexWithValue == -1 && lastIndexWithValue == -1 || firstIndexWithValue == lastIndexWithValue) return;

            decimal? lastValue = null;

            for (int i = firstIndexWithValue; i <= lastIndexWithValue; i++)
            {
                var data = Data[i];
                var isValid = data?.Value != null && data.Value != 0;

                if (!isValid && lastValue != null)
                    data.Value = (decimal)lastValue;

                if (isValid)
                    lastValue = data.Value;
            }
        }
    }

    public class LayingChartOptionsDTO
    {
        public string Title { get; set; }
        public string XAxisLabel { get; set; }
        public string YAxisLabel { get; set; }
        public int? YMin { get; set; } = null;
        public int? YMax { get; set; } = null;
        public int? YTickAmount { get; set; } = null;
        public int? YTickInterval { get; set; } = null;

        public int WeekStart { get; set; } = 22;
        public int WeekEnd { get; set; } = 70;
        public int TotalWeeks { get; set; } = 25;
    }

    public class LayingChart
    {
        DashboardLineOrBarChartDTO chart;
        int totalWeeks = 25;
        public int WeekOffset { get; set; } = 0;

        List<string> colors = new List<string>() { "#C042FB", "#2ECC71", "#F18A2E", "#2471A3", "#85C1E9", "#EDFB42", "#E74C3C", "#145A32", "#633974" };

        bool IsAutoRangeYAxis = true;

        public LayingChart(LayingChartOptionsDTO options)
        {
            chart = new DashboardLineOrBarChartDTO
            {
                Title = options.Title,
                XAxisLabel = options.XAxisLabel,
                XAxisGridLineWidth = 1,
                ToolTipShared = false,
                Series = new List<DecimalSeriesDTO>(),
                YAxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Min = options.YMin ?? int.MaxValue,
                        Max = options.YMax,
                        TickAmount = options.YTickAmount,
                        TickInterval = options.YTickInterval,
                        GridLineWidth = 1,
                        Opposite = false,
                        Title = new TitleDTO
                        {
                            Text = options.YAxisLabel
                        }
                    }
                }
            };

            IsAutoRangeYAxis = options.YMin == null;
            totalWeeks = options.TotalWeeks;
        }

        public void AddSeries(LayingChartSeriesDTO series)
        {
            List<decimal?> seriesValues = new List<decimal?>(new decimal?[totalWeeks]);

            foreach (var point in series.Data)
            {
                int index = point.WeekNumber - WeekOffset;
                if (index >= 0 && index < totalWeeks && point.Value != 0M)
                {
                    seriesValues[index] = decimal.Round(point.Value, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);
                }
            }

            this.chart.Series.Add(new DecimalSeriesDTO
            {
                Name = series.SeriesLabel,
                Type = series.Type,
                Data = seriesValues,
                SeriesUnitsSymbol = series.UnitSymbol,
                ToolTip = new ToolTipDTO() { ValueSuffix = series.UnitSymbol },
                ShowInLegend = true,
                YAxis = series.IsSecondaryAxis ? 1 : 0,
                Color = series.Color ?? colors[chart.Series.Count % colors.Count]
            });

            if (this.IsAutoRangeYAxis)
            {
                this.chart.YAxis[series.IsSecondaryAxis ? 1 : 0].Min = (int)Math.Min(seriesValues.Where(d => d != null).Min() ?? 0, (decimal)chart.YAxis[series.IsSecondaryAxis ? 1 : 0].Min);
            }
        }

        public void AddSecondaryYAxis(string label, int? min = null, int? max = null, int? tickAmount = null)
        {
            this.chart.YAxis.Add(new YAxis()
            {
                Min = min ?? 0,
                Max = max,
                TickAmount = tickAmount,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = label }
            });
        }

        public void AutoRangeYAxis()
        {
            if (!this.IsAutoRangeYAxis)
            {
                return;
            }

            foreach (var series in chart.Series)
            {
                var seriesMinimum = series.Data.Where(d => d != null).Min();
                this.chart.YAxis[0].Min = (int)Math.Min(seriesMinimum ?? 0, (decimal)chart.YAxis[0].Min);
                if (chart.YAxis.Count > 1)
                {
                    this.chart.YAxis[1].Min = (int)Math.Min(seriesMinimum ?? 0, (decimal)chart.YAxis[1].Min);
                }
            }
        }

        public void AddSeries(List<LayingChartSeriesDTO> series)
        {
            foreach (var s in series)
            {
                AddSeries(s);
            }
        }

        public DashboardLineOrBarChartDTO GetChart()
        {
            return chart;
        }

        private void SetCategories()
        {
            for (var week = WeekOffset; week <= chart.WeekEnd; week++)
                chart.Categories.Add(week.ToString());
        }

        public void SetChartWithWeekRange(int? weekStart, int? weekEnd)
        {
            chart.WeekStart = weekStart;
            chart.WeekEnd = weekEnd;

            WeekOffset = weekStart ?? 0;

            SetCategories();
        }

        public static LayingChart CreateWithWeekRange(LayingChartOptionsDTO options, int? weekStart, int? weekEnd)
        {
            var chart = new LayingChart(options);
            chart.SetChartWithWeekRange(weekStart, weekEnd);

            return chart;
        }

    }
    #endregion
}