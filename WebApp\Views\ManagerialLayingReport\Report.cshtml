@using Binit.Framework
@using Binit.Framework.Helpers
@using Domain.Entities.Model
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<SharedResources> localizer

@{
    var farms = ViewData["Farms"] as List<SelectListItem>;
    var parentHenBatches = ViewData["ParentHenBatches"] as List<SelectListItem>;
    var status = ViewData["HenBatchStatus"] as List<SelectListItem>;
}

<link href="~/css/handsontable.css" rel="stylesheet" />
<style>
    #hot-display-license-info {
        display: none;
    }

    .ht_clone_top {
        z-index: 1
    }

    .htDimmed {
        background-color: #ddd !important;
    }

    #report-table {
        width: 100%;
    }

    .header-yellow {
        background-color: #FFFF00 !important;
    }
    .header-blue {
        background-color: #d9e5f6 !important;
    }
    .header-orange {
        background-color: #fbd28b !important;
    }
    .header-green {
        background-color: #d6eadf !important;
    }
    .header-pink {
        background-color: #FBE3D6 !important;
    }

</style>

<div class="row">
    <div class="form-group col-md-3" style="display:@( farms?.Count() == 1 ? "none": "" )">
        <label for="farm">Fazenda</label>
        <select class="form-control select2" id="farm">
            <option value="">
                Selecione uma fazenda
            </option>
            @foreach (var item in farms)
            {
                if (farms.Count() == 1)
                {
                    <option selected value="@item.Value">@item.Text</option>
                }
                else
                {
                    <option value="@item.Value">@item.Text</option>
                }
            }
        </select>
    </div>

    <div class="form-group col-md-3">
        <label for="henBatch-status">Estado do lote</label>
        <select class="form-control select2" id="henbatch-status">
            @foreach (var item in status)
            {
                if (@item.Selected)
                {
                    <option selected value="@item.Value">@item.Text</option>

                }
                else
                {
                    <option value="@item.Value">@item.Text</option>
                }
            }
        </select>
    </div>


    <div class="form-group col-md-3">
        <label for="parentHenBatch">Lote</label>
        <select class="form-control select2" id="parentHenBatch">
            <option value="">Selecione um lote</option>
            @if (farms.Count() == 1)
            {
                foreach (var item in parentHenBatches)
                {
                    if (parentHenBatches.Count() == 1)
                    {
                        <option selected value="@item.Value">@item.Text</option>
                    }
                    else
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                }
            }
        </select>
    </div>

    <div class="justify-content-center align-items-center" style="display: flex">
        <button type="button" id="btn-filter" class="btn btn-primary m-l-5 m-b-0">
            <i class="fa fa-filter m-l-5"></i> Filtrar
        </button>
        <button type="button" id="btn-export-filtered" class="btn btn-primary excel m-l-5 m-b-0">
            <i class="fa fa-file-excel m-l-5"></i> Excel
        </button>
    </div>

    <div id="report-table"></div>
</div>

@section scripts {
    <script src="~/js/handsontable.js"></script>
    <script src="~/js/views/managerialLayingReport/report.js"></script>
}

<ignite-load plugins="select2"></ignite-load>