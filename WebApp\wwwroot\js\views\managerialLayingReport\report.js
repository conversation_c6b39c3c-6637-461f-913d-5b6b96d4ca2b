// function that repopulates the combo box for hen batches
const henStage =
  new URLSearchParams(window.location.search).get("henStage") || "Breeding";

const getActiveParameter = () => {
  switch ($("#henbatch-status").val()) {
    case "active":
      return true;
    case "closed":
      return false;
    default:
      return null;
  }
};

let refreshParentHenBatchesByFarm = async () => {
  let selectedFarm = $("#farm").val();
  $("#parentHenBatch option").not(":first").remove();
  $("#henBatch option").not(":first").remove();
  let active = getActiveParameter();

  if (henStage !== "" && selectedFarm !== "") {
    const parentHenBatches = await fetch(
      `${location.origin}/ManagerialLayingReport/GetParentHenBatches?selectedFarm=${selectedFarm}&active=${active}`
    ).then((res) => res.json());

    if (parentHenBatches.length > 0) {
      $.each(parentHenBatches, function (i, l) {
        $("#parentHenBatch").append(
          new Option(l.text, l.value, false, l.selected)
        );
      });
    } else {
      swal({
        type: "info",
        text: noHenBatches,
      });
    }
  }

  selectedFarm == "";
};

// on change methods for all combo boxes in the view
$("#farm").on("change", refreshParentHenBatchesByFarm);
$("#henbatch-status").on("change", refreshParentHenBatchesByFarm);

$("#btn-export-filtered").click(function () {
  const $button = $(this);
  const originalContent = $button.html();

  // Show loading state
  $button
    .prop("disabled", true)
    .html('<i class="fa fa-spinner fa-spin m-l-5"></i> Carregando...');

  // Use AJAX to handle the server response
  $.ajax({
    type: "POST",
    url: "/ManagerialLayingReport/ExcelExport",
    contentType: "application/json",
    data: JSON.stringify({
      farm: $("#farm").val(),
      parentHenBatch: $("#parentHenBatch").val(),
      active: getActiveParameter(),
      henStage: henStage,
    }),
    xhrFields: {
      responseType: "blob",
    },
    success: function (response) {
      const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "ManagerialLayingReport.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    },
    error: function (xhr, status, error) {
      Swal.fire({
        icon: "Error",
        title: "Esse lote não possui relatórios",
        text: "Por favor, tentar outro lote.",
      });
    },
    complete: function () {
      // Restore button state
      $button.prop("disabled", false).html(originalContent);
    },
  });
});

$("#btn-filter").click(function () {
  const $button = $(this);
  const originalContent = $button.html();

  // Show loading state
  $button
    .prop("disabled", true)
    .html('<i class="fa fa-spinner fa-spin m-l-5"></i> Carregando...');

  // Use AJAX to handle the server response
  $.ajax({
    type: "POST",
    url: "/ManagerialLayingReport/GetReportData",
    contentType: "application/json",
    data: JSON.stringify({
      farm: $("#farm").val(),
      parentHenBatch: $("#parentHenBatch").val(),
      active: getActiveParameter(),
      henStage: henStage,
    }),
    success: function (response) {
      console.log("report data", response);
      displayReportData(response);
    },
    error: function (xhr, status, error) {
      console.error(error);
      Swal.fire({
        icon: "Error",
        title: "Esse lote não possui relatórios",
        text: "Por favor, tentar outro lote.",
      });
    },
    complete: function () {
      // Restore button state
      $button.prop("disabled", false).html(originalContent);
    },
  });
});

let currentTable = null;

async function displayReportData(reportData) {
  clearReportTable();

  const groupNames = [
    "weeks",
    "henAmountMale",
    "henAmountFemale",
    "femaleViability",
    "femaleWeight",
    "femaleUniformity",
    "femaleGAD",
    "maleWeight",
    "maleUniformity",
    "maleGAD",
    "childBatchesSeries",
    "eggProduction",
    "bedEggProduction",
    "oifc",
    "hatchingPercentage",
    "fertileEggs",
    "maleFemaleRatio",
  ];

  // initialize table data with weeks
  const nestedHeaders = [
    [{ label: "" }],
    [{ label: `<strong>Idade Semanas</strong>` }],
  ];
  const tableData = reportData["weeks"].map((week) => [week]);

  groupNames.forEach((groupName) => {
    const dataGroup = reportData[groupName];

    const [headers, subheaders] = nestedHeaders;

    if (dataGroup.series?.length > 0) {
      // MULTIPLE SERIES IN GROUP

      // add main header
      headers.push({
        label: `<strong>${dataGroup.name}</strong>`,
        colspan: dataGroup.series.length,
      });
      dataGroup.series.forEach((series) => {
        // add subheader for each series
        subheaders.push({ label: `<strong>${series.name}</strong>` });

        // add content
        tableData.forEach(([week], rowIndex) => {
          const dataPoint = series.data.find((point) => point.week === week);
          if (dataPoint) {
            tableData[rowIndex].push(formatValue(series, dataPoint.value));
          } else {
            tableData[rowIndex].push("");
          }
        });
      });
    } else if (dataGroup.data) {
      // SINGLE SERIES IN GROUP

      // add main header and subheader
      headers.push({ label: "" });
      subheaders.push({ label: `<strong>${dataGroup.name}</strong>` });

      // add content
      tableData.forEach(([week], rowIndex) => {
        const dataPoint = dataGroup.data.find((point) => point.week === week);
        if (dataPoint) {
          tableData[rowIndex].push(formatValue(dataGroup, dataPoint.value));
        } else {
          tableData[rowIndex].push("");
        }
      });
    } else if (groupName === "childBatchesSeries") {
      // GROUP OF CHILD BATCHES

      dataGroup.forEach((childBatch) => {
        // add main header
        headers.push({
          label: `<strong>${childBatch.name}</strong>`,
          colspan: childBatch.series.length,
        });

        childBatch.series.forEach((series) => {
          // add subheader for each series
          subheaders.push({ label: `<strong>${series.name}</strong>` });

          // add content
          tableData.forEach(([week], rowIndex) => {
            const dataPoint = series.data.find((point) => point.week === week);
            if (dataPoint) {
              tableData[rowIndex].push(formatValue(series, dataPoint.value));
            } else {
              tableData[rowIndex].push("");
            }
          });
        });
      });
    }
  });

  const tableElement = $("#report-table").get(0);
  currentTable = new Handsontable(tableElement, {
    data: tableData,
    rowHeaders: false,
    nestedHeaders: nestedHeaders,
    filters: false,
    fillHandle: false, // disable drag copy
    dropdownMenu: false,
    colWidths: 100,
    manualColumnResize: true,
    manualRowResize: true,
    autoWrapRow: true,
    autoWrapCol: true,
    width: "100%",
    height: "500px",
    stretchH: "all",
    licenseKey: "non-commercial-and-evaluation",
    afterGetColHeader: function (col, TH) {
      if (col < 0) return;

      const aviaryColSpan = 6;
      const firstAviaryCol = 17;
      const aviariesClasses = [
        "header-blue",
        "header-orange",
        "header-green",
        "header-pink",
      ];
      const aviariesSubHeaders = [
        "Peso Padrão",
        "Peso Real",
        "Dif (%)",
        "Unif. F (%)",
        "GAD Padrão",
        "GAD Real",
      ];

      if (col < firstAviaryCol) {
        TH.classList.add("header-yellow");
      } else if (
        TH.colSpan === aviaryColSpan &&
        !TH.classList.contains("hiddenHeader")
      ) {
        const aviaryIndex = Math.floor((col - firstAviaryCol) / aviaryColSpan);
        const colorClass =
          aviariesClasses[aviaryIndex % aviariesClasses.length];
        TH.classList.add(colorClass);
      } else if (
        !TH.hasAttribute("colspan") &&
        aviariesSubHeaders.includes(TH.textContent.trim())
      ) {
        const aviaryIndex = Math.floor((col - firstAviaryCol) / aviaryColSpan);
        const colorClass =
          aviariesClasses[aviaryIndex % aviariesClasses.length];
        TH.classList.add(colorClass);
      }
    },
  });
}

function clearReportTable() {
  if (currentTable && !currentTable.isDestroyed) {
    currentTable.destroy();
    currentTable = null;
  }
}

function formatValue(series, value) {
  if (series.decimalPlaces) {
    return value.toFixed(series.decimalPlaces);
  }

  return value;
}
