// Tooltip formatter to show the percentage that represents the difference between the real viability and STD
var dashboardViabilityChartToolTipFormatter = function () {
  // Procura o gráfico cujo ID seja "ViabilityFemale"
  var viabilityChart = Highcharts.charts.find(
    (obj) => obj !== undefined && obj.renderTo?.id == "ViabilityFemale"
  );

  // Define o cabeçalho do tooltip
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ":</b><br/>";

  // Se o gráfico não for encontrado, use um tooltip simples
  if (!viabilityChart) {
    var fallbackBody =
      '<span style="color:' +
      this.color +
      '">\u25CF</span> ' +
      this.series.name +
      ': <span style="font-weight:bold">' +
      this.series.chart.numberFormatter(this.y, 2) +
      " " +
      (this.point.series.userOptions.toolTip?.valueSuffix || "") +
      "</span><br/>";
    return header + fallbackBody;
  }

  // Obter as séries do gráfico
  var viabilityChartSeries = viabilityChart.series;

  var body = "";

  // Itera pelas séries (ou pontos) para compor o corpo do tooltip
  for (var i = 0; i < viabilityChartSeries.length; i++) {
    // Verifica se o ponto da série na posição atual tem um valor diferente de zero
    var pointY =
      viabilityChartSeries[i].points &&
      viabilityChartSeries[i].points[this.point.x]
        ? viabilityChartSeries[i].points[this.point.x].y
        : 0;
    if (pointY !== 0 && this.series.index === i) {
      let viableFemaleSuffix = "";
      const currentNumberFormatter = this.series.chart.numberFormatter;

      // Obtém o valor real e o valor STD dos pontos da primeira e segunda série
      var viableFemaleReal = viabilityChartSeries[0].points[this.point.x].y;
      var viableFemaleSTD = viabilityChartSeries[1].points[this.point.x].y;

      // Calcula a diferença percentual (invertendo o sinal)
      var percentageDifference = (viableFemaleSTD - viableFemaleReal) * -1;

      // Define a cor de acordo com se a diferença é negativa ou positiva
      let viabilityColor = "#17202A"; // preto como padrão
      if (i !== 1) {
        if (percentageDifference < 0) {
          viabilityColor = "#E74C3C"; // vermelho se menor
          viableFemaleSuffix =
            " (" + currentNumberFormatter(percentageDifference) + "%)";
        } else {
          viabilityColor = "#28B463"; // verde se maior ou igual
          viableFemaleSuffix =
            " (+" + currentNumberFormatter(percentageDifference) + "%)";
        }
      }

      // Constrói o corpo para a série atual
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        viabilityColor +
        ';font-weight:bold">' +
        currentNumberFormatter(this.y, 2) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        viableFemaleSuffix +
        "</span><br/>";
    }
  }

  // Retorna o cabeçalho concatenado com o corpo do tooltip
  return header + body;
};

const dashboardViabilityChartPlotOptions = {
  spline: {
    dashStyle: "Solid",
  },
  series: {
    dashStyle: "ShortDot",
    lineWidth: 2,
    shadow: true,
    marker: {
      enabled: false,
      symbol: "circle",
      states: {
        hover: {
          enabled: true,
        },
      },
    },
  },
};

var dashboardSaleableChicksChartPlotOptions = {
  series: {
    dataLabels: {
      enabled: false,
    },
    marker: {
      enabled: true,
      radius: 1,
      states: {
        hover: {
          enabled: true,
          radius: 4,
        },
      },
    },
  },
};

var dashboardLayingChartToolTipFormatter = function () {
  const chart = Highcharts.charts.find(
    (obj) =>
      obj !== undefined && obj.renderTo.id == "LayingHatcheringAndFertility"
  );
  const chartSeries = chart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  var body = "";
  let currentIndex = this.series.index;
  let percentageColor = "#17202A"; // black
  const currentNumberFormatter = this.point.series.chart.numberFormatter;

  if (this.y !== null && this.y !== undefined) {
    if (currentIndex < 3) {
      let suffix = "";

      let valueReal = chartSeries[currentIndex].points[this.point.x].y;
      let valueSTD = chartSeries[currentIndex + 3].points[this.point.x].y;
      var percentageDifference = (valueSTD - valueReal) * -1;

      if (percentageDifference < 0) {
        percentageColor = "#E74C3C"; // red
        suffix = " (" + currentNumberFormatter(percentageDifference) + "%)";
      } else {
        percentageColor = "#28B463"; // green
        suffix = " (+" + currentNumberFormatter(percentageDifference) + "%)";
      }
      // values are rounded to no decimals
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, 2) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        suffix +
        "</span><br/>";
    } else {
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, 2) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        "</span><br/>";
    }
  }

  // Append body to header and return
  return header + body;
};

// this toolTipFormates is used by EggPerformance and EggContaminated
var dashboardEggPerformanceChartToolTipFormatter = function () {
  const chart = Highcharts.charts.find(
    (obj) =>
      obj !== undefined && obj.renderTo.id == this.series.chart.renderTo.id
  );
  const chartSeries = chart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  var body = "";
  let currentIndex = this.series.index;
  let percentageColor = "#17202A"; // black
  const currentNumberFormatter = this.point.series.chart.numberFormatter;
  let decimalPrecision =
    this.series.chart.renderTo.id == "ContaminatedEggs" ? 4 : 2;

  if (this.y !== null && this.y !== undefined) {
    if (currentIndex == 0) {
      let suffix = "";

      let valueReal = chartSeries[currentIndex].points[this.point.x].y;
      let valueSTD = chartSeries[currentIndex + 1].points[this.point.x].y;
      var percentageDifference = (valueSTD - valueReal) * -1;

      if (percentageDifference < 0) {
        percentageColor = "#E74C3C"; // red
        suffix = " (" + currentNumberFormatter(percentageDifference) + ")";
      } else {
        percentageColor = "#28B463"; // green
        suffix = " (+" + currentNumberFormatter(percentageDifference) + ")";
      }
      // values are rounded to no decimals
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        '(%): <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, decimalPrecision) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        suffix +
        "</span><br/>";
    } else {
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, decimalPrecision) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        "</span><br/>";
    }
  }

  // Append body to header and return
  return header + body;
};

var dashboardOIFCChartToolTipFormatter = function () {
  const chart = Highcharts.charts.find(
    (obj) => obj !== undefined && obj.renderTo.id == "OIFCChart"
  );
  const chartSeries = chart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  var body = "";
  let currentIndex = this.series.index;
  let percentageColor = "#17202A"; // black
  const currentNumberFormatter = this.point.series.chart.numberFormatter;

  if (this.y !== null && this.y !== undefined) {
    if (currentIndex == 0) {
      let suffix = "";

      let valueReal = chartSeries[currentIndex].points[this.point.x].y;
      let valueSTD = chartSeries[currentIndex + 1].points[this.point.x].y;
      var percentageDifference = (valueSTD - valueReal) * -1;

      if (percentageDifference < 0) {
        percentageColor = "#E74C3C"; // red
        suffix = " (" + currentNumberFormatter(percentageDifference) + ")";
      } else {
        percentageColor = "#28B463"; // green
        suffix = " (+" + currentNumberFormatter(percentageDifference) + ")";
      }
      // values are rounded to no decimals
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, 2) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        suffix +
        "</span><br/>";

      // calculated iep suffix
      let iepValue = valueSTD == 0 ? 100 : (100 * valueReal) / valueSTD;

      if (iepValue < 100) percentageColor = "#E74C3C"; // red
      else percentageColor = "#28B463"; // green

      let iepBody =
        '<span style="color:' +
        this.color +
        '">\u25CF</span> IEP (%): <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        currentNumberFormatter(iepValue) +
        " %</span><br/>";

      body += iepBody;
    } else {
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, 2) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        "</span><br/>";
    }
  }

  // Append body to header and return
  return header + body;
};

var dashboardSaleableChicksChartToolTipFormatter = function () {
  var header = "<b>Semana " + this.x + ":</b><br/>";
  var body =
    '<span style="color:' +
    this.color +
    '">\u25CF</span> ' +
    this.series.name +
    ": <b>" +
    Highcharts.numberFormat(this.y, 2) +
    "</b>";
  var unit =
    this.point.series.userOptions.toolTip &&
    this.point.series.userOptions.toolTip.valueSuffix
      ? this.point.series.userOptions.toolTip.valueSuffix
      : "";
  return header + body + " " + unit;
};

var dashboardManagerialOIFCChartToolTipFormatter = function () {
  const currentChart = this.series.chart;

  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  // Build body of tooltip
  var body = "";

  // Show the current point value
  body +=
    '<span style="color:' +
    this.color +
    '">\u25CF</span> ' +
    this.series.name +
    ': <span style="font-weight:bold">' +
    currentChart.numberFormatter(this.y, 2) +
    " " +
    (this.point.series.userOptions.toolTip?.valueSuffix || "") +
    "</span><br/>";

  return header + body;
};

// this toolTipFormates is used by EggQuality
var dashboardEggQualityChartToolTipFormatter = function () {
  const chart = Highcharts.charts.find(
    (obj) =>
      obj !== undefined && obj.renderTo.id == this.series.chart.renderTo.id
  );
  const chartSeries = chart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  var body = "";
  let currentIndex = this.series.index;
  let percentageColor = "#17202A"; // black
  const currentNumberFormatter = this.point.series.chart.numberFormatter;
  let decimalPrecision = 2;

  if (this.y !== null && this.y !== undefined) {
    if (currentIndex % 2 == 0) {
      let suffix = "";

      let valueReal = chartSeries[currentIndex].points[this.point.x].y;
      let valueSTD = chartSeries[currentIndex + 1].points[this.point.x].y;
      var percentageDifference = (valueSTD - valueReal) * -1;

      if (percentageDifference < 0) {
        percentageColor = "#E74C3C"; // red
        suffix = " (" + currentNumberFormatter(percentageDifference) + ")";
      } else {
        percentageColor = "#28B463"; // green
        suffix = " (+" + currentNumberFormatter(percentageDifference) + ")";
      }
      // values are rounded to no decimals
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        '(%): <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, decimalPrecision) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        suffix +
        "</span><br/>";
    } else {
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        ': <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, decimalPrecision) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        "</span><br/>";
    }
  }

  // Append body to header and return
  return header + body;
};

// this toolTipFormates is used by EggDensity
var dashboardEggDensityChartToolTipFormatter = function () {
  const chart = Highcharts.charts.find(
    (obj) =>
      obj !== undefined && obj.renderTo.id == this.series.chart.renderTo.id
  );
  const chartSeries = chart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  var body = "";
  let currentIndex = this.series.index;
  let percentageColor = "#17202A"; // black
  const currentNumberFormatter = this.point.series.chart.numberFormatter;
  let decimalPrecision = 2;
  let amountChartSeries = chartSeries.length / 2;
  if (this.y !== null && this.y !== undefined) {
    if (currentIndex < amountChartSeries) {
      let suffix = "";

      let valueReal = chartSeries[currentIndex].points[this.point.x].y;
      let valueSTD =
        chartSeries[currentIndex + amountChartSeries].points[this.point.x].y;
      var percentageDifference = (valueSTD - valueReal) * -1;

      if (percentageDifference < 0) {
        suffix = " (" + currentNumberFormatter(percentageDifference) + ")";
      } else {
        suffix = " (+" + currentNumberFormatter(percentageDifference) + ")";
      }
      // values are rounded to no decimals
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        '(%): <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, decimalPrecision) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        suffix +
        "</span><br/>";
    } else {
      body +=
        '<span style="color:' +
        this.color +
        '">\u25CF</span> ' +
        this.series.name +
        '(%): <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        this.series.chart.numberFormatter(this.y, decimalPrecision) +
        " " +
        this.point.series.userOptions.toolTip.valueSuffix +
        "</span><br/>";
    }
  }

  // Append body to header and return
  return header + body;
};
