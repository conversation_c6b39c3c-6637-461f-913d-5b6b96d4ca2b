using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.Interfaces.ExceptionHandling;
using DAL.Interfaces;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.BreedingChartsBusinessLogic;
using HenStageLang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.HenStageChartsBusinessLogic;
using Binit.Framework.ExceptionHandling.Types;
using Domain.Logic.BusinessLogic.DTOs.Genetic;

namespace Domain.Logic.BusinessLogic.ChartsBusinessLogic
{
    public class SearaBreedingChartsBusinessLogic : ISearaBreedingChartsBusinessLogic
    {
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IExceptionManager exceptionManager;
        private readonly IUnitOfWork unitOfWork;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IHenBatchService henBatchService;

        private const int DefaultTotalWeeks = 24;

        public SearaBreedingChartsBusinessLogic(
            IStringLocalizer<SharedResources> localizer,
            IExceptionManager exceptionManager,
            IUnitOfWork unitOfWork,
            ICapacityUnitService capacityUnitService,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IHenBatchService henBatchService)
        {
            this.localizer = localizer;
            this.exceptionManager = exceptionManager;
            this.unitOfWork = unitOfWork;
            this.capacityUnitService = capacityUnitService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.henBatchService = henBatchService;
        }
        #region Colors
        public const string redColor = "#E74C3C";
        public const string blueColor = "#2471A3";
        public const string greenColor = "#2ECC71";
        public const string lightblueColor = "#85C1E9";
        public const string orangeColor = "#F18A2E";
        public const string yellowColor = "#EDFB42";
        public const string violetColor = "#C042FB";
        public const string uniformitySTDColor = "#145A32"; // dark green
        public const string cvSTDColor = "#633974"; // violet
        public const string femaleSeriesColor = redColor;
        public const string maleSeriesColor = blueColor;
        #endregion

        #region Seara Breeding Charts
        public DashboardLineOrBarChartDTO GetSearaBreedingViabilityChart(FilterDataDTO filters = null)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);
            
            var henBatchPerformancesQuery = GetSearaBreedingPerformances(filters);
            var henBatchParentId = henBatchPerformancesQuery.FirstOrDefault(q => q.ParentId == null)?.HenBatchId;

            var viabilityData = henBatchPerformancesQuery
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = hbp.HenBatchLifeTimeFemaleViability,
                    HenBatchId = hbp.HenBatchId,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = hbp.HenAmountFemale,
                })
                .ToList();

            BreedingChart chart = new BreedingChart(new BreedingChartOptionsDTO
            {
                Title = this.localizer[Lang.ViabilityChartTitle],
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.ViabilityAxis] + " (%)",
                YMin = 94,
                YMax = 100,
                YTickAmount = 13,
                TotalWeeks = filters.TotalWeeks,
            });

            chart.SetChartWithWeekRange(filters.StartWeek, filters.EndWeek);

            if (viabilityData.Any())
            {
                AddStandardSeries(
                    chart, 
                    viabilityData.First().GeneticId.Value, 
                    localizer[Lang.ViableFemaleSTD], 
                    femaleSeriesColor, 
                    p => p.ViableFemalesPercentage
                );

                AddGroupedSeriesToChart(
                    filters, 
                    chart, 
                    viabilityData, 
                    localizer[Lang.ViableFemaleReal], 
                    "%",
                    group => CalculateViabilityWithParentLogic(group, henBatchParentId)
                );
            }

            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetDataForBreedingBirdWeightChart(FilterDataDTO filters = null)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);
            
            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";

            CapacityUnit birdWeightCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Grams);
            var conversionFactor = capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, 1.0M);

            var henBatchPerformancesQuery = GetSearaBreedingPerformances(filters);

            var weightData = henBatchPerformancesQuery
                .Where(hbp => isBoth || (isMale ? hbp.HenAmountMale > 0 : hbp.HenAmountFemale > 0))
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = decimal.Round((isMale ? (hbp.AvgMaleBirdWeight ?? 0M) : (hbp.AvgFemaleBirdWeight ?? 0M)) * conversionFactor, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven),
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.HenAmountMale : hbp.HenAmountFemale,
                })
                .ToList();

            if (isBoth)
            {
                var femaleWeightData = henBatchPerformancesQuery
                    .Where(hbp => hbp.HenAmountFemale > 0)
                    .Select(hbp => new BreedingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = decimal.Round((hbp.AvgFemaleBirdWeight ?? 0M) * conversionFactor, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven),
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name + " (F)",
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name + " (F)",
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.HenAmountFemale,
                    })
                    .ToList();

                weightData.AddRange(femaleWeightData);
            }


            BreedingChart chart = new BreedingChart(new BreedingChartOptionsDTO
            {
                Title = this.localizer[HenStageLang.BirdWeightChartTitle] + (isMale ? " (machos)" : " (fêmeas)"),
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.WeightAxis] + $" ({birdWeightCapacityUnit.Symbol})",
                YMin = 0,
                YMax = 3400,
                YTickAmount = 18,
                TotalWeeks = filters.TotalWeeks
            });
            
            chart.SetChartWithWeekRange(filters.StartWeek, filters.EndWeek);

            AddStandardSeries(
                chart, 
                weightData.First().GeneticId.Value, 
                localizer[Lang.WeightAxis] + " STD", 
                isMale ? maleSeriesColor : femaleSeriesColor, 
                p => (isMale ? p.WeightMale : p.WeightFemale) * conversionFactor, 
                birdWeightCapacityUnit.Symbol
            );

            AddGroupedSeriesToChart(filters, chart, weightData, this.localizer[Lang.WeightAxis], birdWeightCapacityUnit.Symbol, (IGrouping<DateTime, BreedingDataPointDTO> dataPoints) => CalculateSimpleAverage(dataPoints));

            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetSearaBreedingBirdWeightGainChart(FilterDataDTO filters = null)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);

            filters.StartWeek = filters.StartWeek > 2 ? filters.StartWeek - 1 : 1;

            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";

            var birdWeightCapacityUnit = capacityUnitService.Get(CapacityUnits.Grams);
            var conversionFactor = capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, 1.0M);

            var henBatchPerformancesQuery = GetSearaBreedingPerformances(filters);
            var weightData = henBatchPerformancesQuery
                .Where(hbp => isBoth || (isMale ? hbp.AvgMaleBirdWeight > 0 : hbp.AvgFemaleBirdWeight > 0))
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = decimal.Round(((isMale ? hbp.AvgMaleBirdWeight : hbp.AvgFemaleBirdWeight) ?? 0M) * conversionFactor, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven),
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.HenAmountMale : hbp.HenAmountFemale,
                }).ToList();

            if (isBoth)
            {
                var femaleWeightData = henBatchPerformancesQuery
                    .Where(hbp => hbp.AvgFemaleBirdWeight > 0)
                    .Select(hbp => new BreedingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = decimal.Round((hbp.AvgFemaleBirdWeight ?? 0M) * conversionFactor, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven),
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name + " (F)",
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name + " (F)",
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.HenAmountFemale,
                    })
                    .ToList();

                weightData.AddRange(femaleWeightData);
            }

            BreedingChart chart = new BreedingChart(new BreedingChartOptionsDTO
            {
                Title = "Ganho de peso" + (isBoth ? " (machos e fêmeas)" : (isMale ? " (machos)" : " (fêmeas)")),
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.WeightAxis] + " (g)",
                YMin = 0,
                YMax = 300,
                YTickAmount = 7,
                TotalWeeks = filters.TotalWeeks - 1,
            });
            
            filters.StartWeek += 1;
            
            chart.SetChartWithWeekRange(filters.StartWeek, filters.EndWeek);

            AddStandardSeries(
                chart, 
                weightData.First().GeneticId.Value, 
                "Ganho de peso STD", 
                isMale ? maleSeriesColor : femaleSeriesColor, 
                p => (isMale ? p.WeightMale : p.WeightFemale) * conversionFactor, 
                "g"
            );

            AddGroupedSeriesToChart(filters, chart, weightData, "Ganho de peso", "g", (IGrouping<DateTime, BreedingDataPointDTO> dataPoints) => CalculateSimpleAverage(dataPoints));

            foreach (var series in chart.GetChart().Series)
            {
                var seriesValues = series.Data;
                var gainsSeriesValues = new List<decimal?>(new decimal?[seriesValues.Count]);

                for (int i = 1; i < seriesValues.Count; i++)
                {
                    gainsSeriesValues[i - 1] = (seriesValues[i] - seriesValues[i - 1]) ?? 0M;
                }
            
                series.Data = gainsSeriesValues.Select(d => d == 0M ? null : d).ToList();
            }
            
            chart.AutoRangeYAxis();
            
            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetSearaBreedingFeedIntakeGADChart(FilterDataDTO filters = null)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);
            
            var henBatchPerformancesQuery = GetSearaBreedingPerformances(filters);

            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";

            if (!henBatchPerformancesQuery.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(localizer["Não há dados de desempenho para o lote de aves."]));
            }

            var feedIntakeData = henBatchPerformancesQuery
                .Where(hbp => isMale ? hbp.HenAmountMale > 0 : hbp.HenAmountFemale > 0)
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = isMale ?
                            (hbp.HenAmountMale > 0 ? hbp.FeedIntakeMale * 1000M / hbp.HenAmountMale / 7M : 0) :
                            (hbp.HenAmountFemale > 0 ? hbp.FeedIntakeFemale * 1000M / hbp.HenAmountFemale / 7M : 0),
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + (isBoth ? (isMale ? " (M)" : " (F)") : ""),
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.HenAmountMale : hbp.HenAmountFemale,
                })
                .ToList();

            if (isBoth)
            {
                var femaleFeedIntakeData = henBatchPerformancesQuery
                    .Where(hbp => hbp.HenAmountFemale > 0)
                    .Select(hbp => new BreedingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = hbp.FeedIntakeFemale * 1000M / hbp.HenAmountFemale / 7M,
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name,
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.HenAmountFemale,
                    })
                    .ToList();

                if (isMale)
                {
                    feedIntakeData.AddRange(femaleFeedIntakeData);
                }
                else if (!feedIntakeData.Any())
                {
                    feedIntakeData = henBatchPerformancesQuery
                        .Where(hbp => hbp.HenAmountMale > 0)
                        .Select(hbp => new BreedingDataPointDTO
                        {
                            Date = hbp.Date,
                            WeekNumber = hbp.WeekNumber,
                            Value = hbp.FeedIntakeMale * 1000M / hbp.HenAmountMale / 7M,
                            ParentHenBatchId = hbp.HenBatch.ParentId,
                            ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (M)",
                            WarehouseId = hbp.HenBatch.Line.WarehouseId,
                            WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                            LineId = hbp.HenBatch.Line.Id,
                            LineName = hbp.HenBatch.Line.Name,
                            GeneticId = hbp.HenBatch.GeneticId,
                            AveragingWeight = hbp.HenAmountMale,
                        })
                        .ToList();
                }
            }

            if (!feedIntakeData.Any())
            {
                throw exceptionManager.Handle(new NotFoundException(localizer["Não há dados de consumo de ração para o lote de aves."]));
            }

            BreedingChart chart = new BreedingChart(new BreedingChartOptionsDTO
            {
                Title = this.localizer[Lang.FeedIntakeChartTitle] + (isBoth ? " (machos e fêmeas)" : (isMale ? " (machos)" : " (fêmeas)")),
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = this.localizer[Lang.FeedIntakeAxis],
                YMin = 0,
                YMax = 130,
                YTickAmount = 14,
                TotalWeeks = filters.TotalWeeks,
            });
            
            chart.SetChartWithWeekRange(filters.StartWeek, filters.EndWeek);

            if (feedIntakeData.Any() && feedIntakeData.First().GeneticId.HasValue)
            {
                AddStandardSeries(
                    chart, 
                    feedIntakeData.First().GeneticId.Value,
                    isBoth ? localizer[Lang.GADMaleSTD] + " / " + localizer[Lang.GADFemaleSTD] :
                    (isMale ? this.localizer[Lang.GADMaleSTD] : this.localizer[Lang.GADFemaleSTD]),
                    isMale ? maleSeriesColor : femaleSeriesColor,
                    p => isMale ? (p.FeedIntakeMale * 1000M / 7M) : (p.FeedIntakeFemale * 1000M / 7M),
                    "g"
                );
            }

            AddGroupedSeriesToChart(filters, chart, feedIntakeData,
                isBoth ? this.localizer[Lang.GADMaleReal] + " / " + this.localizer[Lang.GADFemaleReal] :
                (isMale ? this.localizer[Lang.GADMaleReal] : this.localizer[Lang.GADFemaleReal]),
                "g", (IGrouping<DateTime, BreedingDataPointDTO> dataPoints) => CalculateSimpleAverage(dataPoints));
            
            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetSearaMaleFemaleChart(FilterDataDTO filters = null)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);
            
            var henBatchPerformancesQuery = GetSearaBreedingPerformances(filters);

            var maleFemaleData = henBatchPerformancesQuery
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    HenAmountMale = hbp.HenAmountMale,
                    HenAmountFemale = hbp.HenAmountFemale,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                })
                .ToList();

            BreedingChart chart = new BreedingChart(new BreedingChartOptionsDTO
            {
                Title = "Relação M/F",
                XAxisLabel = this.localizer[Lang.WeekXAxisLabel],
                YAxisLabel = "Machos/Fêmeas (%)",
                YMin = 9,
                YMax = 26,
                YTickAmount = 10,
                TotalWeeks = filters.TotalWeeks,
            });
            
            chart.SetChartWithWeekRange(filters.StartWeek, filters.EndWeek);

            AddGroupedSeriesToChart(filters, chart, maleFemaleData, "Machos/Fêmeas (%)", "%", CalculateMaleFemaleRatio);

            AddStandardSeries(
                chart, 
                maleFemaleData.First().GeneticId.Value, 
                "Machos/Fêmeas STD (%)", 
                greenColor, 
                p => p.MaleFemaleRatio
            );

            return chart.GetChart();
        }

        private decimal CalculateMaleFemaleRatio(IGrouping<DateTime, BreedingDataPointDTO> dataPoints)
        {
            var maleCount = dataPoints.Sum(point => point.HenAmountMale);
            var femaleCount = dataPoints.Sum(point => point.HenAmountFemale);
            return femaleCount == 0 ? 0 : (100M * maleCount / femaleCount);
        }

        private decimal CalculateSimpleAverage(IGrouping<DateTime, BreedingDataPointDTO> dataPoints)
        {
            var validDataPoints = dataPoints.Where(point => point.Value != 0).ToList();
            if (!validDataPoints.Any())
                return 0M;

            var sum = validDataPoints.Sum(point => point.Value);
            var count = validDataPoints.Count;
            return Math.Round(sum / count, 2);
        }

        public GeneticGraphicResponseDTO GetGeneticChartData(FilterDataDTO filters)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);
            
            var henBatchId = filters.HenBatchId;

            if (henBatchId == null)
                throw exceptionManager.Handle(new NotFoundException("É necessário selecionar um lote"));

            // Get genetic id from hen batch
            var geneticId = henBatchService.GetAll()
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => hb.GeneticId)
                .Distinct()
                .FirstOrDefault();

            if (geneticId == null)
                throw exceptionManager.Handle(new NotFoundException("Não há dados para exibir"));

            // Get genetics parameters by genetic id
            var geneticsParametersList = geneticsParameterService.GetAll()
                .Where(gp => gp.GeneticsId == geneticId)
                .Where(gp => gp.HenStage == HenStage.Breeding)
                .Where(gp => gp.TimePeriodValue >= filters.StartWeek && gp.TimePeriodValue <= filters.EndWeek)
                .Include(gp => gp.Genetics)
                .OrderBy(gp => gp.TimePeriodValue)
                .ToList();

            // Get hen batch performances
            var henBatchPerformances = henBatchPerformanceService.GetAll();

            if (filters.LineId != null)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatch.ParentId == henBatchId)
                    .Where(hbp => hbp.HenBatch.LineId == filters.LineId);
            }
            else if (filters.WarehouseId != null)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatch.ParentId == henBatchId)
                    .Where(hbp => hbp.HenBatch.Line.WarehouseId == filters.WarehouseId);
            }
            else
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.HenBatchId == henBatchId);
            }
            if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
            {
                henBatchPerformances = henBatchPerformances
                    .Where(hbp => hbp.WeekNumber >= filters.StartWeek.Value &&
                                  hbp.WeekNumber <= filters.EndWeek.Value);
            }

            var henBatchPerformancesList = henBatchPerformances
                .Include(hbp => hbp.HenBatch)
                .OrderBy(hbp => hbp.WeekNumber)
                .ToList();

            // Set period from Genetic Parameters
            int firstWeek = geneticsParametersList.First().TimePeriodValue;
            int lastWeek = geneticsParametersList.Last().TimePeriodValue;

            //Initialize DTO
            GeneticGraphicResponseDTO geneticGraphicDTOs = new GeneticGraphicResponseDTO()
            {
                HenStage = HenStage.Breeding,
                Data = new List<GeneticGraphicDTO>(),
                TotalWeeks = filters.TotalWeeks,
            };

            //Build graphic data -> one DTO per week. 
            for (int week = firstWeek; week <= lastWeek; week++)
            {
                GeneticGraphicDTO dto = new GeneticGraphicDTO { WeekNumber = week };

                //Set current values
                //Mortality is accumulative.
                HenBatchPerformance hbp = henBatchPerformancesList.Where(h => h.WeekNumber == week).FirstOrDefault();
                if (hbp != null)
                {
                    dto.BodyWeightFemale = hbp.AvgFemaleBirdWeight != null ? Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, hbp.AvgFemaleBirdWeight.Value)) : 0;
                    dto.BodyWeightMale = hbp.AvgMaleBirdWeight != null ? Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, hbp.AvgMaleBirdWeight.Value)) : 0;
                    dto.MortalityFemale = hbp.HenBatch.InitialHenAmountFemale != 0 ? Math.Round(((double)hbp.DeadFemale / (double)hbp.HenBatch.InitialHenAmountFemale) * 100, 2) : 0;
                    dto.MaxTemp = Decimal.ToDouble(hbp.MaxTemp);
                    dto.MinTemp = Decimal.ToDouble(hbp.MinTemp);
                }

                //STD: standard -> genetic parameters reference
                //Set standard values
                GeneticsParametersReference gpr = geneticsParametersList.Where(g => g.TimePeriodValue == week).FirstOrDefault();
                if (gpr != null)
                {
                    dto.BodyWeightSTDMaxFemale = Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, gpr.WeightFemale));
                    dto.BodyWeightSTDMaxMale = Decimal.ToDouble(capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Kilograms, CapacityUnits.Grams, gpr.WeightMale));
                    dto.MortalitySTDFemale = Math.Round((double)gpr.MortalityFemaleWeek, 2);
                }

                geneticGraphicDTOs.Data.Add(dto);
            }
            
            return geneticGraphicDTOs;
        }

        public DashboardLineOrBarChartDTO GetBreedingCVUniformityChart(FilterDataDTO filters = null)
        {
            filters = FilterDataDTO.OfDefaultFilter(filters,  DefaultTotalWeeks);
            
            var henBatchPerformancesQuery = GetSearaBreedingPerformances(filters);

            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";

            var uniformityData = henBatchPerformancesQuery
                .Where(hbp => isBoth || (isMale ? hbp.HenAmountMale > 0 : hbp.HenAmountFemale > 0))
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = isMale ? hbp.AvgUniformityMale : hbp.AvgUniformityFemale,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.HenAmountMale : hbp.HenAmountFemale,
                })
                .ToList();

            if (isBoth && isMale)
            {
                var femaleUniformityData = henBatchPerformancesQuery
                    .Where(hbp => hbp.HenAmountFemale > 0)
                    .Select(hbp => new BreedingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = hbp.AvgUniformityFemale,
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name + " (F)",
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name + " (F)",
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.HenAmountFemale,
                    })
                    .ToList();

                uniformityData.AddRange(femaleUniformityData);
            }

            var varianceCoefficientData = henBatchPerformancesQuery
                .Where(hbp => isBoth || (isMale ? hbp.HenAmountMale > 0 : hbp.HenAmountFemale > 0))
                .Select(hbp => new BreedingDataPointDTO
                {
                    Date = hbp.Date,
                    WeekNumber = hbp.WeekNumber,
                    Value = isMale ? hbp.AvgVariationCoefficientMale : hbp.AvgVariationCoefficientFemale,
                    ParentHenBatchId = hbp.HenBatch.ParentId,
                    ParentHenBatchName = hbp.HenBatch.Parent.DetailedName,
                    WarehouseId = hbp.HenBatch.Line.WarehouseId,
                    WarehouseName = hbp.HenBatch.Line.Warehouse.Name,
                    LineId = hbp.HenBatch.Line.Id,
                    LineName = hbp.HenBatch.Line.Name,
                    GeneticId = hbp.HenBatch.GeneticId,
                    AveragingWeight = isMale ? hbp.HenAmountMale : hbp.HenAmountFemale,
                })
                .ToList();

            // For "Both" option, add female data points as well if we're filtering for males
            if (isBoth && isMale)
            {
                var femaleVarianceCoefficientData = henBatchPerformancesQuery
                    .Where(hbp => hbp.HenAmountFemale > 0)
                    .Select(hbp => new BreedingDataPointDTO
                    {
                        Date = hbp.Date,
                        WeekNumber = hbp.WeekNumber,
                        Value = hbp.AvgVariationCoefficientFemale,
                        ParentHenBatchId = hbp.HenBatch.ParentId,
                        ParentHenBatchName = hbp.HenBatch.Parent.DetailedName + " (F)",
                        WarehouseId = hbp.HenBatch.Line.WarehouseId,
                        WarehouseName = hbp.HenBatch.Line.Warehouse.Name + " (F)",
                        LineId = hbp.HenBatch.Line.Id,
                        LineName = hbp.HenBatch.Line.Name + " (F)",
                        GeneticId = hbp.HenBatch.GeneticId,
                        AveragingWeight = hbp.HenAmountFemale,
                    })
                    .ToList();

                varianceCoefficientData.AddRange(femaleVarianceCoefficientData);
            }

            BreedingChart chart = new BreedingChart(new BreedingChartOptionsDTO
            {
                Title = this.localizer[HenStageLang.UniformityChartTitle] + (isBoth ? " (machos e fêmeas)" : (isMale ? " (machos)" : " (fêmeas)")),
                XAxisLabel = this.localizer[HenStageLang.UniformityChartXAxisLabel],
                YAxisLabel = this.localizer[HenStageLang.Uniformity],
                YMin = 40,
                YMax = 100,
                YTickAmount = 13,
                TotalWeeks = filters.TotalWeeks,
            });
            
            chart.SetChartWithWeekRange(filters.StartWeek, filters.EndWeek);

            chart.AddSecondaryYAxis("CV", 3, 15, 5);

            AddStandardSeries(
                chart, 
                uniformityData.First().GeneticId.Value, 
                localizer[HenStageLang.UniformitySTD], 
                uniformitySTDColor, 
                p => p.Uniformity
            );
            AddStandardSeries(
                chart, 
                uniformityData.First().GeneticId.Value, 
                localizer[HenStageLang.CVSTD], 
                cvSTDColor, 
                p => p.VarianceCoefficient, 
                "%", 
                true
            );

            AddGroupedSeriesToChart(filters, chart, uniformityData, this.localizer[HenStageLang.Uniformity], "%", (IGrouping<DateTime, BreedingDataPointDTO> dataPoints) => CalculateSimpleAverage(dataPoints));
            AddGroupedSeriesToChart(filters, chart, varianceCoefficientData, "CV", "%", (IGrouping<DateTime, BreedingDataPointDTO> dataPoints) => CalculateSimpleAverage(dataPoints), true);

            return chart.GetChart();
        }

        public DashboardLineOrBarChartDTO GetSearaBreedingMortalityChart(FilterDataDTO filters)
        {
            var isMale = filters.Gender == "1";
            var isBoth = filters.Gender == "3";
            var henBatchIds = GetHenBatchIdsFromFilters(filters);

            var henReportDbSet = this.unitOfWork.GetModelDbContext().Set<HenReport>();

            var casualtiesQuery = henReportDbSet
                .Join(
                    this.unitOfWork.GetModelDbContext().Set<Casualty>(),
                    hr => hr.Id,
                    c => c.HenReportId,
                    (hr, c) => new { HenReport = hr, Casualty = c }
                )
                .Join(
                    this.unitOfWork.GetModelDbContext().Set<CasualtyReason>(),
                    hrc => hrc.Casualty.CasualtyReasonId,
                    cr => cr.Id,
                    (hrc, cr) => new { hrc.HenReport, hrc.Casualty, CasualtyReason = cr }
                )
                .Where(x => x.Casualty.DeadCount > 0)
                .Where(x => henBatchIds.Contains(x.HenReport.HenBatch.ParentId.Value) || henBatchIds.Contains(x.HenReport.HenBatchId));

            // If not "Both", filter by gender
            if (!isBoth)
            {
                casualtiesQuery = casualtiesQuery.Where(x => x.Casualty.IsFemale == !isMale);
            }

            // filter by period
            if (filters.MinDate.HasValue && filters.MaxDate.HasValue)
            {
                casualtiesQuery = casualtiesQuery.Where(x => x.HenReport.Date >= filters.MinDate.Value && x.HenReport.Date <= filters.MaxDate.Value);
            }

            // Group and aggregate the data for chart generation
            var casualties = casualtiesQuery
                .GroupBy(x => new { 
                    CasualtyReasonName = x.CasualtyReason.Name,
                    IsFemale = isBoth ? (bool?)null : x.Casualty.IsFemale // When isBoth, ignore gender; otherwise, group by gender
                })
                .Select(g => new
                {
                    CasualtyReasonName = isBoth ? 
                        g.Key.CasualtyReasonName : // When isBoth, use only the reason name
                        g.Key.CasualtyReasonName + (g.Key.IsFemale.Value ? " (F)" : " (M)"), // When specific gender, append gender indicator
                    DeadCount = g.Sum(x => x.Casualty.DeadCount)
                })
                .OrderByDescending(g => g.DeadCount)
                .ToList();

            // Generate chart
            var chart = new DashboardLineOrBarChartDTO
            {
                Title = "Mortalidade" + (isBoth ? " (machos e fêmeas)" : (isMale ? " (machos)" : " (fêmeas)")),
                XAxisLabel = "Causa",
                XAxisGridLineWidth = 1,
                ToolTipShared = true,
                Series = new List<DecimalSeriesDTO>(),
                YAxis = new List<YAxis> {
                    new YAxis() {
                        Min = 0,
                        TickAmount = 7,
                        GridLineWidth = 1,
                        Opposite = false,
                        Title = new TitleDTO()
                        {
                            Text = "Número de mortes"
                        }
                    },
                    new YAxis()
                    {
                        Min = 0,
                        Max = 120,
                        TickAmount = 7,
                        GridLineWidth = 1,
                        Opposite = true,
                        Title = new TitleDTO()
                        {
                            Text = "Percentual de mortes"
                        }
                    }
                },
            };

            chart.Categories = casualties.Select(c => c.CasualtyReasonName).Prepend("Acumulado").ToList();

            // Calculate cumulative percentage
            var totalDeadCount = casualties.Sum(c => c.DeadCount);
            var cumulativePercentage = 0M;
            var cumulativePercentageData = new List<decimal?>();

            foreach (var casualty in casualties)
            {
                cumulativePercentage += 100M * casualty.DeadCount / totalDeadCount;
                cumulativePercentageData.Add(cumulativePercentage);
            }

            // Add quantity series (column)
            chart.Series.Add(new DecimalSeriesDTO()
            {
                Name = "Quantidade",
                Data = casualties.Select(c => (decimal?)c.DeadCount).Prepend(totalDeadCount).ToList(),
                Type = "column",
                ToolTip = new ToolTipDTO() { ValueSuffix = " " },
                ShowInLegend = true,
                Visible = true,
                YAxis = 0,
                Color = blueColor,
            }
            );

            // Add cumulative percentage series (spline)
            chart.Series.Add(new DecimalSeriesDTO()
            {
                Name = "Percentual",
                Data = cumulativePercentageData.Prepend(null).ToList(),
                Type = "spline",
                SeriesUnitsSymbol = "%",
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" },
                ShowInLegend = true,
                Visible = true,
                YAxis = 1,
                Color = orangeColor,
            });

            return chart;
        }
        #endregion

        #region Data queries        
        /// <summary>
        /// Get list of hen batch Ids contained in the given container. Only parents or only children (self contained batches belong to all categories)
        /// </summary>
        public IQueryable<Guid> GetHenBatchIdsFromFilters(FilterDataDTO filters)
        {
            if (!(filters.StartWeek.HasValue && filters.EndWeek.HasValue) && !(filters.MinDate.HasValue && filters.MaxDate.HasValue))
            {
                throw exceptionManager.Handle(new ValidationException("Date range is required"));
            }

            var henBatchesQuery = henBatchService.GetAll()
                .Where(hb => hb.HenStage == HenStage.Breeding);

            // Status filter
            if (filters.HenBatchStatus == "active")
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Active && hb.DateEnd == null);
            }
            else if (filters.HenBatchStatus == "closed")
            {
                henBatchesQuery = henBatchesQuery.Where(hb => !hb.Active || hb.DateEnd != null);
            }

            // Current week filter
            if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb =>
                    (hb.DateEnd != null && hb.BatchWeekNumber + EF.Functions.DateDiffDay(hb.DateStart.Value, hb.DateEnd.Value) / 7 >= filters.StartWeek) ||
                    (hb.DateEnd == null && hb.BatchWeekNumber + EF.Functions.DateDiffDay(hb.DateStart.Value, DateTime.Today) / 7 >= filters.StartWeek));

                henBatchesQuery = henBatchesQuery.Where(hb =>
                    (hb.DateEnd != null && hb.BatchWeekNumber + EF.Functions.DateDiffDay(hb.DateStart.Value, hb.DateEnd.Value) / 7 <= filters.EndWeek) ||
                    (hb.DateEnd == null && hb.BatchWeekNumber + EF.Functions.DateDiffDay(hb.DateStart.Value, DateTime.Today) / 7 <= filters.EndWeek));
            }
            if (filters.ProductorId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.FarmId == filters.ProductorId);
            }
            if (filters.HenBatchId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Id == filters.HenBatchId.Value || hb.ParentId == filters.HenBatchId.Value);
            }
            if (filters.WarehouseId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Line.WarehouseId == filters.WarehouseId.Value);
            }
            if (filters.LineId.HasValue)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.LineId == filters.LineId.Value);
            }
            if (filters.RegionalId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.Company.RegionalId == filters.RegionalId);
            }
            if (filters.UnitId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.CompanyId == filters.UnitId);
            }
            if (filters.SupervisorId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.SupervisorId == filters.SupervisorId);
            }
            if (filters.ExtensionistId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.Farm.TechnicianId == filters.ExtensionistId);
            }
            if (filters.GeneticsId != null)
            {
                henBatchesQuery = henBatchesQuery.Where(hb => hb.GeneticId == filters.GeneticsId);
            }
            

            return henBatchesQuery.Select(hb => hb.Id);
        }


        private IQueryable<HenBatchPerformance> GetSearaBreedingPerformances(FilterDataDTO filters)
        {
            // Get hen batch performance data
            IQueryable<HenBatchPerformance> query = henBatchPerformanceService.GetAll(true)
                .Include(hbp => hbp.HenBatch)
                .Where(hbp => hbp.HenBatch.HenStage == HenStage.Breeding);

            if (filters != null)
            {
                if (filters.MinDate != null && filters.MaxDate != null)
                {
                    filters.MaxDate = filters.MaxDate?.Date.AddMonths(1).AddDays(-1); // set to last day of the month, as the frontend sends only MM/YYYY and not DD/MM/YYYY
                    query = query.Where(hbp => hbp.Date >= filters.MinDate.Value && hbp.Date <= filters.MaxDate.Value);
                }
                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(hbp =>
                        hbp.WeekNumber >= filters.StartWeek &&
                        hbp.WeekNumber <= filters.EndWeek);
                }    
            }

            var henBatchIds = GetHenBatchIdsFromFilters(filters);
            // Use exact same filtering logic as breeding dashboard
            query = query.Where(hbp => hbp.HenBatch.ParentId.HasValue
                ? henBatchIds.Contains(hbp.HenBatchId) || henBatchIds.Contains(hbp.HenBatch.ParentId.Value)
                : henBatchIds.Contains(hbp.HenBatchId));

            return query;
        }

        #endregion

        #region Chart data grouping
        private void AddGroupedSeriesToChart(FilterDataDTO filters, BreedingChart chart, List<BreedingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null, bool isSecondaryAxis = false)
        {
            // consolidated
            if (filters.ReportType == "1")
            {
                var consolidatedSeries = GetChartSeriesConsolidated(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                consolidatedSeries.IsSecondaryAxis = isSecondaryAxis;
                chart.AddSeries(consolidatedSeries);
            }
            // open
            else if (filters.ReportType == "2")
            {
                // if batch is not selected, display data by batch
                if (!filters.HenBatchId.HasValue)
                {
                    var batchSeries = GetChartSeriesByBatch(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    batchSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                    chart.AddListSeries(batchSeries);
                }
                // if batch is selected, display data by warehouse
                else
                {
                    var warehouseSeries = GetChartSeriesByWarehouse(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                    warehouseSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                    chart.AddListSeries(warehouseSeries);

                    // if warehouse is selected, also display data by line
                    if (filters.WarehouseId.HasValue)
                    {
                        var lineSeries = GetChartSeriesByLine(dataPoints, seriesLabel, unitSymbol, averagingFunction);
                        lineSeries.ForEach(s => s.IsSecondaryAxis = isSecondaryAxis);
                        chart.AddListSeries(lineSeries);
                    }
                }
            }
        }


        private List<BreedingChartSeriesDTO> GetChartSeriesByBatch(List<BreedingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null)
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.ParentHenBatchId,
                point => seriesLabel + " - " + point.ParentHenBatchName,
                unitSymbol,
                averagingFunction
            );
        }

        private List<BreedingChartSeriesDTO> GetChartSeriesByWarehouse(List<BreedingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null)
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.WarehouseId,
                point => seriesLabel + " - " + point.WarehouseName,
                unitSymbol,
                averagingFunction
            );
        }

        private List<BreedingChartSeriesDTO> GetChartSeriesByLine(List<BreedingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null)
        {
            return GetChartSeriesByProperty(
                dataPoints,
                point => point.LineId,
                point => seriesLabel + " - " + point.LineName,
                unitSymbol,
                averagingFunction
            );
        }

        private BreedingChartSeriesDTO GetChartSeriesConsolidated(List<BreedingDataPointDTO> dataPoints, string seriesLabel, string unitSymbol,
            Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null)
        {
            return new BreedingChartSeriesDTO
            {
                SeriesLabel = seriesLabel,
                UnitSymbol = unitSymbol,
                Data = GroupDataPointsByWeek(dataPoints.ToList(), averagingFunction)
            };
        }

        private List<BreedingChartSeriesDTO> GetChartSeriesByProperty<TKey>(
            List<BreedingDataPointDTO> dataPoints,
            Func<BreedingDataPointDTO, TKey> groupingSelector,
            Func<BreedingDataPointDTO, string> labelSelector,
            string unitSymbol,
            Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null)
        {
            return dataPoints
                .GroupBy(groupingSelector)
                .Select(groupedPoints => new BreedingChartSeriesDTO
                {
                    SeriesLabel = labelSelector(groupedPoints.FirstOrDefault()),
                    UnitSymbol = unitSymbol,
                    Data = GroupDataPointsByWeek(groupedPoints.ToList(), averagingFunction)
                })
                .OrderBy(w => w.SeriesLabel)
                .ToList();
        }

        private List<BreedingChartPointDTO> GroupDataPointsByWeek(List<BreedingDataPointDTO> dataPoints, Func<IGrouping<DateTime, BreedingDataPointDTO>, decimal> averagingFunction = null)
        {
            return dataPoints
                .GroupBy(point => point.Date.Date)
                .Select(a => new BreedingChartPointDTO
                {
                    WeekNumber = a.FirstOrDefault().WeekNumber,
                    Value = averagingFunction?.Invoke(a) ?? CalculateWeightedAverage(a)
                })
                .ToList();
        }

        private decimal CalculateWeightedAverage(IGrouping<DateTime, BreedingDataPointDTO> group)
        {
            var totalWeight = group.Sum(point => point.Value != 0 ? point.AveragingWeight : 0);
            if (totalWeight == 0) return 0;

            var result = group.Sum(point => point.Value * point.AveragingWeight) / totalWeight;
            
            return decimal.Round(result, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven);
        }

        private decimal CalculateViabilityWithParentLogic(IGrouping<DateTime, BreedingDataPointDTO> group, Guid? henBatchParentId)
        {
            var parentPoint = henBatchParentId != null ? group.FirstOrDefault(point => point.HenBatchId == henBatchParentId) : null;
            
            decimal result;
            if (parentPoint != null)
            {
                // If parent hen batch exists in this group, use its value directly
                result = parentPoint.Value;
            }
            else
            {
                // Otherwise use the conditional averaging logic
                result = group.Where(point => point.Value != 0).Any() ? 
                    group.Sum(point => point.Value) / group.Where(point => point.Value != 0).Count() : 
                    group.Sum(point => point.Value);
            }
            
            return decimal.Round(result, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven);
        }

        private void AddStandardSeries(BreedingChart chart, Guid geneticId, string seriesLabel, string color,
            Func<GeneticsParametersReference, decimal> valueSelector, string unitSymbol = "%", bool isSecondaryAxis = false)
        {
            chart.AddSeries(new BreedingChartSeriesDTO
            {
                SeriesLabel = seriesLabel,
                Data = geneticsParameterService.GetAll()
                    .Where(gpr => gpr.HenStage == HenStage.Breeding && gpr.GeneticsId == geneticId)
                    .ToList()
                    .Select(p => new BreedingChartPointDTO
                    {
                        WeekNumber = p.TimePeriodValue,
                        Value = valueSelector(p)
                    }).ToList(),
                Color = color,
                Type = "line",
                UnitSymbol = unitSymbol,
                IsSecondaryAxis = isSecondaryAxis
            });
        }
        #endregion
    }

    public class BreedingDataPointDTO
    {
        public DateTime Date { get; set; }
        public int WeekNumber { get; set; }
        public decimal Value { get; set; }
        public string SeriesLabel { get; set; }
        public int AveragingWeight { get; set; }
        public Guid HenBatchId { get; set; }
        public Guid? ParentHenBatchId { get; set; }
        public string ParentHenBatchName { get; set; }
        public Guid? WarehouseId { get; set; }
        public string WarehouseName { get; set; }
        public Guid? LineId { get; set; }
        public string LineName { get; set; }
        public Guid? GeneticId { get; set; }
        public int HenAmountMale { get; set; }
        public int HenAmountFemale { get; set; }
    }

    public class BreedingChartPointDTO
    {
        public DateTime Date { get; set; }
        public int WeekNumber { get; set; }
        public decimal Value { get; set; }
    }

    public class BreedingChartSeriesDTO
    {
        public string SeriesLabel { get; set; }
        public string UnitSymbol { get; set; } = "%";
        public string Color { get; set; }
        public string Type { get; set; } = "spline";
        public List<BreedingChartPointDTO> Data { get; set; }
        public bool IsSecondaryAxis { get; set; } = false;
    }

    public class BreedingChartOptionsDTO
    {
        public string Title { get; set; }
        public string XAxisLabel { get; set; }
        public string YAxisLabel { get; set; }
        public int? YMin { get; set; } = null;
        public int? YMax { get; set; } = null;
        public int? YTickAmount { get; set; } = null;
        public int? YTickInterval { get; set; } = null;
        public int TotalWeeks { get; set; } = 25;
    }

    public class BreedingChart
    {
        DashboardLineOrBarChartDTO chart;
        int totalWeeks = 25;

        List<string> colors = new List<string>() { "#C042FB", "#2ECC71", "#F18A2E", "#2471A3", "#85C1E9", "#EDFB42", "#E74C3C", "#145A32", "#633974" };

        bool IsAutoRangeYAxis = true;
        public int WeekOffset { get; set; } = 0;

        public BreedingChart(BreedingChartOptionsDTO options)
        {
            this.chart = new DashboardLineOrBarChartDTO();
            this.chart.Title = options.Title;
            this.chart.XAxisLabel = options.XAxisLabel;
            this.chart.XAxisGridLineWidth = 1;
            this.chart.ToolTipShared = false;

            this.IsAutoRangeYAxis = options.YMin == null;

            this.chart.YAxis = new List<YAxis>
            {
                new YAxis()
                {
                    Min = options.YMin ?? int.MaxValue,
                    Max = options.YMax,
                    TickAmount = options.YTickAmount,
                    TickInterval = options.YTickInterval,
                    GridLineWidth = 1,
                    Opposite = false,
                    Title = new TitleDTO()
                    {
                        Text = options.YAxisLabel
                    }
                }
            };

            this.chart.Series = new List<DecimalSeriesDTO>();

            this.totalWeeks = options.TotalWeeks;
        }

        public void AddSeries(BreedingChartSeriesDTO series)
        {
            var seriesValues = new List<decimal?>(new decimal?[totalWeeks]);

            foreach (var point in series.Data)
            {
                var index = point.WeekNumber - WeekOffset;
                
                if (index >= 0 && index < totalWeeks && point.Value != 0M)
                {
                    seriesValues[index] = decimal.Round(point.Value, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.ToEven);
                }
            }

            chart.Series.Add(new DecimalSeriesDTO
            {
                Name = series.SeriesLabel,
                Type = series.Type,
                Data = seriesValues,
                SeriesUnitsSymbol = series.UnitSymbol,
                ToolTip = new ToolTipDTO() { ValueSuffix = series.UnitSymbol },
                ShowInLegend = true,
                YAxis = series.IsSecondaryAxis ? 1 : 0,
                Color = series.Color ?? colors[chart.Series.Count % colors.Count]
            });

            if (IsAutoRangeYAxis)
            {
                chart.YAxis[series.IsSecondaryAxis ? 1 : 0].Min = (int)Math.Min(seriesValues.Where(d => d != null).Min() ?? 0, (decimal)chart.YAxis[series.IsSecondaryAxis ? 1 : 0].Min);
            }
        }

        public void AddSecondaryYAxis(string label, int? min = null, int? max = null, int? tickAmount = null)
        {
            this.chart.YAxis.Add(new YAxis()
            {
                Min = min ?? 0,
                Max = max,
                TickAmount = tickAmount,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = label }
            });
        }

        public void AutoRangeYAxis()
        {
            if (!this.IsAutoRangeYAxis)
            {
                return;
            }

            foreach (var series in chart.Series)
            {
                var seriesMinimum = series.Data.Where(d => d != null).Min();
                this.chart.YAxis[0].Min = (int)Math.Min(seriesMinimum ?? 0, (decimal)chart.YAxis[0].Min);
                if (chart.YAxis.Count > 1)
                {
                    this.chart.YAxis[1].Min = (int)Math.Min(seriesMinimum ?? 0, (decimal)chart.YAxis[1].Min);
                }
            }
        }

        public void AddListSeries(List<BreedingChartSeriesDTO> series)
        {
            foreach (var s in series)
            {
                AddSeries(s);
            }
        }

        public DashboardLineOrBarChartDTO GetChart()
        {
            return chart;
        }

        private void SetCategories()
        {
            for (var week = WeekOffset; week <= chart.WeekEnd; week++)
                chart.Categories.Add(week.ToString());
        }
        
        public void SetChartWithWeekRange(int? weekStart, int? weekEnd)
        {
            chart.WeekStart = weekStart;
            chart.WeekEnd = weekEnd;
            
            WeekOffset = weekStart ?? 0;    

            SetCategories();
        }
    }
}