﻿using Binit.Framework.Helpers.Excel;
using Domain.Entities.DTOs;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Entities.Model.Views;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.BusinessLogic.DTOs.HenBatchDTOs;
using Domain.Logic.DTOs.HenBatchDTOs;
using Domain.Logic.Validations;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface IHenBatchBusinessLogic
    {
        /// <summary>
        /// Assign formula to batches from a warehouse if it is not assigned already
        /// </summary>
        void AddFormulaToWarehouseBatches(Guid warehouseId, Guid formulaId);

        /// <summary>
        /// Creates a HenBatch, fetch it, set its name and detailed name and updates it.
        /// </summary>
        Task SetPropertiesAndCreate(HenBatch henBatch);

        /// <summary>
        /// Creates a henbatch if there is only one distribution or parent henbatch with children in case of multiple distribution
        /// </summary>
        Task<HenBatch> SetPropertiesAndCreate((HenBatch parent, List<HenBatch> children) henBatches);

        /// <summary>
        /// Updates a HenBatch, fetch it, set its  detailed name and updates it.
        /// </summary>
        Task SetPropertiesAndUpdate(HenBatch henBatch);

        List<SelectListItem> GetGenetics(HenStage? henStage = null, Guid? selectedGenetic = null, Guid? selectedFarm = null);

        (List<int>, int) GetDisabledWeekDays(Guid warehouseIdid, string discriminator = null, bool isForOpeningDate = false);

        /// <summary>
        /// Gets all henbatches to use in hen report index henbatch filter
        /// </summary>
        public Genetic GetGeneticByHenBatch(Guid HBId);

        List<SelectListItem> GetHenBatches(HenStage? henStage = null, Guid? lineId = null, bool activeBatches = false, bool orderByNew = false);

        /// <summary>
        /// returns HenBatches by Status
        /// </summary>
        List<SelectListItem> GetHenBatchesByStatus(string status, HenStage? henStage = null);

        /// <summary>
        /// Get list of hen batch Ids contained in the given container. Only parents or only children (self contained batches belong to all categories)
        /// </summary>
        public List<Guid> GetHenBatchIdsByContainer(Guid containerId, string containerType = null, HenStage? henStage = null, bool activeBatches = true, bool parents = true);

        List<SelectListItem> GetAvailableLines(HenBatchFilterDTO d);

        /// <summary>
        /// Get hen batches that belong to the given genetic ids
        /// </summary>
        List<SelectListItem> GetAllByGenetic(List<Guid> geneticIds, HenStage? henStage, bool? active);

        /// <summary>
        /// Get all distinct genetics from hen batches
        /// </summary>
        List<SelectListItem> GetAllGenetics(bool? activeBatches = null);

        /// <summary>
        /// Get all distinct formulas from hen batches
        /// </summary>
        List<SelectListItem> GetAllFormulasByHenStage(HenStage henStage, bool? activeBatches = null);

        /// <summary>
        ///  creates the select list for farms with hen batches
        /// </summary>
        List<SelectListItem> GetFarms(HenStage? henStage = null, Guid? selected = null);

        /// <summary>
        ///  creates the select list for clusters with hen batches
        /// </summary>
        List<SelectListItem> GetClusters(HenStage? henStage = null, Guid? selectedFarm = null);

        /// <summary>
        ///  creates the select list for clusters with hen batches
        /// </summary>
        List<SelectListItem> GetWarehouses(HenStage? henStage = null, Guid? selectedCluster = null);

        /// <summary>
        ///  creates the select list for lines with hen batches
        /// </summary>
        List<SelectListItem> GetLines(HenStage? henStage = null, Guid? selectedWarehouse = null);

        /// <summary>
        ///  creates the select list for cages with hen batches
        /// </summary>
        List<SelectListItem> GetCages(HenStage? henStage = null, Guid? selectedLine = null);

        /// <summary>
        ///  creates the select list for warehouses of hen batche
        /// </summary>
        List<SelectListItem> GetWarehousesByHenBatch(Guid selectedHenBatch, HenStage? henStage = null);

        /// <summary>
        ///  creates the select list for lines of selected warehouse and hen batch  
        /// </summary>
        List<SelectListItem> GetLinesByWarehouseAndHenBatch(Guid selectedWarehouse, Guid selectedHenBatch, HenStage? henStage = null);

        /// <summary>
        /// Returns all hen batches filtered by genetic, status, start and end date including all its relationships.
        /// </summary>
        public IQueryable<HenBatch> GetAllFiltered(Dictionary<string, string> filters, HenStage? henStage = null, bool? active = null, HenBatchTypeEnum? henBatchType = null);

        /// <summary>
        /// Returns all hen batches filtered From HenBatchGridView
        /// </summary>
        public IQueryable<HenBatchGridView> GetAllForIndex(Dictionary<string, string> filters);

        /// <summary>
        /// Closes asynchronously the hen batch setting the end date with the current timestamp.
        /// </summary>
        Task<BusinessValidationResult<HenBatch>> CloseAsync(Guid id, string reason, DateTime closingDate);

        /// <summary>
        /// Delete hen batch asynchronically only if it has no hen report associated.
        /// Also, removes hen batch from the line.
        /// It can delete distributions if the henbatch is distributed in different lines.
        /// </summary>
        Task DeleteAsync(Guid id, bool deleteDistributions = false);

        /// <summary>
        /// Get list of origin or destination options for bird movement, whether it is internal or external. 
        /// </summary>
        List<MovementOptionDTO> GetBirdMovementOptions(Guid id, string intern, bool origin = true);

        /// <summary>
        /// Get list of origin or destination options for internal bird movement of the same henstage.
        /// </summary>
        List<MovementOptionDTO> GetSameStageBirdMovementOptions(HenStage? henStage = null, Guid? id = null);

        /// <summary>
        /// Moves birds from an origin to a destination. 
        /// </summary>
        Task MoveBirds(BirdMovementDTO dto);

        /// <summary>
        /// Updates FirstProductionDate in distributions and parent
        /// </summary>
        Task FirstProductionDate(List<FirstProductionDateDTO> dtos, Guid henbatchId, DateTime? capitalizationDate);

        /// <summary>
        /// Get list of material options for bird movement, for user to match with genetic. 
        /// </summary>
        List<SelectListItem> GetMaterialOptions(bool origin = true);
        Task<ExportResult> ExportExcel(string searchTerm, Dictionary<string, string> data, HenStage? henStage);

        /// <summary>
        /// Get clusters which have henwarehouses with available lines for a given farm
        /// </summary>
        IQueryable<Cluster> GetAvailableClusters(HenStage henStage, Guid? farmId = null);

        /// <summary>
        /// Get farms which have warehouses with available lines for the given henstage.
        /// </summary>
        IQueryable<Farm> GetAvailableFarms(HenStage? henStage);

        /// <summary>
        /// Get henwarehouses with available lines for a given henstage. 
        /// If indicated, only those belonging to the given cluster.
        /// </summary>
        IQueryable<HenWarehouse> GetAvailableHenWarehouses(HenStage? henStage = null, Guid? clusterId = null);

        /// <summary>
        /// Get available lines for a given hen warehouse. 
        /// </summary>
        IQueryable<Line> GetAvailableLines(Guid? henWarehouseId);

        /// <summary>
        /// Updates a henbatch if there is only one distribution or parent henbatch with children in case of multiple distribution
        /// </summary>
        Task SetPropertiesAndUpdate((HenBatch parent, List<HenBatch> children) henBatches);

        /// <summary>
        /// Asynchronously retrieves a parent or single henbatch that belongs to a given farm by code.
        /// </summary>
        Task<HenBatch> GetByCodeAndFarmAsync(string code, string farmCode);

        /// <summary>
        /// Return parent hen batches whose children have available space for birds.
        /// </summary>
        IQueryable<HenBatch> GetAvailableParents(HenStage? henStage = null);

        /// <summary>
        ///  returns a list of origins containers for the childrens hen batches 
        /// </summary>
        List<Container> GetChildsOrigins(Guid parentId, HenStage? henStage = null);

        /// <summary>
        ///  validate and set FirstProductionDate to the hen batch parent and all its children and update
        /// </summary>
        Task SetFirstProductionDateAndUpdate(Guid id, DateTime date);

        /// <summary>
        /// Get the first date of movement of hens of the parent hen batch.
        /// </summary>
        DateTime? GetFirstDateHenMovementForParent(IEnumerable<HenBatch> children);

        /// <summary>
        /// Get all parent hen batches by hen stage.
        /// </summary>
        List<SelectListItem> GetParentHenBatches(HenStage? henStage = null, bool? activeBatches = null, bool orderByNew = false, bool isMainFilter = false);

        /// <summary>
        /// <summary>
        /// Retorna parentHenBatches, warehouses e lines
        /// </summary>
        ContainerFilterDTO GetAllContainerFilter(HenStage henStage, Guid? farmId, bool? activeBatches = null, bool orderByNew = false);

        (List<string> headers, List<Dictionary<string, string>> table) GetDistributionQuantitiesTable(Guid henBatchId);

        Task RedistributeHen(HenRedistributionsDTO redistribution);

        List<SelectListItem> GetLinesFiltered(HenStage? henStage = null, Guid? selectedWarehouse = null, Guid? selectedHenBatch = null);

        List<SelectListItem> GetLinesHenBatchFiltered(HenStage? henStage = null, Guid? selectedWarehouse = null, Guid? selectedHenBatch = null);

        /// <summary>
        /// Gets page of capitalization by farm code
        /// </summary>
        Task<HenBatchCapitalizationPage> GetCapitalization(HenBatchCapitalizationReqDTO henBatchCapitalizationReqDTO);

        /// <summary>
        /// Compensates the initial hen amount after a movement within the same parent henbatch
        /// </summary>
        void UpdateInitialHenAmount(BirdMovementDTO movement);

    }
}
