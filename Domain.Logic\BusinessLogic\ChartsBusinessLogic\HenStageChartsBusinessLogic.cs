using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.ChartsBusinessLogic.HenStageChartsBusinessLogic;
using StatsLang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.StatisticsBusinessLogic;

namespace Domain.Logic.BusinessLogic.ChartsBusinessLogic
{
    public class HenStageChartsBusinessLogic : IHenStageChartsBusinessLogic
    {
        private readonly IClusterService clusterService;
        private readonly IContainerService<Container> containerService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IExceptionManager exceptionManager;
        private readonly ICommonChartsBusinessLogic chartFunctions;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly IFarmService farmService;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IGeneticService geneticService;
        private readonly IHenBatchService henBatchService;
        private readonly IHenReportService henReportService;
        private readonly ILineService lineService;
        private readonly ISampleCageReportService sampleCageReportService;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IOperationContext operationContext;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;

        public HenStageChartsBusinessLogic(
            IClusterService clusterService,
            IContainerService<Container> containerService,
            IStringLocalizer<SharedResources> localizer,
            IExceptionManager exceptionManager,
            ICommonChartsBusinessLogic chartFunctions,
            ICapacityUnitService capacityUnitService,
            IFarmService farmService,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IGeneticService geneticService,
            IHenBatchService henBatchService,
            IHenReportService henReportService,
            ILineService lineService,
            ISampleCageReportService sampleCageReportService,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IOperationContext operationContext,
            IService<TenantConfiguration> tenantConfigurationService,
            IHenBatchBusinessLogic henBatchBusinessLogic)
        {
            this.clusterService = clusterService;
            this.containerService = containerService;
            this.localizer = localizer;
            this.exceptionManager = exceptionManager;
            this.chartFunctions = chartFunctions;
            this.capacityUnitService = capacityUnitService;
            this.farmService = farmService;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.geneticService = geneticService;
            this.henBatchService = henBatchService;
            this.henReportService = henReportService;
            this.lineService = lineService;
            this.sampleCageReportService = sampleCageReportService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.operationContext = operationContext;
            this.tenantConfigurationService = tenantConfigurationService;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
        }
        #region Charts
        /// <summary>
        /// Get male and female hen amounts for each hen batch and show it's total eggs
        /// </summary>
        public DashboardLineOrBarChartDTO MaleFemaleDistributionChart(FilterDataDTO filters, HenStage? henStage)
        {
            DashboardLineOrBarChartDTO dto = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[Lang.MaleFemaleDistributionChartTitle],
                XAxisLabel = this.localizer[Lang.MaleFemaleDistributionChartXAxisLabel],
                Series = new List<DecimalSeriesDTO>(),
                YAxis = new List<YAxis>(),
                Categories = new List<string>(),
                NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage]
            };

            // HenAmount axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = false,
                Title = new TitleDTO() { Text = this.localizer[Lang.MaleFemaleDistributionChartYAxisHenAmountLabel] }
            });

            var henReportsToFilter = this.henReportService.GetAll()
                .Where(hr =>
                    (!henStage.HasValue || hr.HenBatch.HenStage == henStage.Value) &&
                    hr.ReportEnum == ReportEnum.New &&
                    filters.MinDate.Value.Date <= hr.Date.Date &&
                    hr.Date.Date <= filters.MaxDate.Value.Date &&
                    (!filters.LineId.HasValue || hr.HenBatch.LineId == filters.LineId) &&
                    (!filters.WarehouseId.HasValue || hr.HenBatch.Line.WarehouseId == filters.WarehouseId) &&
                    (!filters.HenBatchId.HasValue || hr.HenBatchId == filters.HenBatchId || hr.HenBatch.ParentId == filters.HenBatchId)
                )
                .Select(hr => new
                {
                    Date = hr.Date,
                    HenBatchId = hr.HenBatchId,
                    ParentHenBatchId = hr.HenBatch.ParentId,
                    LineId = hr.HenBatch.LineId,
                    WarehouseId = hr.HenBatch.Line.WarehouseId,
                    ClusterId = hr.HenBatch.Line.Warehouse.ClusterId,
                    FarmId = hr.HenBatch.FarmId,
                    HenAmountFemale = hr.HenAmountFemale,
                    HenAmountMale = hr.HenAmountMale,
                    HatchableEggs = hr.HatchableEggs,
                    CommercialEggs = hr.CommercialEggs

                });

            // If no farm is selected, the cumulative data by parentHenBatch is displayed
            // else, the distributions for that farm are displayed.
            bool showDataByParentHenBatch = henReportsToFilter.Select(hr => hr.FarmId).Distinct().Count() > 1;

            var henReports = henReportsToFilter
                .ToList()
                .GroupBy(hr => new
                {
                    ContainerId = filters.LineId.HasValue ? hr.LineId :
                                filters.WarehouseId.HasValue ? hr.WarehouseId :
                                filters.ClusterId.HasValue ? hr.ClusterId :
                                filters.FarmId.HasValue ? hr.HenBatchId :
                                showDataByParentHenBatch ? hr.ParentHenBatchId.HasValue ? hr.ParentHenBatchId.Value : hr.HenBatchId : hr.HenBatchId,
                    ContainerType = filters.LineId.HasValue ? ContainerTypes.Line :
                                filters.WarehouseId.HasValue ? ContainerTypes.HenWarehouse :
                                filters.ClusterId.HasValue ? "cluster" : ContainerTypes.HenBatch
                })
                .Select(hrGroup => new
                {
                    ContainerId = hrGroup.Key.ContainerId,
                    ContainerName = hrGroup.Key.ContainerType == "farm" ? farmService.GetAll().Where(f => f.Id == hrGroup.Key.ContainerId).Select(f => f.Code).FirstOrDefault() :
                                    hrGroup.Key.ContainerType == "cluster" ? clusterService.GetAll().Where(c => c.Id == hrGroup.Key.ContainerId).Select(c => c.Name).FirstOrDefault() :
                                    showDataByParentHenBatch ? containerService.GetAll()
                                    .Where(c => c.Id == hrGroup.Key.ContainerId && c.ContainerType == hrGroup.Key.ContainerType)
                                    .Select(c => c.Code + " | " + c.DetailedName).FirstOrDefault() :
                                    henBatchService.GetAll()
                                    .Include(h => h.Line).ThenInclude(l => l.Warehouse)
                                    .Where(c => c.Id == hrGroup.Key.ContainerId && c.ContainerType == hrGroup.Key.ContainerType)
                                    .Select(hb => $"{hb.Code} | {hb.Line.Warehouse.Name} | {hb.Line.Name}").FirstOrDefault(),
                    HenAmountFemaleAccum = hrGroup.Sum(g => g.HenAmountFemale),
                    HenAmountMaleAccum = hrGroup.Sum(g => g.HenAmountMale),
                    HatchableEggsAccum = hrGroup.Sum(g => g.HatchableEggs),
                    CommercialEggsAccum = hrGroup.Sum(g => g.CommercialEggs),
                    DaysRange = (hrGroup.Max(g => g.Date) - hrGroup.Min(g => g.Date)).Days + 1
                })
                .OrderBy(g => g.ContainerName);

            dto.Categories = henReports.Select(g => g.ContainerName).ToList();
            List<decimal> henAmountFemale = henReports.Select(g => Math.Round(g.HenAmountFemaleAccum / (decimal)g.DaysRange)).ToList();
            List<decimal> henAmountMale = henReports.Select(g => Math.Round(g.HenAmountMaleAccum / (decimal)g.DaysRange)).ToList();
            List<decimal> hatchableEggs = henReports.Select(g => Math.Round(g.HatchableEggsAccum / (decimal)g.DaysRange)).ToList();
            List<decimal> commercialEggs = henReports.Select(g => Math.Round(g.CommercialEggsAccum / (decimal)g.DaysRange)).ToList();

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MaleFemaleDistributionChartSeriesHenAmountFemaleLabel],
                Data = henAmountFemale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                ShowInLegend = true,
                Visible = true,
                Type = "column",
                Stack = "henAmount",
                Stacking = "normal",
                YAxis = 0,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MaleFemaleDistributionChartSeriesHenAmountMaleLabel],
                Data = henAmountMale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                ShowInLegend = true,
                Visible = false,
                Type = "column",
                Stack = "henAmount",
                Stacking = "normal",
                YAxis = 0,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });

            if (!henStage.HasValue || henStage.Value == HenStage.Laying)
            {
                dto.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.MaleFemaleDistributionChartSeriesHatchableEggsLabel],
                    Data = hatchableEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "column",
                    Stack = "eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });

                dto.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.MaleFemaleDistributionChartSeriesCommercialEggsLabel],
                    Data = commercialEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "column",
                    Stack = "eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });
            }

            // If all data is zero, clear lists so no available data message pops up
            if (dto.Series.All(s => s.Data.All(p => p == 0)))
                foreach (DecimalSeriesDTO series in dto.Series)
                    series.Data.Clear();

            return dto;
        }

        /// <summary>
        /// Get deaths and different environment variables
        /// </summary>
        public DashboardLineOrBarChartDTO MortalityChart(FilterDataDTO filters, HenStage? henStage = null)
        {
            // Initialize chart DTO
            DashboardLineOrBarChartDTO dto = new DashboardLineOrBarChartDTO
            {
                Title = !henStage.HasValue || henStage.Value == HenStage.Laying ? this.localizer[Lang.MortalityChartTitle] :
                this.localizer[Lang.MortalityChartTitleBreeding],
                XAxisLabel = this.localizer[Lang.MortalityChartXAxisLabel],
                Series = new List<DecimalSeriesDTO>(),
                YAxis = new List<YAxis>(),
                Categories = new List<string>(),
                NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage]
            };

            // Horizontal axis
            List<DateTime> dateTimeAxis = chartFunctions.BuildDateTimeAxis(filters.MinDate.Value, filters.MaxDate.Value.AddDays(1));
            foreach (DateTime date in dateTimeAxis)
                dto.Categories.Add(date.ToShortDateString());

            // DeadCount axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = false,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisDeadCountLabel] }
            });

            // Humidity axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisHumidityLabel] }
            });

            // WaterChlorineConcentration axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisWaterChlorineConcentrationLabel] }
            });

            // Ph axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisWaterPhLabel] }
            });


            // EggCount axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisEggCountLabel] }
            });

            // Temperature axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisTemperatureLabel] }
            });

            // FoodConsumption axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisFoodConsumptionLabel] }
            });

            // WaterConsumption axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.MortalityChartYAxisWaterConsumptionLabel] }
            });

            var henReports = this.henReportService.GetAll()
                .Where(hr =>
                    (!henStage.HasValue || hr.HenBatch.HenStage == henStage.Value) &&
                    hr.ReportEnum == ReportEnum.New &&
                    filters.MinDate.Value.Date <= hr.Date.Date &&
                    hr.Date.Date <= filters.MaxDate.Value.Date &&
                    (!filters.LineId.HasValue || hr.HenBatch.LineId == filters.LineId) &&
                    (!filters.WarehouseId.HasValue || hr.HenBatch.Line.WarehouseId == filters.WarehouseId) &&
                    (!filters.HenBatchId.HasValue || hr.HenBatchId == filters.HenBatchId || hr.HenBatch.ParentId == filters.HenBatchId)
                )
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Cluster)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Parent)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Genetic)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Farm)
                .Include(hr => hr.ClassifiedEggs).ThenInclude(ce => ce.Material)
                .Include(hr => hr.Casualties).ThenInclude(c => c.CasualtyReason)
                .ToList()
                .GroupBy(hr => new { Date = hr.Date.Date })
                .Select(hrGroup => new
                {
                    Date = hrGroup.Key.Date,
                    Humidity = hrGroup.Average(g => g.Humidity),
                    WaterChlorineConcentration = hrGroup.Average(g => g.WaterChlorineConcentration),
                    WaterPh = hrGroup.Average(g => g.WaterPh),
                    MaxTemperature = hrGroup.Max(g => g.MaxTemp),
                    MinTemperature = hrGroup.Min(g => g.MinTemp),
                    FemaleFeedIntake = hrGroup.Sum(g => g.HenAmountFemale) == 0 ? 0 : (hrGroup.Sum(g => g.FeedIntakeFemale) * 1000) / hrGroup.Sum(g => g.HenAmountFemale),
                    MaleFeedIntake = hrGroup.Sum(g => g.HenAmountMale) == 0 ? 0 : (hrGroup.Sum(g => g.FeedIntakeMale) * 1000) / hrGroup.Sum(g => g.HenAmountMale),
                    WaterConsumption = hrGroup.Sum(g => g.WaterConsumption),
                    TotalCommercialEggs = hrGroup.Sum(g => g.CommercialEggs),
                    TotalHatchableEggs = hrGroup.Sum(g => g.HatchableEggs),
                    Eggs = hrGroup.SelectMany(g => g.ClassifiedEggs)
                        .GroupBy(ce => ce.MaterialId)
                        .Select(ce => new
                        {
                            Date = hrGroup.Key.Date,
                            Quantity = ce.Sum(e => e.Quantity),
                            MaterialId = ce.Key,
                            Name = ce.Min(ce => ce.Material.Name)
                        }),
                    Casualties = hrGroup.SelectMany(g => g.Casualties)
                        .GroupBy(c => c.CasualtyReasonId)
                        .Select(crg => new
                        {
                            Date = hrGroup.Key.Date,
                            DeadCount = crg.Sum(c => c.DeadCount),
                            CasualtyReasonId = crg.Key,
                            CasualtyReason = crg.Min(c => c.CasualtyReason.Name)
                        }),
                    UnasignedDeaths = hrGroup.Where(hr => (hr.DeadFemale - hr.CasualtiesFemale.Where(cf => cf.HenReport.Id == hr.Id).Sum(hr => hr.DeadCount)) > 0 ||
                                                            (hr.DeadMale - hr.CasualtiesMale.Where(cf => cf.HenReport.Id == hr.Id).Sum(hr => hr.DeadCount)) > 0)
                        .Sum(d => d.DeadFemale + d.DeadMale - d.CasualtiesFemale.Where(cf => cf.HenReport.Id == d.Id).Sum(d => d.DeadCount) - d.CasualtiesMale.Where(cf => cf.HenReport.Id == d.Id).Sum(d => d.DeadCount)),
                })
                .ToList();

            var casualtyReasonGroups = henReports.SelectMany(hr => hr.Casualties).GroupBy(g => g.CasualtyReasonId);

            foreach (var casualtyReasonGroup in casualtyReasonGroups)
            {
                List<decimal> casualties = new List<decimal>(new decimal[dateTimeAxis.Count()]);

                foreach (var casualty in casualtyReasonGroup)
                {
                    int index = dateTimeAxis.FindIndex(d => d.Date == casualty.Date.Date);
                    if (index != -1)
                    {
                        casualties[index] += casualty.DeadCount;
                    }
                }

                dto.Series.Add(new DecimalSeriesDTO()
                {
                    Name = casualtyReasonGroup.Min(c => c.CasualtyReason),
                    Data = casualties.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = false,
                    Type = "column",
                    Stack = "deadCount",
                    Stacking = "normal",
                    YAxis = 0,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });
            }

            List<decimal> humidity = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal?> waterChlorineConcentration = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> waterPh = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> maxTemp = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> minTemp = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> femaleFeedIntake = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> maleFeedIntake = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> waterConsumption = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> unasignedDeaths = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> totalHatchableEggs = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);
            List<decimal?> totalCommercialEggs = new List<decimal?>(new decimal?[dateTimeAxis.Count()]);

            foreach (var henReport in henReports)
            {
                int index = dateTimeAxis.FindIndex(d => d.Date == henReport.Date.Date);
                if (index != -1)
                {
                    humidity[index] += henReport.Humidity.HasValue ? henReport.Humidity.Value : 0;
                    waterChlorineConcentration[index] = henReport.WaterChlorineConcentration;
                    waterPh[index] = henReport.WaterPh;
                    maxTemp[index] = henReport.MaxTemperature;
                    minTemp[index] = henReport.MinTemperature;
                    femaleFeedIntake[index] = henReport.FemaleFeedIntake;
                    maleFeedIntake[index] = henReport.MaleFeedIntake;
                    waterConsumption[index] = henReport.WaterConsumption;
                    unasignedDeaths[index] = henReport.UnasignedDeaths == 0 ? (int?)null : henReport.UnasignedDeaths;

                    if (!henStage.HasValue || henStage.Value == HenStage.Laying)
                    {
                        if ((henReport.TotalHatchableEggs + henReport.TotalCommercialEggs) != 0)
                        {
                            totalHatchableEggs[index] = henReport.TotalHatchableEggs;
                            totalCommercialEggs[index] = henReport.TotalCommercialEggs;
                        }
                        else
                        {
                            totalHatchableEggs[index] = 0;
                            totalCommercialEggs[index] = 0;
                        }

                    }
                }
            }

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MortalityChartSeriesHumidityLabel],
                Data = humidity.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                ShowInLegend = true,
                Visible = false,
                Type = "spline",
                YAxis = 1,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + this.localizer[Lang.MortalityChartValueSuffixHumidityLabel] }
            });

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MortalityChartSeriewsWaterPhLabel],
                DataObjects = new List<object>(waterPh.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = false,
                Type = "spline",
                YAxis = 3,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + this.localizer[Lang.MortalityChartValueSuffixWaterPhLabel] }
            });

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MortalityChartSeriesWaterChlorineConcentrationLabel],
                DataObjects = new List<object>(waterChlorineConcentration.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = false,
                Type = "spline",
                YAxis = 2,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + this.localizer[Lang.MortalityChartValueSuffixWaterChlorineConcentrationLabel] }
            });

            string tempSymbol = capacityUnitService.GetAll().Where(cu => cu.Id == CapacityUnits.Celsius).Select(cu => cu.Symbol).FirstOrDefault();
            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MaxTemperatureLabel],
                DataObjects = new List<object>(maxTemp.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = true,
                Type = "spline",
                YAxis = 5,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + tempSymbol }
            });
            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MinTemperatureLabel],
                DataObjects = new List<object>(minTemp.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = true,
                Type = "spline",
                YAxis = 5,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + tempSymbol }
            });

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.FemaleFeedIntakeLabel],
                DataObjects = new List<object>(femaleFeedIntake.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = false,
                Type = "spline",
                YAxis = 6,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + "GAD" }
            });
            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MaleFeedIntakeLabel],
                DataObjects = new List<object>(maleFeedIntake.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = false,
                Type = "spline",
                YAxis = 6,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + "GAD" }
            });

            string liters = capacityUnitService.GetAll().Where(cu => cu.Id == CapacityUnits.Litres).Select(cu => cu.Symbol).FirstOrDefault();
            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.WaterConsumptionLabel],
                DataObjects = new List<object>(waterConsumption.Select(w => new { y = w }).ToList()),
                ShowInLegend = true,
                Visible = false,
                Type = "spline",
                YAxis = 7,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + liters }
            });

            dto.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MortalityChartSeriesUnasignedDeathsLabel],
                DataObjects = new List<object>(unasignedDeaths.Select(ud => new { y = ud }).ToList()),
                ShowInLegend = true,
                Visible = false,
                Type = "column",
                Stack = "deadCount",
                Stacking = "normal",
                YAxis = 0,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });


            if (!henStage.HasValue || henStage.Value == HenStage.Laying)
            {
                string unitsSymbol = capacityUnitService.GetAll().Where(cu => cu.Id == CapacityUnits.Units).Select(cu => cu.Symbol).FirstOrDefault();

                var classifiedEggs = henReports.SelectMany(hr => hr.Eggs).GroupBy(g => g.MaterialId);

                foreach (var classifiedEgg in classifiedEggs)
                {
                    List<decimal> amounts = new List<decimal>(new decimal[dateTimeAxis.Count()]);

                    foreach (var egg in classifiedEgg)
                    {
                        int index = dateTimeAxis.FindIndex(d => d.Date == egg.Date.Date);
                        if (index != -1)
                        {
                            amounts[index] += egg.Quantity;
                        }
                    }

                    dto.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = classifiedEgg.Min(c => c.Name),
                        Data = amounts.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = false,
                        Type = "spline",
                        YAxis = 4,
                        ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + unitsSymbol }
                    });
                }

                dto.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.TotalCommercialEggsLabel],
                    Data = totalCommercialEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 4,
                    ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + unitsSymbol }
                });
                dto.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.TotalHatchableEggsLabel],
                    Data = totalHatchableEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 4,
                    ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + unitsSymbol }
                });
            }

            // If all data is zero, clear lists so no available data message pops up
            if (dto.Series.All(s => s.Data == null || s.Data.All(p => p == 0)))
                foreach (DecimalSeriesDTO series in dto.Series.Where(s => s.Data != null))
                    series.Data.Clear();

            return dto;
        }

        ///<summary>
        /// Build DTO for active and inactive lines capacity chart.
        ///</summary>
        public DashboardLineOrBarChartDTO LineCapacity(HenStage? henStage = null)
        {
            // Get batches
            IQueryable<Line> lines = this.lineService.GetAllFull();

            if (henStage.HasValue)
                lines = lines.Where(l => l.Warehouse.HenStage == henStage);

            // Get capacities 
            decimal totalLinesCapacity = lines != null
                ? (decimal)lines.SelectMany(l => l.AcceptedMaterialType)
                    .Where(am => am.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAve))
                    .Sum(s => s.CapacityStandarizedValue)
                : 0m; // Si lines es null, asigna un valor predeterminado (por ejemplo, 0)
            decimal activeLinesCapacity = (decimal)lines
                .Where(l => l.HenBatches.Any(hb => !hb.DateEnd.HasValue && (hb.HenAmountFemale > 0 || hb.HenAmountMale > 0) && hb.DateStart.HasValue && hb.DateStart.Value.Date <= DateTime.Today.Date))
                .SelectMany(l => l.AcceptedMaterialType)
                .Where(am => am.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAve))
                .Sum(s => s.CapacityStandarizedValue);
            decimal inactiveLinesCapacity = totalLinesCapacity - activeLinesCapacity;

            // Round results
            activeLinesCapacity = decimal.Round(activeLinesCapacity, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);
            inactiveLinesCapacity = decimal.Round(inactiveLinesCapacity, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);

            // Build Data points
            DecimalSeriesDTO activeBatchesCapacitySeries = new DecimalSeriesDTO()
            {
                Name = this.localizer[StatsLang.ActiveBatchesCapacity].Value,
                Data = new List<decimal?> { activeLinesCapacity },
                ShowInLegend = true,
                Visible = true,
                Stack = "capacity",
                Stacking = "normal",
                Type = "bar",
                YAxis = 0,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + this.localizer[StatsLang.Hens].Value }
            };
            DecimalSeriesDTO inactiveBatchesCapacitySeries = new DecimalSeriesDTO()
            {
                Name = this.localizer[StatsLang.InactiveBatchesCapacity].Value,
                Data = new List<decimal?> { inactiveLinesCapacity },
                ShowInLegend = true,
                Visible = true,
                Stack = "capacity",
                Stacking = "normal",
                Type = "bar",
                YAxis = 0,
                ToolTip = new ToolTipDTO() { ValueSuffix = ' ' + this.localizer[StatsLang.Hens].Value }
            };

            // Build series with datapoints
            return new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[StatsLang.CapacityChartTitle],
                Series = new List<DecimalSeriesDTO>
                {
                    inactiveBatchesCapacitySeries,
                    activeBatchesCapacitySeries,
                },
                YAxis = new List<YAxis>() { new YAxis() { Title = new TitleDTO() { Text = this.localizer[StatsLang.CapacityChartYAxisLabel] } } },
                NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage],
                XAxisLabel = " ",
                Categories = new List<string>() { " " }
            };
        }

        ///<summary>
        /// Build DTO for hen genetic distribution pie chart.
        ///</summary>
        public PieChartDTO HenGeneticDistribution(HenStage? henStage = null)
        {
            // Get batches
            IQueryable<HenBatch> batches = this.henBatchService.GetAllFull();
            if (henStage.HasValue)
                batches = batches.Where(hb => hb.HenStage == henStage);

            //EF core does not allow for grouping when an Include() clause is executed before. 
            //We were force to do a Select() after the Include() for the GroupBy() to be run in SQL and not in C#
            List<HenAmountByGeneticDTO> batchesGroups = batches.Where(hb => hb.ParentId == null && hb.DateEnd == null && hb.HenAmountFemale > 0 && hb.DateStart.HasValue && hb.DateStart.Value.Date <= DateTime.Today.Date)
                .Select(g => new { GeneticName = g.Genetic.Name, g.GeneticId, HenAmount = g.HenAmountFemale + g.HenAmountMale })
                .GroupBy(g => g.GeneticId).Select(b => new HenAmountByGeneticDTO
                {
                    HenAmount = b.Sum(s => (double)s.HenAmount),
                    GeneticName = b.Min(g => g.GeneticName)
                }).ToList();

            PieSeriesDTO series = new PieSeriesDTO() { Name = this.localizer[StatsLang.GeneticsChartParameter].Value, Data = new List<object>(), ToolTip = new ToolTipDTO() { ValueSuffix = this.localizer[StatsLang.Hens] } };

            // Build DTO
            foreach (HenAmountByGeneticDTO group in batchesGroups)
                series.Data.Add(new object[] { group.GeneticName, group.HenAmount });
            // show every legend
            series.ShowInLegend = true;
            return new PieChartDTO { Title = this.localizer[StatsLang.GeneticsChartTitle], Series = new List<PieSeriesDTO> { series }, NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage] };
        }

        ///<summary>
        /// Build DTO for hen distribution sunburst chart. Groups by genetic and egg type.
        ///</summary>
        public DashboardSunburstChartDTO HenDistribution(HenStage? henStage = null)
        {
            // Get data
            List<HenBatchSunburstChartDTO> henBatches = this.henBatchService.GetAll()
                .Where(hb =>
                    hb.DateEnd == null &&
                    hb.LineId != null &&
                    (!henStage.HasValue || hb.HenStage == henStage))
                .Select(hb => new HenBatchSunburstChartDTO()
                {
                    Id = hb.Id,
                    Code = hb.Code,
                    GeneticId = hb.GeneticId,
                    GeneticName = hb.Genetic.Name,
                    HenAmountFemale = hb.HenAmountFemale,
                    HenAmountMale = hb.HenAmountMale,
                    FarmId = hb.FarmId,
                    FarmCode = hb.Farm.Code,
                    ParentId = hb.ParentId,
                    ParentCode = hb.Parent.Code,
                    LineId = hb.LineId,
                    LineCode = hb.Line.Code,
                    WarehouseId = hb.Line.WarehouseId,
                    WarehouseCode = hb.Line.Warehouse.Code,
                })
                .ToList();

            // Initalize level sub indexes and series data DTO
            int level1SubIndex = 1;
            int level2SubIndex = 1;
            int level3SubIndex = 1;
            int level4SubIndex = 1;
            int level5SubIndex = 1;
            int level6SubIndex = 1;
            List<SunburstSeriesDTO> series = new List<SunburstSeriesDTO>();

            // Center level
            string id_center = "0.0";
            string parent = "";
            series.Add(new SunburstSeriesDTO
            {
                Name = this.localizer[Lang.HenDistributionChartCenterLabel],
                Id = id_center,
                Parent = parent,
                DisplayValue = henBatches.Sum(hb => (double)(hb.HenAmountFemale + hb.HenAmountMale)).ToString(DashboardDecimalPrecision.NoDecimal)
            });

            if (henBatches.Count > 0)
            {
                IEnumerable<IGrouping<Guid?, HenBatchSunburstChartDTO>> level1Groups = henBatches
                    .Select(hb => new HenBatchSunburstChartDTO
                    {
                        KeyId = hb.GeneticId,
                        KeyName = hb.GeneticName,
                        Value = hb.HenAmountFemale + hb.HenAmountMale
                    })
                    .GroupBy(hb => hb.KeyId);

                foreach (IGrouping<Guid?, HenBatchSunburstChartDTO> level1Element in level1Groups) // Genetic level
                {
                    // Level specific names and indexes
                    int level = 1;
                    string id_level1 = level.ToString() + "." + level1SubIndex.ToString();
                    parent = id_center;

                    // Build data
                    series.Add(new SunburstSeriesDTO
                    {
                        Name = localizer[Lang.HenDistributionChartGeneticLevelPrefixLabel] + " " + level1Element.Min(g => g.KeyName),
                        Id = id_level1,
                        Parent = parent,
                        Value = level1Element.Sum(g => g.Value),
                        DisplayValue = level1Element.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                    });

                    // Generate child grouping
                    IEnumerable<IGrouping<Guid?, HenBatchSunburstChartDTO>> level2Groups = henBatches
                        .Where(hb => hb.GeneticId == level1Element.Key)
                        .Select(hb => new HenBatchSunburstChartDTO
                        {
                            KeyId = hb.FarmId,
                            KeyName = hb.FarmCode ?? "",
                            Value = hb.HenAmountFemale + hb.HenAmountMale
                        })
                        .GroupBy(hb => hb.KeyId);

                    if (level2Groups != null && level2Groups.Any())
                    {
                        foreach (IGrouping<Guid?, HenBatchSunburstChartDTO> level2Element in level2Groups)  // Farm level
                        {
                            // Level specific names and indexes
                            level = 2;
                            string id_level2 = level.ToString() + "." + level2SubIndex.ToString();
                            parent = id_level1;

                            // Build data
                            series.Add(new SunburstSeriesDTO
                            {
                                Name = localizer[Lang.HenDistributionChartFarmLevelPrefixLabel] + " " + level2Element.Min(g => g.KeyName),
                                Id = id_level2,
                                Parent = id_level1,
                                Value = level2Element.Sum(g => g.Value),
                                DisplayValue = level2Element.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                            });

                            // Generate child grouping
                            IEnumerable<IGrouping<Guid?, HenBatchSunburstChartDTO>> level3Groups = henBatches
                                .Where(hb => hb.FarmId == level2Element.Key)
                                .Select(hb => new HenBatchSunburstChartDTO
                                {
                                    KeyId = hb.ParentId.HasValue ? hb.ParentId.Value : hb.Id,
                                    KeyName = hb.ParentCode ?? hb.Code,
                                    Value = hb.HenAmountFemale + hb.HenAmountMale
                                })
                                .GroupBy(hb => hb.KeyId);

                            if (level3Groups != null && level3Groups.Any())
                            {
                                foreach (IGrouping<Guid?, HenBatchSunburstChartDTO> level3Element in level3Groups)  // Batch level
                                {
                                    // Level specific names and indexes
                                    level = 3;
                                    string id_level3 = level.ToString() + "." + level3SubIndex.ToString();
                                    parent = id_level2;

                                    // Build data
                                    series.Add(new SunburstSeriesDTO
                                    {
                                        Name = localizer[Lang.HenDistributionChartBatchLevelPrefixLabel] + " " + level3Element.Min(g => g.KeyName),
                                        Id = id_level3,
                                        Parent = id_level2,
                                        Value = level3Element.Sum(g => g.Value),
                                        DisplayValue = level3Element.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                                    });

                                    // Generate child grouping
                                    IEnumerable<IGrouping<Guid?, HenBatchSunburstChartDTO>> level4Groups = henBatches
                                        .Where(hb => hb.Id == level3Element.Key || hb.ParentId == level3Element.Key)
                                        .Select(hb => new HenBatchSunburstChartDTO
                                        {
                                            KeyId = hb.WarehouseId,
                                            KeyName = hb.WarehouseCode,
                                            Value = hb.HenAmountFemale + hb.HenAmountMale
                                        })
                                        .GroupBy(hb => hb.KeyId);

                                    if (level4Groups != null && level4Groups.Any())
                                    {
                                        foreach (IGrouping<Guid?, HenBatchSunburstChartDTO> level4Element in level4Groups)  // Warehouse level
                                        {
                                            // Level specific names and indexes
                                            level = 4;
                                            string id_level4 = level.ToString() + "." + level4SubIndex.ToString();
                                            parent = id_level3;

                                            // Build data
                                            series.Add(new SunburstSeriesDTO
                                            {
                                                Name = localizer[Lang.HenDistributionChartWarehouseLevelPrefixLabel] + " " + level4Element.Min(g => g.KeyName),
                                                Id = id_level4,
                                                Parent = id_level3,
                                                Value = level4Element.Sum(g => g.Value),
                                                DisplayValue = level4Element.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                                            });

                                            // Generate child grouping
                                            IEnumerable<IGrouping<Guid?, HenBatchSunburstChartDTO>> level5Groups = henBatches
                                                .Where(hb => hb.WarehouseId == level4Element.Key)
                                            .Select(hb => new HenBatchSunburstChartDTO
                                            {
                                                KeyId = hb.LineId,
                                                KeyName = hb.LineCode,
                                                Value = hb.HenAmountFemale + hb.HenAmountMale
                                            })
                                            .GroupBy(hb => hb.KeyId);

                                            if (level5Groups != null && level5Groups.Any())
                                            {
                                                foreach (IGrouping<Guid?, HenBatchSunburstChartDTO> level5Element in level5Groups)  // Line level
                                                {
                                                    // Level specific names and indexes
                                                    level = 5;
                                                    string id_level5 = level.ToString() + "." + level5SubIndex.ToString();
                                                    parent = id_level4;

                                                    // Build data
                                                    series.Add(new SunburstSeriesDTO
                                                    {
                                                        Name = localizer[Lang.HenDistributionChartLineLevelPrefixLabel] + " " + level5Element.Min(g => g.KeyName),
                                                        Id = id_level5,
                                                        Parent = id_level4,
                                                        Value = level5Element.Sum(g => g.Value),
                                                        DisplayValue = level5Element.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                                                    });

                                                    // Generate child grouping
                                                    var males = henBatches
                                                        .Where(hb => hb.LineId == level5Element.Key)
                                                        .Select(hb => new
                                                        {
                                                            KeyId = hb.Id,
                                                            KeyName = hb.Code,
                                                            Value = hb.HenAmountMale,
                                                        });

                                                    var females = henBatches
                                                        .Where(hb => hb.LineId == level5Element.Key)
                                                        .Select(hb => new
                                                        {
                                                            KeyId = hb.Id,
                                                            KeyName = hb.Code,
                                                            Value = hb.HenAmountFemale,
                                                        });

                                                    // Level specific names and indexes
                                                    level = 6;
                                                    string id_level6 = level.ToString() + "." + level6SubIndex.ToString();
                                                    parent = id_level5;

                                                    // Build data
                                                    series.Add(new SunburstSeriesDTO
                                                    {
                                                        Name = males.Min(g => g.KeyName) + " machos",
                                                        Id = id_level6,
                                                        Parent = id_level5,
                                                        Value = males.Sum(g => g.Value),
                                                        DisplayValue = males.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                                                    });

                                                    level6SubIndex++;
                                                    id_level6 = level.ToString() + "." + level6SubIndex.ToString();

                                                    series.Add(new SunburstSeriesDTO
                                                    {
                                                        Name = females.Min(g => g.KeyName) + " femeas",
                                                        Id = id_level6,
                                                        Parent = id_level5,
                                                        Value = females.Sum(g => g.Value),
                                                        DisplayValue = females.Sum(g => g.Value).ToString(DashboardDecimalPrecision.NoDecimal)
                                                    });

                                                    level6SubIndex++;

                                                    level5SubIndex++;
                                                }
                                            }
                                            level4SubIndex++;
                                        }
                                    }
                                    level3SubIndex++;
                                }
                            }
                            level2SubIndex++;
                        }
                    }
                    level1SubIndex++;
                }
            }
            return new DashboardSunburstChartDTO { Series = series, Title = this.localizer[Lang.HenDistributionChartTitle], NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage] };
        }
        ///<summary>
        /// Generate hen batch age histogram with 5 custom bins
        ///</summary>
        public DashboardLineOrBarChartDTO HenBatchesAge(FilterDataDTO data, bool noStage = false)
        {
            // Parse histogram bin filter input
            data = chartFunctions.ParseHistogramBins(data);

            // Initialize DTO
            DashboardLineOrBarChartDTO model = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[StatsLang.HenBatchAgeChartTitle],
                XAxisLabel = this.localizer[StatsLang.HenBatchAgeChartXAxisLabel],
                Series = new List<DecimalSeriesDTO>(),
                Categories = new List<string>(),
                YAxis = new List<YAxis>() { new YAxis() { Title = new TitleDTO() { Text = this.localizer[StatsLang.HenBatchAgeChartAmountLabel] } } }
            };

            // Histogram bin labels
            string limit1 = data.HenBatchHistogramWeekNumberBinLimit1.ToString();
            string limit2 = data.HenBatchHistogramWeekNumberBinLimit2.ToString();
            string limit3 = data.HenBatchHistogramWeekNumberBinLimit3.ToString();
            string limit4 = data.HenBatchHistogramWeekNumberBinLimit4.ToString();
            model.Categories.Add("< " + limit1);
            model.Categories.Add(">= " + limit1 + " | " + " <" + limit2);
            model.Categories.Add(">= " + limit2 + " | " + " <" + limit3);
            model.Categories.Add(">= " + limit3 + " | " + " <" + limit4);
            model.Categories.Add(">= " + limit4);

            model.NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage];
            model.Series.Add(new DecimalSeriesDTO() { Name = this.localizer[StatsLang.HenBatchAgeChartAmountLabel], Data = new List<decimal?>(), Type = "column", Stacking = "normal", ToolTip = new ToolTipDTO() { ValueSuffix = " " }, ShowInLegend = true, Visible = true });
            model.Series.Add(new DecimalSeriesDTO() { Name = this.localizer[StatsLang.HenBatchAgeChartPercentLabel], Data = new List<decimal?>(), Type = "column", Stacking = "normal", ToolTip = new ToolTipDTO() { ValueSuffix = " " }, ShowInLegend = false, Visible = false });

            // Get hen batches
            IEnumerable<HenBatchStatusDTO> henBatches = chartFunctions.GetHenBatchesStatusData(data.HenStage, noStage, true);

            if (henBatches != null && henBatches.Count() > 0)
            {
                // Histogram bins
                List<List<HenBatchStatusDTO>> bins = new List<List<HenBatchStatusDTO>>()
                {
                    henBatches.Where(w => w.Age < data.HenBatchHistogramWeekNumberBinLimit1).ToList(),
                    henBatches.Where(w => w.Age >= data.HenBatchHistogramWeekNumberBinLimit1 && w.Age < data.HenBatchHistogramWeekNumberBinLimit2).ToList(),
                    henBatches.Where(w => w.Age >= data.HenBatchHistogramWeekNumberBinLimit2 && w.Age < data.HenBatchHistogramWeekNumberBinLimit3).ToList(),
                    henBatches.Where(w => w.Age >= data.HenBatchHistogramWeekNumberBinLimit3 && w.Age < data.HenBatchHistogramWeekNumberBinLimit4).ToList(),
                    henBatches.Where(w => w.Age >= data.HenBatchHistogramWeekNumberBinLimit4).ToList()
                };

                // Build data
                List<decimal> henAmounts = bins.Select(b => b.Sum(hbsd => hbsd.HenAmount)).ToList();
                List<decimal> batchCounts = bins.Select(b => (decimal)b.Count()).ToList();
                List<decimal> batchPercentage = batchCounts.Select(b => (decimal)100 * b / henBatches.Count()).ToList();

                // Round percentages
                for (int i = 0; i < batchPercentage.Count(); i++)
                    batchPercentage[i] = decimal.Round(batchPercentage[i], DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);

                // Build DTO
                model.Series[0].Data = batchCounts.Select(d => d == 0 ? (decimal?)null : d).ToList();
                model.Series[1].Data = batchPercentage.Select(d => d == 0 ? (decimal?)null : d).ToList(); ;
            }

            return model;
        }

        /// <summary>
        /// Get a DTO with chart configuration and a default data series and get a list of
        /// lists of hen reports for a hen batch so dynamic grouping can be done client side.
        /// Time filtering is server side
        /// </summary>
        public Tuple<List<List<HenBatchStatusDTO>>, DashboardLineOrBarChartDTO> PerformanceChart(List<Guid> henBatchIds, DateTime minDate, DateTime maxDate, HenStage? henStage = null)
        {
            // Initialize chart DTO
            DashboardLineOrBarChartDTO dto = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[Lang.PerformanceChartTitle],
                XAxisLabel = this.localizer[Lang.PerformanceChartXAxisLabel],
                Series = new List<DecimalSeriesDTO>(),
                YAxis = new List<YAxis>(),
                Categories = new List<string>(),
                NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage]
            };
            ;

            // Units symbol
            string eggsUnitSymbol = this.capacityUnitService.Get(CapacityUnits.Units).Symbol;
            //GAD symbol
            string feedIntakeUnitSymbol = this.localizer[Lang.PerformanceChartGADSymbol];

            // Get hen reports which are new (not reverted) from hen batches of the selected stage
            // between date bounds making shure to consider only date and not time.
            IQueryable<HenReport> henReports = this.henReportService.GetAllFullForDashboard()
                .Where(hr => henBatchIds.Contains(hr.HenBatchId) && hr.ReportEnum == ReportEnum.New && minDate.Date <= hr.Date.Date && hr.Date.Date <= maxDate.Date);

            bool validHenStage = henStage.HasValue;

            List<HenBatchStatusDTO> henReportDTOs = henReports
                .Select(hr => new HenBatchStatusDTO
                {
                    DateStart = hr.Date.Date,
                    HenAmount = hr.HenAmountFemale + hr.HenAmountMale,
                    HenAmountFemale = hr.HenAmountFemale,
                    HenAmountMale = hr.HenAmountMale,
                    Dead = hr.DeadFemale + hr.DeadMale,
                    DeadFemale = hr.DeadFemale,
                    DeadMale = hr.DeadMale,
                    FeedIntakeFemale = hr.FeedIntakeFemale,
                    FeedIntakeMale = hr.FeedIntakeMale,
                    Eggs = hr.TotalEggs, //a definir
                    HatchableEggs = hr.HatchableEggs,
                    CommercialEggs = hr.CommercialEggs,
                    BrokenEggs = hr.BrokenEggs,
                    Id = hr.HenBatchId,
                    HenBatchName = hr.HenBatch.DetailedName,
                    LineId = hr.HenBatch.LineId.Value,
                    LineName = hr.HenBatch.Line.DetailedName,
                    WarehouseId = hr.HenBatch.Line.WarehouseId,
                    WarehouseName = hr.HenBatch.Line.Warehouse.DetailedName,
                    ClusterId = hr.HenBatch.Line.Warehouse.ClusterId,
                    ClusterName = hr.HenBatch.Line.Warehouse.Cluster.Name,
                    FarmId = hr.HenBatch.Line.Warehouse.Cluster.Farm.Id,
                    FarmName = hr.HenBatch.Line.Warehouse.Cluster.Farm.Name
                })
                .ToList();

            // We can't be sure there is only one hen report for every day
            // for a hen batch. Hence, accumulate data for every hen batch at
            // every date

            henReportDTOs = henReportDTOs
                .GroupBy(hr => new { hr.DateStart, hr.Id })
                .Select(g => new HenBatchStatusDTO
                {
                    DateStart = g.Key.DateStart,
                    HenAmount = g.Min(hr => hr.HenAmount),
                    HenAmountFemale = g.Min(hr => hr.HenAmountFemale),
                    HenAmountMale = g.Min(hr => hr.HenAmountMale),
                    Dead = g.Sum(hr => hr.Dead),
                    DeadFemale = g.Sum(hr => hr.DeadFemale),
                    DeadMale = g.Sum(hr => hr.DeadMale),
                    FeedIntakeFemale = g.Sum(hr => hr.FeedIntakeFemale),
                    FeedIntakeMale = g.Sum(hr => hr.FeedIntakeMale),
                    Eggs = decimal.Round(g.Sum(hr => hr.Eggs)),
                    HatchableEggs = (uint)g.Sum(hr => hr.HatchableEggs),
                    CommercialEggs = (uint)g.Sum(hr => hr.CommercialEggs),
                    BrokenEggs = (uint)g.Sum(hr => hr.BrokenEggs),
                    Id = g.Key.Id,
                    HenBatchName = g.Min(hr => hr.HenBatchName),
                    LineId = g.Min(hr => hr.LineId),
                    LineName = g.Min(hr => hr.LineName),
                    WarehouseId = g.Min(hr => hr.WarehouseId),
                    WarehouseName = g.Min(hr => hr.WarehouseName),
                    ClusterId = g.Min(hr => hr.ClusterId),
                    ClusterName = g.Min(hr => hr.ClusterName),
                    FarmId = g.Min(hr => hr.FarmId),
                    FarmName = g.Min(hr => hr.FarmName)
                })
                .ToList();

            // Vertical axis
            if (!validHenStage || (validHenStage && henStage == HenStage.Laying))
            {
                // Production axis
                dto.YAxis.Add(new YAxis()
                {
                    Min = 0,
                    GridLineWidth = 1,
                    Opposite = false,
                    Title = new TitleDTO() { Text = this.localizer[Lang.PerformanceChartProductionAxisLabel] + " (" + eggsUnitSymbol + ")" }
                });
            }
            // Dead axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.PerformanceChartDeadAxisLabel] }
            });
            // Hen Amount axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.PerformanceChartHenAmountAxisLabel] }
            });
            // Feed Intake axis
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = false,
                Title = new TitleDTO() { Text = this.localizer[Lang.PerformanceChartFeedIntakeAxisLabel] + " (" + feedIntakeUnitSymbol + ")" },
                UnitSymbol = feedIntakeUnitSymbol
            });
            // Moratality and viability
            dto.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[Lang.PerformanceChartMortalityAndViabilityAxisLabel] },
                UnitSymbol = "%"
            });

            // Horizontal axis
            List<DateTime> dateTimeAxis = chartFunctions.BuildDateTimeAxis(minDate, maxDate.AddDays(1));
            foreach (DateTime date in dateTimeAxis)
                dto.Categories.Add(date.ToShortDateString());

            #region Generate data for client side grouping
            // Initialize list of lists
            List<List<HenBatchStatusDTO>> series = new List<List<HenBatchStatusDTO>>();

            // Group by date
            List<IGrouping<DateTime, HenBatchStatusDTO>> groupedHenReportDTOs = henReportDTOs
                .GroupBy(hr => hr.DateStart).ToList();
            // build list
            foreach (DateTime date in dateTimeAxis)
            {
                // Extract data from IGrouping element
                List<HenBatchStatusDTO> group = groupedHenReportDTOs.Where(g => g.Key == date).SelectMany(hr => hr).ToList();
                // Initialize hen batch data series
                series.Add(new List<HenBatchStatusDTO>());
                // Add every hen batch data to that specific date
                foreach (HenBatchStatusDTO hr in group)
                    series.Last().Add(hr);
            }
            #endregion

            // Initialize data lists
            List<decimal> henAmount = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> henAmountFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> henAmountMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> deadFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> deadMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> mortalityFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> mortalityMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> viabilityFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> viabilityMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> feedIntakeFemale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> feedIntakeMale = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> hatchableEggs = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> commercialEggs = new List<decimal>(new decimal[dateTimeAxis.Count()]);
            List<decimal> brokenEggs = new List<decimal>(new decimal[dateTimeAxis.Count()]);

            for (int i = 0; i < series.Count; i++)
            {
                henAmount[i] = series[i].Sum(hr => hr.HenAmount);
                henAmountFemale[i] = series[i].Sum(hr => hr.HenAmountFemale);
                henAmountMale[i] = series[i].Sum(hr => hr.HenAmountMale);
                deadFemale[i] = series[i].Sum(hr => hr.DeadFemale);
                deadMale[i] = series[i].Sum(hr => hr.DeadMale);
                mortalityFemale[i] = series[i].Sum(hr => hr.DeadFemale) + series[i].Sum(hr => hr.HenAmountFemale) != 0 ? decimal.Round((100 * series[i].Sum(hr => hr.DeadFemale)) / (series[i].Sum(hr => hr.DeadFemale) + series[i].Sum(hr => hr.HenAmountFemale)), 2) : 0;
                mortalityMale[i] = series[i].Sum(hr => hr.DeadMale) + series[i].Sum(hr => hr.HenAmountMale) != 0 ? decimal.Round((100 * series[i].Sum(hr => hr.DeadMale)) / (series[i].Sum(hr => hr.DeadMale) + series[i].Sum(hr => hr.HenAmountMale)), 2) : 0;
                viabilityFemale[i] = 100 - mortalityFemale[i];
                viabilityMale[i] = 100 - mortalityMale[i];
                feedIntakeFemale[i] = series[i].Sum(hr => hr.HenAmountFemale) != 0 ? series[i].Sum(hr => hr.FeedIntakeFemale) / series[i].Sum(hr => hr.HenAmountFemale) * 1000 : 0;
                feedIntakeMale[i] = series[i].Sum(hr => hr.HenAmountMale) != 0 ? series[i].Sum(hr => hr.FeedIntakeMale) / series[i].Sum(hr => hr.HenAmountMale) * 1000 : 0;
                hatchableEggs[i] = series[i].Sum(hr => hr.HatchableEggs);
                commercialEggs[i] = series[i].Sum(hr => hr.CommercialEggs);
                brokenEggs[i] = series[i].Sum(hr => hr.BrokenEggs);
            }

            if (!validHenStage || (validHenStage && henStage == HenStage.Laying))
            {
                dto.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[Lang.PerformanceChartHatchableEggsSeriesName],
                    Data = hatchableEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    Stack = "Eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    Type = "column",
                    ShowInLegend = true,
                    Visible = false,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggsUnitSymbol }
                });
                dto.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[Lang.PerformanceChartCommercialEggsSeriesName],
                    Data = commercialEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    Stack = "Eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    Type = "column",
                    Visible = false,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggsUnitSymbol }
                });
                dto.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[Lang.PerformanceChartBrokenEggsSeriesName],
                    Data = brokenEggs.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    Stack = "Eggs",
                    Stacking = "normal",
                    YAxis = 0,
                    Type = "column",
                    Visible = false,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggsUnitSymbol }
                });
            }

            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartMortalityFemaleSeriesName],
                Data = mortalityFemale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "Mortality",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartViabilityFemaleSeriesName],
                Data = viabilityFemale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "Viability",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartMortalityMaleSeriesName],
                Data = mortalityMale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "Mortality",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartViabilityMaleSeriesName],
                Data = viabilityMale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "Viability",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 4 : 3,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartDeadFemaleSeriesName],
                Data = deadFemale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "Dead",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 1 : 0,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartDeadMaleSeriesName],
                Data = deadMale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "Dead",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 1 : 0,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartHenAmountSeriesName],
                Data = henAmount.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "HenAmount",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 2 : 1,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartHenAmountFemaleSeriesName],
                Data = henAmountFemale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "HenAmountFemale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 2 : 1,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartHenAmountMaleSeriesName],
                Data = henAmountMale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "HenAmountMale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 2 : 1,
                Type = "spline",
                Visible = false,
                ToolTip = new ToolTipDTO() { ValueSuffix = "" }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartFeedIntakeFemaleSeriesName],
                Data = feedIntakeFemale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "FeedIntakeFemale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 3 : 2,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = feedIntakeUnitSymbol }
            });
            dto.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[Lang.PerformanceChartFeedIntakeMaleSeriesName],
                Data = feedIntakeMale.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                Stack = "FeedIntakeMale",
                YAxis = (!validHenStage || (validHenStage && henStage == HenStage.Laying)) ? 3 : 2,
                Type = "spline",
                ToolTip = new ToolTipDTO() { ValueSuffix = feedIntakeUnitSymbol }
            });


            // If all data is zero, clear lists so no available data message pops up
            if (dto.Series.All(s => s.Data.All(p => p == 0)))
                foreach (DecimalSeriesDTO data in dto.Series)
                    data.Data.Clear();

            return new Tuple<List<List<HenBatchStatusDTO>>, DashboardLineOrBarChartDTO>(series, dto);
        }

        /// <summary>
        /// Build DTO for Laying Dashboard hen batch performance chart.
        /// </summary>
        public DashboardLineOrBarChartDTO HenBatchPerformance(HenStage henStage, DateTime minDate, DateTime maxDate, List<Guid> henBatchIds = null)
        {
            // Initialize DTO
            DashboardLineOrBarChartDTO model = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[StatsLang.HenBatchPerformanceChartTitle],
                XAxisLabel = this.localizer[StatsLang.HenBatchPerformanceXAxisLabel],
                Series = new List<DecimalSeriesDTO>()
            };

            // Variables to convert from Kg to tonnes
            CapacityUnit feedIntakeCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Tonnes);
            decimal feedIntakeRelativeValue = Convert.ToDecimal(feedIntakeCapacityUnit.RelativeValue);
            string feedIntakeUnitSymbol = feedIntakeCapacityUnit.Symbol;
            // Units to convert to grams
            CapacityUnit feedIntakePerHenCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Grams);
            decimal feedIntakePerHenRelativeValue = Convert.ToDecimal(feedIntakePerHenCapacityUnit.RelativeValue);
            string feedIntakePerHenUnitSymbol = feedIntakePerHenCapacityUnit.Symbol;
            // Variables to convert units to boxes
            CapacityUnit eggCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Box);
            decimal eggRelativeValue = Convert.ToDecimal(eggCapacityUnit.RelativeValue);
            string eggUnitSymbol = this.localizer[StatsLang.EggCapacityUnitBoxesPluralSymbol];

            // Vertical axis
            model.YAxis = new List<YAxis>();
            // Eggs of feed intake per hen axis
            model.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 1,
                Opposite = false,
                Title = new TitleDTO() { Text = henStage == HenStage.Laying ? (this.localizer[StatsLang.HenBatchPerformanceEggsChartParameter].Value + " (" + this.localizer[StatsLang.EggCapacityUnitBoxesPluralSymbol] + ")") : (this.localizer[StatsLang.FeedIntakeChartTitle].Value + " (" + this.localizer[StatsLang.FeedIntakePerHenPerDayUnitSymbol] + ")") },
            });
            // Hen amount axis
            model.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[StatsLang.HenBatchPerformanceHenAmountChartParameter].Value }
            });
            // Feed intake axis
            model.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[StatsLang.HenBatchPerformanceFeedIntakeChartParameter].Value },
                UnitSymbol = feedIntakeUnitSymbol
            });
            // Weighted average birds axis
            model.YAxis.Add(new YAxis()
            {
                Min = 0,
                GridLineWidth = 0,
                Opposite = true,
                Title = new TitleDTO() { Text = this.localizer[StatsLang.HenBatchPerformanceWeightedAverageBirdsParameter].Value },
                UnitSymbol = "%"
            });


            // Time axis
            // The chart is in weeks, hence 7 days in a week (in a time step)
            int stepInDays = 7;
            minDate = minDate.AddDays(chartFunctions.DaysFromStartOfWeek(minDate.DayOfWeek, CultureInfo.CurrentCulture.DateTimeFormat.FirstDayOfWeek));
            maxDate = maxDate.AddDays(chartFunctions.DaysFromStartOfWeek(maxDate.DayOfWeek, CultureInfo.CurrentCulture.DateTimeFormat.FirstDayOfWeek) + stepInDays);
            List<DateTime> timeAxis = chartFunctions.BuildDateTimeAxis(minDate, maxDate, stepInDays);
            // List to match farm weeks to calendar weeks
            DayOfWeek farmDayOfWeek = chartFunctions.GetFarmDayOfWeek();
            minDate = minDate.AddDays(chartFunctions.DaysFromStartOfWeek(minDate.DayOfWeek, farmDayOfWeek));
            maxDate = maxDate.AddDays(chartFunctions.DaysFromStartOfWeek(maxDate.DayOfWeek, farmDayOfWeek));
            List<DateTime> weekAxis = chartFunctions.BuildDateTimeAxis(minDate, maxDate, stepInDays);

            // Initialize data lists
            List<decimal> eggsData = new List<decimal>(new decimal[timeAxis.Count()]);
            List<decimal> henAmountData = new List<decimal>(new decimal[timeAxis.Count()]);
            List<decimal> feedIntakeFemaleData = new List<decimal>(new decimal[timeAxis.Count()]);
            List<decimal> feedIntakeMaleData = new List<decimal>(new decimal[timeAxis.Count()]);
            List<decimal> idealProductionData = new List<decimal>(new decimal[timeAxis.Count()]);
            List<decimal> feedIntakePerHenData = new List<decimal>(new decimal[timeAxis.Count()]);
            List<decimal> weightedAverageBirdsData = new List<decimal>(new decimal[timeAxis.Count()]);
            model.Categories = new List<string>(new string[timeAxis.Count()]);

            // Get hen batch performance data
            IQueryable<HenBatchPerformance> dataQuery = this.henBatchPerformanceService.GetAll()
                .Where(hbp => weekAxis.Min() <= hbp.Date.Date && hbp.Date.Date <= weekAxis.Max() && hbp.HenBatch.HenStage == henStage);

            if (henBatchIds != null && henBatchIds.Any())
                dataQuery = dataQuery.Where(hbp => henBatchIds.Contains(hbp.HenBatchId));

            List<GenericPerformanceDTO> data = dataQuery
                .Select(hbp => new GenericPerformanceDTO
                {
                    Date = hbp.Date.GetPastSelectedDay(farmDayOfWeek).Date,
                    HenBatchId = hbp.HenBatchId,
                    HenAmount = hbp.HenAmountFemale - hbp.DepopulateFemaleAccum + hbp.HenAmountMale - hbp.DepopulateMaleAccum,
                    InitialHenAmount = hbp.HenAmountFemale - hbp.DepopulateFemaleAccum + hbp.DepopulateFemale + hbp.HenAmountMale - hbp.DepopulateMaleAccum + hbp.DepopulateMale,
                    Eggs = hbp.TotalEggs,
                    FeedIntakeFemale = hbp.FeedIntakeFemale,
                    FeedIntakeMale = hbp.FeedIntakeMale,
                    GeneticId = hbp.GeneticId,
                    Week = hbp.WeekNumber,
                    BatchWeekNumber = hbp.HenBatch.BatchWeekNumber + hbp.WeekNumber + (DateTime.Today.Date - hbp.HenBatch.DateStart.Value.GetPastSelectedDay(hbp.HenBatch.Farm.DayOfWeek)).Days / 7,
                })
                .ToList();

            if (data != null && data.Any())
            {
                // Get genetic parameter reference data
                List<GeneticReferenceDTO> geneticParameters = new List<GeneticReferenceDTO>();
                if (henStage == HenStage.Laying)
                {
                    int maxWeek = data.Max(d => d.Week);
                    int minWeek = data.Min(d => d.Week);
                    IEnumerable<Guid> genetics = data.Select(d => d.GeneticId);
                    geneticParameters = this.geneticsParameterService.GetAll()
                        .Where(gpr => minWeek <= gpr.TimePeriodValue && gpr.TimePeriodValue <= maxWeek && genetics.Contains(gpr.GeneticsId))
                        .Select(gprd => new GeneticReferenceDTO
                        {
                            GeneticId = gprd.GeneticsId,
                            Week = gprd.TimePeriodValue,
                        })
                        .OrderBy(gprd => gprd.Week)
                        .ToList();


                    foreach (GenericPerformanceDTO hbpd in data)
                    {
                        GeneticReferenceDTO parameter = geneticParameters.Where(gpr => gpr.Week <= hbpd.Week && hbpd.GeneticId.Equals(gpr.GeneticId)).OrderByDescending(p => p.Week).FirstOrDefault();
                        if (parameter != null)
                            hbpd.HenDay = parameter.HenDayAVG;
                        else
                            hbpd.HenDay = (decimal)0.95;
                    }
                }

                // Build data lists
                List<IGrouping<Guid, GenericPerformanceDTO>> batchGroupedData = data.GroupBy(hbpd => hbpd.HenBatchId).ToList();
                decimal HenTotalAMount = data.Sum(hbpd => hbpd.HenAmount);
                //var dateBatchGroupedData = data.GroupBy(hbp => new { hbp.Date.Year, hbp.Week }).ToList();
                var dateBatchGroupedData = data.GroupBy(hbp => new { hbp.Date.Year, hbp.Week }).
                    Select(hbp => new
                    {
                        batchWeek = hbp.Sum(h => h.Week * h.HenAmount),
                        date = hbp.Select(h => h.Date).First()
                    }).ToList();

                foreach (var dateBatchGroup in dateBatchGroupedData)
                {
                    int index = weekAxis.FindIndex(a => a == dateBatchGroup.date);
                    if (index != -1)
                        weightedAverageBirdsData[index] += dateBatchGroup.batchWeek / HenTotalAMount;

                }

                foreach (IGrouping<Guid, GenericPerformanceDTO> batchGroup in batchGroupedData)
                {
                    foreach (GenericPerformanceDTO dto in batchGroup)
                    {
                        int index = weekAxis.FindIndex(a => a == dto.Date);
                        if (index != -1)
                        {
                            feedIntakeFemaleData[index] += decimal.Round(dto.FeedIntakeFemale / feedIntakeRelativeValue, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);

                            feedIntakeMaleData[index] += decimal.Round(dto.FeedIntakeMale / feedIntakeRelativeValue, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);

                            henAmountData[index] += dto.HenAmount;

                            if (henStage == HenStage.Laying)
                            {

                                eggsData[index] += (int)(dto.Eggs / eggRelativeValue);
                                idealProductionData[index] += (int)(dto.InitialHenAmount * dto.HenDay * 7 / eggRelativeValue);
                            }
                            else
                            {
                                decimal value = 0;


                                if (dto.HenAmount != 0)
                                    value = (dto.FeedIntakeFemale + dto.FeedIntakeMale) / (dto.HenAmount * (decimal)7 * feedIntakePerHenRelativeValue); //a definir

                                feedIntakePerHenData[index] += decimal.Round(value, DashboardDecimalPrecision.TwoDecimalPrecision, MidpointRounding.AwayFromZero);
                            }
                        }
                    }

                }
            }

            // Format time axis to show week number of year instead of date
            string cultureName = this.localizer[StatsLang.CultureName];
            for (int i = 0; i < timeAxis.Count(); i++)
                model.Categories[i] = chartFunctions.GetWeekOfYear(cultureName, timeAxis[i]).ToString();

            // Add data to DTO with corresponding plot types and unit symbols if needed
            if (henStage == HenStage.Laying)
            {
                model.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[StatsLang.HenBatchPerformanceEggsChartParameter].Value,
                    Type = "column",
                    Data = eggsData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    SeriesUnitsSymbol = eggUnitSymbol,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggUnitSymbol },
                    ShowInLegend = true,
                    YAxis = 0
                });
                model.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[StatsLang.HenBatchPerformanceIdealProductionChartParameter].Value,
                    Type = "column",
                    Data = idealProductionData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    SeriesUnitsSymbol = eggUnitSymbol,
                    ToolTip = new ToolTipDTO() { ValueSuffix = eggUnitSymbol },
                    ShowInLegend = true,
                    YAxis = 0
                });
            }
            else
            {
                model.Series.Add(new DecimalSeriesDTO
                {
                    Name = this.localizer[StatsLang.FeedIntakeChartTitle].Value,
                    Type = "spline",
                    Data = feedIntakePerHenData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    SeriesUnitsSymbol = this.localizer[StatsLang.FeedIntakePerHenPerDayUnitSymbol],
                    ToolTip = new ToolTipDTO() { ValueSuffix = this.localizer[StatsLang.FeedIntakePerHenPerDayUnitSymbol] },
                    ShowInLegend = true,
                    YAxis = 0
                });
            }

            model.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[StatsLang.HenBatchPerformanceHenAmountChartParameter].Value,
                Type = "spline",
                Data = henAmountData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                SeriesUnitsSymbol = this.localizer[StatsLang.Hens].Value,
                ToolTip = new ToolTipDTO() { ValueSuffix = this.localizer[StatsLang.Hens].Value },
                ShowInLegend = true,
                YAxis = 1
            });
            model.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[StatsLang.HenBatchPerformanceFeedIntakeFemaleChartParameter].Value,
                Type = henStage == HenStage.Laying ? "spline" : "column",
                Data = feedIntakeFemaleData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                SeriesUnitsSymbol = feedIntakeUnitSymbol,
                ToolTip = new ToolTipDTO() { ValueSuffix = feedIntakeUnitSymbol },
                ShowInLegend = true,
                YAxis = 2
            });
            model.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[StatsLang.HenBatchPerformanceFeedIntakeMaleChartParameter].Value,
                Type = henStage == HenStage.Laying ? "spline" : "column",
                Data = feedIntakeMaleData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                SeriesUnitsSymbol = feedIntakeUnitSymbol,
                ToolTip = new ToolTipDTO() { ValueSuffix = feedIntakeUnitSymbol },
                ShowInLegend = true,
                YAxis = 2
            });
            model.Series.Add(new DecimalSeriesDTO
            {
                Name = this.localizer[StatsLang.HenBatchPerformanceWeightedAverageBirdsParameter].Value,
                Type = "spline",
                Data = weightedAverageBirdsData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                SeriesUnitsSymbol = "%",
                ToolTip = new ToolTipDTO() { ValueSuffix = "%" },
                ShowInLegend = true,
                YAxis = 3
            });

            model.NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage];

            // If all data is zero, clear lists so no available data message pops up
            if (model.Series.All(s => s.Data.All(p => p == 0)))
                foreach (DecimalSeriesDTO series in model.Series)
                    series.Data.Clear();

            return model;
        }


        public (IEnumerable<IGrouping<Guid?, GenericSampleCageDTO>>? allReports, IQueryable<GeneticsParametersReference> geneticsParametersReference, int currentWeek) GetBreedingData(FilterDataDTO filters)
        {
            HenStage henStage = filters.HenStage.Value;
            bool uniformityChartIsShown = henStage == HenStage.Breeding && filters.HasAuthorizationSearaCharts;
            Guid? henBatchId = filters.HenBatchId;
            Guid? warehouseId = filters.WarehouseId;
            Guid? lineId = filters.LineId;
            Guid? cageId = filters.CageId;
            Guid? regionalId = filters.RegionalId;
            Guid? unitId = filters.UnitId;
            Guid? supervisorId = filters.SupervisorId;
            Guid? extensionistId = filters.ExtensionistId;

            //Company 1 : 1 Regional
            //Company 1 : 1 Supervisor
            //Company 1 : 1 Extensionist

            // Get hen batch data 
            DayOfWeek farmDayOfWeek = chartFunctions.GetFarmDayOfWeek();
            Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<SampleCageReport, HenBatchPerformance> querySampleCage = this.sampleCageReportService.GetAll()
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Line)
               .ThenInclude(line => line.Warehouse)
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Farm)
               .ThenInclude(farm => farm.Company)
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Genetic)
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.SampleCages)
               .Include(sampleCage => sampleCage.SampleCageMeasurement)
               .Include(sampleCage => sampleCage.HenBatchPerformance);


            IQueryable<SampleCageReport> query = !cageId.HasValue ? querySampleCage.Where(sampleCage => sampleCage.HenBatch.HenStage == henStage) :
                                          querySampleCage.Where(sampleCage => sampleCage.SampleCageMeasurement.Any(scm => scm.SampleCageId == cageId));
            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            bool hasHenBatchCategories = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ReportsHenWarehouseAndParentBatchWeightMeasurement && s.Value == "True");

            // Filter data 
            if (lineId.HasValue) // BOX
                query = query.Where(sampleCage => sampleCage.HenBatch.LineId == lineId.Value);
            else if (warehouseId.HasValue) // AVIARIO
                query = query.Where(sampleCage => sampleCage.HenBatch.Line.WarehouseId == warehouseId.Value && sampleCage.HenBatch.ParentId == henBatchId.Value);
            else if (henBatchId.HasValue) // LOTE
                query = query.Where(sampleCage => sampleCage.HenBatch.ParentId == henBatchId || sampleCage.HenBatchId == henBatchId);
            else if (extensionistId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.Farm.TechnicianId == extensionistId);
            else if (supervisorId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.Farm.SupervisorId == supervisorId);
            else if (unitId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.Farm.CompanyId == unitId);
            else if (regionalId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.Farm.Company.RegionalId == regionalId);

            if (filters != null)
            {
                if (filters.StartWeek.HasValue && filters.EndWeek.HasValue)
                {
                    query = query.Where(sampleCage => sampleCage.HenBatchPerformance.WeekNumber >= filters.StartWeek.Value && sampleCage.HenBatchPerformance.WeekNumber <= filters.EndWeek.Value);
                }
                else if (filters.MinDate.HasValue && filters.MaxDate.HasValue)
                {
                    query = query.Where(sampleCage => sampleCage.Date >= filters.MinDate.Value && sampleCage.Date <= filters.MaxDate.Value);
                }
            }

            if (!query.Any())
                return (null, null, 0);

            List<GenericSampleCageDTO> data = query.Select(sampleCage => new GenericSampleCageDTO()
            {
                GroupHenBatchId = sampleCage.HenBatch.ParentId.HasValue ? sampleCage.HenBatch.ParentId : sampleCage.HenBatchId,
                Date = sampleCage.Date.GetPastSelectedDay(farmDayOfWeek),
                Week = sampleCage.HenBatchPerformance.WeekNumber,
                HenBatchId = sampleCage.HenBatchId,
                HenBatchCode = sampleCage.HenBatch.Code,
                HenBatchFarmName = sampleCage.HenBatch.Farm.Name,
                HenBatchWarehauseName = sampleCage.HenBatch.LineId != null ? sampleCage.HenBatch.Line.Warehouse.Name : "",
                HenBatchLineName = sampleCage.HenBatch.LineId != null ? sampleCage.HenBatch.Line.Name : "",
                HenBatchCageName = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().Name : "",
                HenBatchLineId = sampleCage.HenBatch.LineId.HasValue ? sampleCage.HenBatch.LineId : sampleCage.HenBatchId,
                Parent = sampleCage.HenBatch.ParentId,
                GeneticName = sampleCage.HenBatch.Genetic.Name,
                Genetic = sampleCage.HenBatch.GeneticId,
                FemaleBirdWeight = cageId.HasValue ? sampleCage.SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId).FirstOrDefault().AvgFemaleBirdWeight : sampleCage.AvgFemaleBirdWeight,
                MaleBirdWeight = cageId.HasValue ? sampleCage.SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId).FirstOrDefault().AvgMaleBirdWeight : sampleCage.AvgMaleBirdWeight,
                CVFemale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().VariationCoefficientFemale.Value : sampleCage.AvgVariationCoefficientFemale,
                CVMale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().VariationCoefficientMale.Value : sampleCage.AvgVariationCoefficientMale,
                UniformityFemale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().UniformityFemale.Value : sampleCage.AvgUniformityFemale,
                UniformityMale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().UniformityMale.Value : sampleCage.AvgUniformityMale,
                HenWarehouseUniformityFemale = sampleCage.HenWarehouseUniformityFemale,
                HenWarehouseUniformityMale = sampleCage.HenWarehouseUniformityMale,
                HenWarehouseCVFemale = sampleCage.HenWarehouseVariationCoefficientFemale,
                HenWarehouseCVMale = sampleCage.HenWarehouseVariationCoefficientMale,
                HenWarehouseAvgFemaleBirdWeight = hasHenBatchCategories
                                                ? sampleCage.HenWarehouseAvgFemaleBirdWeight
                                                : sampleCage.SampleCageMeasurement.Select(scm => scm.AvgFemaleBirdWeight).Average(),
                HenWarehouseAvgMaleBirdWeight = hasHenBatchCategories
                                                ? sampleCage.HenWarehouseAvgMaleBirdWeight
                                                : sampleCage.SampleCageMeasurement.Select(scm => scm.AvgMaleBirdWeight).Average()

            }).ToList();

            IEnumerable<IGrouping<Guid?, GenericSampleCageDTO>> allReports = data.GroupBy(g => g.GroupHenBatchId);

            // Filter data genetic parameters 
            Guid genetic = data.FirstOrDefault().Genetic;
            IQueryable<GeneticsParametersReference> geneticsParametersReference = geneticsParameterService.GetAll()
            .Where(gpr => gpr.HenStage == henStage && gpr.GeneticsId == genetic)
            .Include(r => r.Genetics);

            // Current week
            int currentWeek;
            if (henBatchId.HasValue)
            {
                currentWeek = henBatchService.GetCurrentWeekNumberForDate(henBatchId.Value, DateTime.Now);
            }
            else
            {
                currentWeek = data.Max(d => d.Week);
            }

            return (allReports, geneticsParametersReference, currentWeek);
        }

        public const string maleSeriesColor = "#2471A3"; // blue
        public const string femaleSeriesColor = "#E74C3C"; // red
        public const string uniformitySTDColor = "#145A32 "; // dark green
        public const string cvSTDColor = "#633974"; // violet
        /// <summary>
        /// Get data for bird weight chart
        /// </summary>
        public DashboardLineOrBarChartDTO GetDataForBreedingBirdWeightChart(FilterDataDTO filters)
        {
            var (allReports, geneticsParametersReference, currentWeek) = GetBreedingData(filters);
            HenStage henStage = filters.HenStage.Value;

            if (currentWeek <= 0)
                return new DashboardLineOrBarChartDTO();

            return BirdWeigthChart(henStage, filters, allReports, geneticsParametersReference, currentWeek);
        }

        /// <summary>
        /// Get data for uniformity chart
        /// </summary>
        public DashboardLineOrBarChartDTO GetBreedingCVUniformityChart(FilterDataDTO filters)
        {
            var (allReports, geneticsParametersReference, currentWeek) = GetBreedingData(filters);
            HenStage henStage = filters.HenStage.Value;
            bool uniformityChartIsShown = henStage == HenStage.Breeding && filters.HasAuthorizationSearaCharts;

            if (currentWeek <= 0)
                return new DashboardLineOrBarChartDTO();

            return uniformityChartIsShown ? UniformityAndCVChart(henStage, filters, geneticsParametersReference, currentWeek, allReports) : null;
        }

        /// <summary>
        /// Get data for bird weight chart and uniformity chart
        /// </summary>
        public SampleCageChartsDTO SampleCageCharts(FilterDataDTO filters)
        {
            HenStage henStage = filters.HenStage.Value;
            bool uniformityChartIsShown = henStage == HenStage.Breeding && filters.HasAuthorizationSearaCharts;
            Guid? henBatchId = filters.HenBatchId;
            Guid? warehouseId = filters.WarehouseId;
            Guid? lineId = filters.LineId;
            Guid? cageId = filters.CageId;

            // Get hen batch data 
            DayOfWeek farmDayOfWeek = chartFunctions.GetFarmDayOfWeek();
            Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<SampleCageReport, HenBatchPerformance> querySampleCage = this.sampleCageReportService.GetAll()
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Line).ThenInclude(line => line.Warehouse)
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Farm)
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Genetic)
               .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.SampleCages)
               .Include(sampleCage => sampleCage.SampleCageMeasurement)
               .Include(sampleCage => sampleCage.HenBatchPerformance);


            IQueryable<SampleCageReport> query = !cageId.HasValue ? querySampleCage.Where(sampleCage => sampleCage.HenBatch.HenStage == henStage) :
                                          querySampleCage.Where(sampleCage => sampleCage.SampleCageMeasurement.Any(scm => scm.SampleCageId == cageId));
            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            bool hasHenBatchCategories = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ReportsHenWarehouseAndParentBatchWeightMeasurement && s.Value == "True");

            // Filter data 
            if (lineId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.LineId == lineId.Value);
            else if (warehouseId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.Line.WarehouseId == warehouseId.Value && sampleCage.HenBatch.ParentId == henBatchId.Value);
            else if (henBatchId.HasValue)
                query = query.Where(sampleCage => sampleCage.HenBatch.ParentId == henBatchId || sampleCage.HenBatchId == henBatchId);

            if (!query.Any())
                return new SampleCageChartsDTO(uniformityChartIsShown, this.localizer[StatsLang.NoDataAvilableMessage], this.localizer[Lang.BirdWeightChartTitle]);

            List<GenericSampleCageDTO> data = query.Select(sampleCage => new GenericSampleCageDTO()
            {
                GroupHenBatchId = sampleCage.HenBatch.ParentId.HasValue ? sampleCage.HenBatch.ParentId : sampleCage.HenBatchId,
                Date = sampleCage.Date.GetPastSelectedDay(farmDayOfWeek),
                Week = sampleCage.HenBatchPerformance.WeekNumber,
                HenBatchId = sampleCage.HenBatchId,
                HenBatchCode = sampleCage.HenBatch.Code,
                HenBatchFarmName = sampleCage.HenBatch.Farm.Name,
                HenBatchWarehauseName = sampleCage.HenBatch.LineId != null ? sampleCage.HenBatch.Line.Warehouse.Name : "",
                HenBatchLineName = sampleCage.HenBatch.LineId != null ? sampleCage.HenBatch.Line.Name : "",
                HenBatchCageName = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().Name : "",
                HenBatchLineId = sampleCage.HenBatch.LineId.HasValue ? sampleCage.HenBatch.LineId : sampleCage.HenBatchId,
                Parent = sampleCage.HenBatch.ParentId,
                GeneticName = sampleCage.HenBatch.Genetic.Name,
                Genetic = sampleCage.HenBatch.GeneticId,
                FemaleBirdWeight = cageId.HasValue ? sampleCage.SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId).FirstOrDefault().AvgFemaleBirdWeight : sampleCage.AvgFemaleBirdWeight,
                MaleBirdWeight = cageId.HasValue ? sampleCage.SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId).FirstOrDefault().AvgMaleBirdWeight : sampleCage.AvgMaleBirdWeight,
                CVFemale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().VariationCoefficientFemale.Value : sampleCage.AvgVariationCoefficientFemale,
                CVMale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().VariationCoefficientMale.Value : sampleCage.AvgVariationCoefficientMale,
                UniformityFemale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().UniformityFemale.Value : sampleCage.AvgUniformityFemale,
                UniformityMale = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().SampleCageMeasurement.Where(scm => scm.SampleCageId == cageId && scm.SampleCageReportId == sampleCage.Id).First().UniformityMale.Value : sampleCage.AvgUniformityMale,
                HenWarehouseUniformityFemale = sampleCage.HenWarehouseUniformityFemale,
                HenWarehouseUniformityMale = sampleCage.HenWarehouseUniformityMale,
                HenWarehouseCVFemale = sampleCage.HenWarehouseVariationCoefficientFemale,
                HenWarehouseCVMale = sampleCage.HenWarehouseVariationCoefficientMale,
                HenWarehouseAvgFemaleBirdWeight = hasHenBatchCategories
                                                ? sampleCage.HenWarehouseAvgFemaleBirdWeight
                                                : sampleCage.SampleCageMeasurement.Select(scm => scm.AvgFemaleBirdWeight).Average(),
                HenWarehouseAvgMaleBirdWeight = hasHenBatchCategories
                                                ? sampleCage.HenWarehouseAvgMaleBirdWeight
                                                : sampleCage.SampleCageMeasurement.Select(scm => scm.AvgMaleBirdWeight).Average()

            }).ToList();

            IEnumerable<IGrouping<Guid?, GenericSampleCageDTO>> allReports = data.GroupBy(g => g.GroupHenBatchId);

            // Filter data genetic parameters 
            Guid genetic = data.FirstOrDefault().Genetic;
            IQueryable<GeneticsParametersReference> geneticsParametersReference = geneticsParameterService.GetAll()
            .Where(gpr => gpr.HenStage == henStage && gpr.GeneticsId == genetic)
            .Include(r => r.Genetics);

            // Current week
            int currentWeek = henBatchService.GetCurrentWeekNumberForDate(henBatchId.Value, DateTime.Now);

            return new SampleCageChartsDTO()
            {
                BirdWeightChart = BirdWeigthChart(henStage, filters, allReports, geneticsParametersReference, currentWeek),
                UniformityChart = uniformityChartIsShown ? UniformityAndCVChart(henStage, filters, geneticsParametersReference, currentWeek, null) : null,
            };
        }

        #region Sample Cage Charts
        private DashboardLineOrBarChartDTO BirdWeigthChart(HenStage henStage, FilterDataDTO filters, IEnumerable<IGrouping<Guid?, GenericSampleCageDTO>> data, IQueryable<GeneticsParametersReference> parametersGenetic, int currentWeek)
        {
            Guid? henBatchId = filters.HenBatchId;
            Guid? warehouseId = filters.WarehouseId;
            Guid? lineId = filters.LineId;
            Guid? cageId = filters.CageId;

            // This chart is always shown
            DashboardLineOrBarChartDTO modelBirdWeightChart = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[Lang.BirdWeightChartTitle],
                XAxisLabel = this.localizer[Lang.BirdWeightChartXAxisLabel],
                XAxisGridLineWidth = 1,
                ToolTipShared = false,
                Series = new List<DecimalSeriesDTO>(),

                // Vertical axis
                YAxis = new List<YAxis>()
            };
            // Hen day axis
            modelBirdWeightChart.YAxis.Add(new YAxis()
            {
                Min = henStage == HenStage.Laying ? 2000 : 0,
                GridLineWidth = 1,
                TickInterval = 300,
                Opposite = false,
                Title = new TitleDTO() { Text = this.localizer[Lang.Weight] + " (g)" }
            });

            // Time axis, breeding:[0,22] laying:[23, ...) 
            int lastWeekWithData = 24;
            int stageStart = 0;
            if (henStage == HenStage.Laying)
            {
                lastWeekWithData = 66;
                stageStart = 23;

                for (int week = stageStart; week < lastWeekWithData + 1; week++)
                    modelBirdWeightChart.Categories.Add(week.ToString());
            }
            else
            {
                for (int week = stageStart; week < lastWeekWithData + 1; week++)
                    modelBirdWeightChart.Categories.Add(week.ToString());
            }

            // if current week is greater than the week of the henStage, we will keep lastWeekWithData
            currentWeek = currentWeek > lastWeekWithData ? lastWeekWithData : currentWeek - stageStart;

            int weeksByDefault = henStage == HenStage.Laying ? lastWeekWithData - stageStart + 1 : lastWeekWithData - stageStart + 1;

            // Set units
            CapacityUnit birdWeightCapacityUnit = this.capacityUnitService.Get(CapacityUnits.Grams);
            string birdWeightSymbol = birdWeightCapacityUnit.Symbol;

            //Build chart STD data
            List<decimal> femaleBirdWeightSTD = new List<decimal>(new decimal[weeksByDefault]);
            List<decimal> maleBirdWeightSTD = new List<decimal>(new decimal[weeksByDefault]);

            List<decimal> uniformitySTD = new List<decimal>(new decimal[weeksByDefault]);

            foreach (GeneticsParametersReference geneticParametersByWeek in parametersGenetic)
            {
                int index = geneticParametersByWeek.TimePeriodValue < lastWeekWithData ? geneticParametersByWeek.TimePeriodValue - stageStart : -1;
                if (index != -1)
                {
                    femaleBirdWeightSTD[index] = capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, geneticParametersByWeek.WeightFemale);
                    maleBirdWeightSTD[index] = capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, geneticParametersByWeek.WeightMale);
                }
            }

            string geneticName = parametersGenetic.FirstOrDefault().Genetics.Name;
            modelBirdWeightChart.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.FemaleSTD, geneticName],
                Data = femaleBirdWeightSTD.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                ShowInLegend = true,
                Visible = true,
                Type = "line",
                YAxis = 0,
                Color = femaleSeriesColor,
                ToolTip = new ToolTipDTO() { ValueSuffix = birdWeightSymbol }
            });
            modelBirdWeightChart.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.MaleSTD, geneticName],
                Data = maleBirdWeightSTD.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                ShowInLegend = true,
                Visible = true,
                Type = "line",
                YAxis = 0,
                Color = maleSeriesColor,
                ToolTip = new ToolTipDTO() { ValueSuffix = birdWeightSymbol }
            });

            if (filters.RegionalId != null || filters.UnitId != null || filters.SupervisorId != null || filters.ExtensionistId != null)
            {
                List<decimal> femaleBirdWeightData = new List<decimal>(new decimal[weeksByDefault]);
                List<decimal> maleBirdWeightData = new List<decimal>(new decimal[weeksByDefault]);
                List<decimal> femaleUniformityData = new List<decimal>(new decimal[weeksByDefault]);
                List<decimal> maleUniformityData = new List<decimal>(new decimal[weeksByDefault]);

                // Agrupa todos os relatórios por data, independentemente do lote
                IEnumerable<IGrouping<DateTime, GenericSampleCageDTO>> allReportsGroupedByDate = data.SelectMany(hb => hb).GroupBy(hb => hb.Date.Date);

                // Define um nome padrão para os dados agregados
                string defaultName = "Todos os Lotes"; // Ou outro nome que faça sentido no seu contexto

                // Processa os relatórios agrupados por data
                foreach (IGrouping<DateTime, GenericSampleCageDTO> reports in allReportsGroupedByDate)
                {
                    int index = reports.FirstOrDefault().Week - stageStart;
                    if (index >= 0 && index < weeksByDefault)
                    {
                        if (warehouseId.HasValue && !lineId.HasValue && !cageId.HasValue)
                        {
                            // Média simples para fêmeas (armazém)
                            decimal totalFemaleBirdWeight = reports
                                .Where(r => r.HenWarehouseAvgFemaleBirdWeight.HasValue)
                                .Sum(hb => hb.HenWarehouseAvgFemaleBirdWeight.Value);
                            int numberOfFemaleReports = reports
                                .Count(r => r.HenWarehouseAvgFemaleBirdWeight.HasValue);
                            femaleBirdWeightData[index] = numberOfFemaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalFemaleBirdWeight) / numberOfFemaleReports;

                            // Média simples para machos (armazém)
                            decimal totalMaleBirdWeight = reports
                                .Where(r => r.HenWarehouseAvgMaleBirdWeight.HasValue)
                                .Sum(hb => hb.HenWarehouseAvgMaleBirdWeight.Value);
                            int numberOfMaleReports = reports
                                .Count(r => r.HenWarehouseAvgMaleBirdWeight.HasValue);
                            maleBirdWeightData[index] = numberOfMaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalMaleBirdWeight) / numberOfMaleReports;
                        }
                        else
                        {
                            // Média simples para fêmeas (diretamente dos relatórios)
                            decimal totalFemaleBirdWeight = reports
                                .Where(r => r.FemaleBirdWeight != 0)
                                .Sum(hb => hb.FemaleBirdWeight);
                            int numberOfFemaleReports = reports
                                .Count(r => r.FemaleBirdWeight != 0);
                            femaleBirdWeightData[index] = numberOfFemaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalFemaleBirdWeight) / numberOfFemaleReports;

                            // Média simples para machos (diretamente dos relatórios)
                            decimal totalMaleBirdWeight = reports
                                .Where(r => r.MaleBirdWeight != 0)
                                .Sum(hb => hb.MaleBirdWeight);
                            int numberOfMaleReports = reports
                                .Count(r => r.MaleBirdWeight != 0);
                            maleBirdWeightData[index] = numberOfMaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalMaleBirdWeight) / numberOfMaleReports;
                        }
                    }
                }

                // Preenche os dados até a semana atual
                FillEmptyDataUpToCurrentWeek(currentWeek, femaleBirdWeightData, maleBirdWeightData);

                // Adiciona a série de dados para as fêmeas
                modelBirdWeightChart.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.Female, defaultName, geneticName],
                    Data = femaleBirdWeightData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 0,
                    Color = femaleSeriesColor,
                    ToolTip = new ToolTipDTO() { ValueSuffix = birdWeightSymbol }
                });

                // Adiciona a série de dados para os machos
                modelBirdWeightChart.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.Male, defaultName, geneticName],
                    Data = maleBirdWeightData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 0,
                    Color = maleSeriesColor,
                    ToolTip = new ToolTipDTO() { ValueSuffix = birdWeightSymbol }
                });
            }
            else
            {
                // Build chart real data
                foreach (IGrouping<Guid?, GenericSampleCageDTO> henBatches in data)
                {
                    string defaultName = "";

                    List<decimal> femaleBirdWeightData = new List<decimal>(new decimal[weeksByDefault]);
                    List<decimal> maleBirdWeightData = new List<decimal>(new decimal[weeksByDefault]);

                    List<decimal> femaleUniformityData = new List<decimal>(new decimal[weeksByDefault]);
                    List<decimal> maleUniformityData = new List<decimal>(new decimal[weeksByDefault]);

                    IEnumerable<IGrouping<DateTime, GenericSampleCageDTO>> henBatchesGroupedByDate = henBatches.GroupBy(hb => hb.Date.Date);

                    foreach (IGrouping<DateTime, GenericSampleCageDTO> reports in henBatchesGroupedByDate)
                    {
                        int index = reports.FirstOrDefault().Week - stageStart;
                        if (index >= 0 && index < weeksByDefault)
                        {
                            if (warehouseId.HasValue && !lineId.HasValue && !cageId.HasValue)
                            {
                                decimal totalFemaleBirdWeight = reports.Where(r => r.HenWarehouseAvgFemaleBirdWeight.HasValue).Sum(hb => hb.HenWarehouseAvgFemaleBirdWeight.Value);
                                decimal totalMaleBirdWeight = reports.Where(r => r.HenWarehouseAvgMaleBirdWeight.HasValue).Sum(hb => hb.HenWarehouseAvgMaleBirdWeight.Value);
                                int numbersOfFemaleReports = reports.Where(r => r.HenWarehouseAvgFemaleBirdWeight.HasValue && r.HenWarehouseAvgFemaleBirdWeight != 0).Count();
                                int numbersOfMaleReports = reports.Where(r => r.HenWarehouseAvgMaleBirdWeight.HasValue && r.HenWarehouseAvgMaleBirdWeight != 0).Count();
                                femaleBirdWeightData[index] = numbersOfFemaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalFemaleBirdWeight) / numbersOfFemaleReports;
                                maleBirdWeightData[index] = numbersOfMaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalMaleBirdWeight) / numbersOfMaleReports;
                            }
                            else
                            {
                                decimal totalFemaleBirdWeight = reports.Sum(hb => hb.FemaleBirdWeight);
                                decimal totalMaleBirdWeight = reports.Sum(hb => hb.MaleBirdWeight);
                                int numbersOfFemaleReports = reports.Where(r => r.FemaleBirdWeight != 0).Count();
                                int numbersOfMaleReports = reports.Where(r => r.MaleBirdWeight != 0).Count();
                                femaleBirdWeightData[index] = numbersOfFemaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalFemaleBirdWeight) / numbersOfFemaleReports;
                                maleBirdWeightData[index] = numbersOfMaleReports == 0 ? 0 : capacityUnitBusinessLogic.ConvertValue(birdWeightCapacityUnit.Id, totalMaleBirdWeight) / numbersOfMaleReports;
                            }
                        }

                        List<SelectListItem> henWarehousesForHenBatch = henBatchId.HasValue ? henBatchBusinessLogic.GetWarehousesByHenBatch(henBatchId.Value, henStage) : new List<SelectListItem>();
                        string warehouseName = "";
                        if (henWarehousesForHenBatch.Count() == 1)
                        {
                            warehouseName = $"| {henWarehousesForHenBatch.First().Text} ";
                        }
                        else
                        {
                            warehouseName = warehouseId.HasValue ? $"| {reports.Min(hb => hb.HenBatchWarehauseName)} " : "";
                        }
                        string lineName = lineId.HasValue ? $"| {reports.Min(hb => hb.HenBatchLineName)}" : "";
                        string cageName = cageId.HasValue ? $"| {reports.Min(hb => hb.HenBatchCageName)}" : "";
                        defaultName = $"{reports.Min(hb => hb.HenBatchFarmName)} | {reports.Min(hb => hb.HenBatchCode)} {warehouseName} {lineName} {cageName}";
                        geneticName = reports.FirstOrDefault().GeneticName;

                    }

                    FillEmptyDataUpToCurrentWeek(currentWeek, femaleBirdWeightData, maleBirdWeightData);

                    modelBirdWeightChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.Female, defaultName, geneticName],
                        Data = femaleBirdWeightData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        Color = femaleSeriesColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = birdWeightSymbol }
                    });

                    modelBirdWeightChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.Male, defaultName, geneticName],
                        Data = maleBirdWeightData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        Color = maleSeriesColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = birdWeightSymbol }
                    });

                }
            }
            modelBirdWeightChart.NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage];

            return modelBirdWeightChart;
        }

        private DashboardLineOrBarChartDTO UniformityAndCVChart(HenStage henStage, FilterDataDTO filters, IQueryable<GeneticsParametersReference> parametersGenetic, int currentWeek, IEnumerable<IGrouping<Guid?, GenericSampleCageDTO>>? data)
        {
            if (data == null || !data.Any())
            {
                Guid? henBatchId = filters.HenBatchId;
                Guid? warehouseId = filters.WarehouseId;
                Guid? lineId = filters.LineId;
                Guid? cageId = filters.CageId;

                // Get hen batch data 
                DayOfWeek farmDayOfWeek = chartFunctions.GetFarmDayOfWeek();

                // queryable for uniformity data
                IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService.GetAll()
                   .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Line).ThenInclude(line => line.Warehouse)
                   .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Farm)
                   .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.Genetic)
                   .Include(sampleCage => sampleCage.HenBatch).ThenInclude(henBatch => henBatch.SampleCages);

                IQueryable<HenBatchPerformance> query = henBatchPerformances.Where(hbp => hbp.HenBatch.HenStage == henStage && hbp.AvgVariationCoefficientFemale != 0 && hbp.AvgUniformityFemale != 0);

                // Filter data 
                if (lineId.HasValue)
                    query = query.Where(sampleCage => sampleCage.HenBatch.LineId == lineId.Value);
                else if (warehouseId.HasValue)
                    query = query.Where(sampleCage => sampleCage.HenBatch.Line.WarehouseId == warehouseId.Value);
                else if (henBatchId.HasValue)
                    query = query.Where(sampleCage => sampleCage.HenBatch.ParentId == henBatchId || sampleCage.HenBatchId == henBatchId);

                List<GenericSampleCageDTO> allReports = query
                    .Select(sampleCage => new GenericSampleCageDTO()
                    {
                        GroupHenBatchId = sampleCage.HenBatch.ParentId.HasValue ? sampleCage.HenBatch.ParentId : sampleCage.HenBatchId,
                        Date = sampleCage.Date.GetPastSelectedDay(farmDayOfWeek),
                        Week = sampleCage.WeekNumber,
                        HenBatchId = sampleCage.HenBatchId,
                        HenBatchCode = sampleCage.HenBatch.Code,
                        HenBatchFarmName = sampleCage.HenBatch.Farm.Name,
                        HenBatchWarehauseName = sampleCage.HenBatch.LineId != null ? sampleCage.HenBatch.Line.Warehouse.Name : "",
                        HenBatchLineName = sampleCage.HenBatch.LineId != null ? sampleCage.HenBatch.Line.Name : "",
                        HenBatchCageName = (cageId.HasValue && sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId.Value).FirstOrDefault() != null) ? sampleCage.HenBatch.SampleCages.Where(sc => sc.Id == cageId).First().Name : "",
                        HenBatchLineId = sampleCage.HenBatch.LineId.HasValue ? sampleCage.HenBatch.LineId : sampleCage.HenBatchId,
                        Parent = sampleCage.HenBatch.ParentId,
                        GeneticName = sampleCage.HenBatch.Genetic.Name,
                        Genetic = sampleCage.HenBatch.GeneticId,
                        CVFemale = sampleCage.AvgVariationCoefficientFemale,
                        CVMale = sampleCage.AvgVariationCoefficientMale,
                        UniformityFemale = sampleCage.AvgUniformityFemale,
                        UniformityMale = sampleCage.AvgUniformityMale,
                    }).ToList();

                data = allReports.GroupBy(g => g.GroupHenBatchId);
            }

            // This chart is only shown if you have the seara permissions
            DashboardLineOrBarChartDTO modelUniformityChart = new DashboardLineOrBarChartDTO
            {
                Title = this.localizer[Lang.UniformityChartTitle],
                XAxisLabel = this.localizer[Lang.UniformityChartXAxisLabel],
                XAxisGridLineWidth = 1,
                ToolTipShared = false,
                Series = new List<DecimalSeriesDTO>(),

                // Vertical axis
                YAxis = new List<YAxis>()
            };

            // Uniformity axis
            modelUniformityChart.YAxis.Add(new YAxis()
            {
                Min = 40,
                GridLineWidth = 1,
                Opposite = false,
                Title = new TitleDTO() { Text = this.localizer[Lang.Uniformity] }
            });

            // Time axis, breeding:[0,22]  
            int lastWeekWithData = 24;
            int stageStart = 0;

            for (int week = stageStart; week < lastWeekWithData + 1; week++)
                modelUniformityChart.Categories.Add(week.ToString());

            // if current week is greater than the week of the henStage, we will keep lastWeekWithData
            currentWeek = currentWeek > lastWeekWithData ? lastWeekWithData : currentWeek - stageStart;

            int weeksByDefault = henStage == HenStage.Laying ? lastWeekWithData - stageStart + 1 : lastWeekWithData - stageStart + 1;

            //Build chart STD data
            List<decimal> uniformitySTD = new List<decimal>(new decimal[weeksByDefault]);
            List<decimal> coefficientVariationSTD = new List<decimal>(new decimal[weeksByDefault]);

            foreach (GeneticsParametersReference geneticParametersByWeek in parametersGenetic)
            {
                int index = geneticParametersByWeek.TimePeriodValue < lastWeekWithData ? geneticParametersByWeek.TimePeriodValue - stageStart : -1;
                if (index != -1)
                {
                    uniformitySTD[index] = geneticParametersByWeek.Uniformity;
                    coefficientVariationSTD[index] = geneticParametersByWeek.VarianceCoefficient;
                }
            }

            modelUniformityChart.Series.Add(new DecimalSeriesDTO()
            {
                Name = this.localizer[Lang.UniformitySTD],
                Data = uniformitySTD.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                ShowInLegend = true,
                Visible = true,
                Type = "line",
                YAxis = 0,
                Color = uniformitySTDColor,
                ToolTip = new ToolTipDTO() { ValueSuffix = "(%)" }
            });
            if (filters.UnitId != null || filters.RegionalId != null || filters.SupervisorId != null || filters.ExtensionistId != null)
            {
                // Agrupa todos os relatórios por data, independentemente do lote
                IEnumerable<IGrouping<DateTime, GenericSampleCageDTO>> allReportsGroupedByDate = data.SelectMany(hb => hb).GroupBy(hb => hb.Date.Date);

                // Define um nome padrão para os dados agregados
                string defaultName = "Todos os Lotes"; // Ou outro nome que faça sentido no seu contexto

                // Inicializa as listas para armazenar os dados agregados
                List<decimal> femaleUniformityData = new List<decimal>(new decimal[weeksByDefault]);
                List<decimal> maleUniformityData = new List<decimal>(new decimal[weeksByDefault]);
                List<decimal> femaleCVData = new List<decimal>(new decimal[weeksByDefault]);
                List<decimal> maleCVData = new List<decimal>(new decimal[weeksByDefault]);

                // Processa os relatórios agrupados por data
                foreach (IGrouping<DateTime, GenericSampleCageDTO> reports in allReportsGroupedByDate)
                {
                    int index = reports.FirstOrDefault().Week - stageStart;
                    if (index >= 0 && index < weeksByDefault)
                    {
                        // Média simples para uniformidade das fêmeas
                        decimal totalUniformityFemale = reports.Any(hb => hb.UniformityFemale > 0) ? reports.Where(hb => hb.UniformityFemale > 0).Average(hb => hb.UniformityFemale) : 0;
                        femaleUniformityData[index] = totalUniformityFemale;

                        // Média simples para uniformidade dos machos
                        decimal totalUniformityMale = reports.Any(hb => hb.UniformityMale > 0) ? reports.Where(hb => hb.UniformityMale > 0).Average(hb => hb.UniformityMale) : 0;
                        maleUniformityData[index] = totalUniformityMale;

                        // Média simples para CV das fêmeas
                        femaleCVData[index] = reports.Any(hb => hb.CVFemale > 0) ? reports.Where(hb => hb.CVFemale > 0).Average(hb => hb.CVFemale) : 0;

                        // Média simples para CV dos machos
                        maleCVData[index] = reports.Any(hb => hb.CVMale > 0) ? reports.Where(hb => hb.CVMale > 0).Average(hb => hb.CVMale) : 0;
                    }
                }

                // Preenche os dados até a semana atual
                FillEmptyDataUpToCurrentWeek(currentWeek, femaleUniformityData, maleUniformityData);
                FillEmptyDataUpToCurrentWeek(currentWeek, femaleCVData, maleCVData);

                // Adiciona a série de dados para a uniformidade das fêmeas
                modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.UniformityFemale],
                    Data = femaleUniformityData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 0,
                    Color = femaleSeriesColor,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });

                // Adiciona a série de dados para a uniformidade dos machos
                modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.UniformityMale],
                    Data = maleUniformityData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 0,
                    Color = maleSeriesColor,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });

                // Adiciona a série de dados para o CV das fêmeas
                modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.CVFemale],
                    Data = femaleCVData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 0,
                    Color = femaleSeriesColor,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });

                // Adiciona a série de dados para o CV dos machos
                modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                {
                    Name = this.localizer[Lang.CVMale],
                    Data = maleCVData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                    ShowInLegend = true,
                    Visible = true,
                    Type = "spline",
                    YAxis = 0,
                    Color = maleSeriesColor,
                    ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                });
            }
            else
            {
                // Build chart real data
                foreach (IGrouping<Guid?, GenericSampleCageDTO> henBatches in data)
                {
                    List<decimal> femaleUniformityData = new List<decimal>(new decimal[weeksByDefault]);
                    List<decimal> maleUniformityData = new List<decimal>(new decimal[weeksByDefault]);
                    List<decimal> femaleCVData = new List<decimal>(new decimal[weeksByDefault]);
                    List<decimal> maleCVData = new List<decimal>(new decimal[weeksByDefault]);

                    IEnumerable<IGrouping<DateTime, GenericSampleCageDTO>> henBatchesGroupedByDate = henBatches.GroupBy(hb => hb.Date.Date);

                    foreach (IGrouping<DateTime, GenericSampleCageDTO> reports in henBatchesGroupedByDate)
                    {
                        int index = reports.FirstOrDefault().Week - stageStart;
                        if (index >= 0)
                        {
                            decimal totalUniformityFemale = reports.Any(hb => hb.UniformityFemale > 0) ? reports.Where(hb => hb.UniformityFemale > 0).Average(hb => hb.UniformityFemale) : 0;
                            decimal totalUniformityMale = reports.Any(hb => hb.UniformityMale > 0) ? reports.Where(hb => hb.UniformityMale > 0).Average(hb => hb.UniformityMale) : 0;
                            femaleUniformityData[index] = totalUniformityFemale;
                            maleUniformityData[index] = totalUniformityMale;

                            femaleCVData[index] = reports.Any(hb => hb.CVFemale > 0) ? reports.Where(hb => hb.CVFemale > 0).Average(hb => hb.CVFemale) : 0;
                            maleCVData[index] = reports.Any(hb => hb.CVMale > 0) ? reports.Where(hb => hb.CVMale > 0).Average(hb => hb.CVMale) : 0;
                        }
                    }

                    FillEmptyDataUpToCurrentWeek(currentWeek, femaleUniformityData, maleUniformityData);
                    FillEmptyDataUpToCurrentWeek(currentWeek, femaleCVData, maleCVData);

                    modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.UniformityFemale],
                        Data = femaleUniformityData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        Color = femaleSeriesColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                    });

                    modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.UniformityMale],
                        Data = maleUniformityData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        Color = maleSeriesColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                    });


                    modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.CVSTD],
                        Data = coefficientVariationSTD.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "line",
                        YAxis = 0,
                        Color = cvSTDColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = "(%)" }
                    });

                    modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.CVFemale],
                        Data = femaleCVData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        Color = femaleSeriesColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                    });

                    modelUniformityChart.Series.Add(new DecimalSeriesDTO()
                    {
                        Name = this.localizer[Lang.CVMale],
                        Data = maleCVData.Select(d => d == 0 ? (decimal?)null : d).ToList(),
                        ShowInLegend = true,
                        Visible = true,
                        Type = "spline",
                        YAxis = 0,
                        Color = maleSeriesColor,
                        ToolTip = new ToolTipDTO() { ValueSuffix = "" }
                    });

                }
            }

            modelUniformityChart.NoDataAvilableMessage = this.localizer[StatsLang.NoDataAvilableMessage];

            return modelUniformityChart;
        }

        private void FillEmptyDataUpToCurrentWeek(int currentWeek, List<decimal> femaleData, List<decimal> maleData)
        {
            // We complete data up to the current week. If the current week exceeds the data that we have to complete, 
            // we are left with the default value
            int limitWeek = currentWeek > femaleData.Count() ? femaleData.Count() : currentWeek;

            for (int potentialWeekToUpdate = 0; potentialWeekToUpdate < limitWeek; potentialWeekToUpdate++)
            {
                if (femaleData[potentialWeekToUpdate] == 0)
                {
                    int weekWithData = potentialWeekToUpdate > 0 ? potentialWeekToUpdate - 1 : 0;
                    femaleData[potentialWeekToUpdate] = femaleData[weekWithData];
                }
                if (maleData[potentialWeekToUpdate] == 0)
                {
                    int weekWithData = potentialWeekToUpdate > 0 ? potentialWeekToUpdate - 1 : 0;
                    maleData[potentialWeekToUpdate] = maleData[weekWithData];
                }
            }
        }
        #endregion

        #endregion
    }
}