using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.BusinessLogic.DTOs.Genetic;
using Domain.Entities.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface ISearaLayingChartsBusinessLogic
    {
        /// <summary>
        /// Get data for feed intake GAD chart.
        /// </summary>
        DashboardLineOrBarChartDTO GetSearaLayingFeedIntakeGADChart(FilterDataDTO filters);

        /// <summary>
        /// Get data for male/female chart.
        /// </summary>
        DashboardLineOrBarChartDTO GetSearaMaleFemaleChart(FilterDataDTO filters);

        /// <summary>
        /// Get data for saleable chicks chart.
        /// </summary>
        DashboardLineOrBarChartDTO GetSaleableChicksChart(FilterDataDTO filters);

        /// <summary>
        /// Get data for saleable chicks chart.
        /// </summary>
        DashboardLineOrBarChartDTO GetOIFCChart(FilterDataDTO filters);

        /// <summary>
        /// Get async data for bed eggs chart.
        /// </summary>
        Task<DashboardLineOrBarChartDTO> GetBedEggsChartAsync(FilterDataDTO filters, CancellationToken cancellationToken);

        /// <summary>
        /// Get data for viability chart.
        /// </summary>
        DashboardLineOrBarChartDTO GetViabilityChart(FilterDataDTO filters);

        /// <summary>
        /// Get data for laying hatchering and fertility chart.
        /// </summary>
        Task<DashboardLineOrBarChartDTO> GetLayingHatcheringAndFertilityChartAsync(FilterDataDTO filters, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Get async data for egg quality chart.
        /// </summary>
        Task<DashboardLineOrBarChartDTO> GetDataForEggQualityChartAsync(FilterDataDTO filters, CancellationToken cancellationToken);

        /// <summary>
        /// Get data for performance chart.
        /// </summary>
        Tuple<List<List<HenBatchStatusDTO>>, DashboardLineOrBarChartDTO> PerformanceChart(FilterDataDTO filters, HenStage? henStage = null);

        ///<summary>
        /// Get data for genetic chart
        ///</summary>
        GeneticGraphicResponseDTO GetGeneticChartData(FilterDataDTO filters);

        /// <summary>
        /// Get hen batch ids from filters.
        /// </summary>
        IQueryable<Guid> GetHenBatchIdsFromFilters(FilterDataDTO filters);
    }
}