﻿using Domain.Entities.Model;
using System.Collections.Generic;

namespace Domain.Logic.BusinessLogic.DTOs.Genetic
{
    public class GeneticGraphicDTO
    {
        public int WeekNumber { get; set; }

        public double? MortalitySTDFemale { get; set; }

        public double? BodyWeightSTDMinFemale { get; set; }
        public double? BodyWeightSTDMinMale { get; set; }

        public double? BodyWeightSTDMaxFemale { get; set; }
        public double? BodyWeightSTDMaxMale { get; set; }

        public double? HenDayEggsProductionSTDMin { get; set; }

        public double? HenDayEggsProductionSTDMax { get; set; }

        public double? AvgEggWeightSTD { get; set; }

        public double? MortalityFemale { get; set; }

        public double? BodyWeightFemale { get; set; }
        public double? BodyWeightMale { get; set; }

        public double? HenDayEggsProduction { get; set; }

        public double? TotalProducedEggsWeekly { get; set; }
        public double? TotalProducedEggsWeeklySTD { get; set; }
        public double? IncubatedEggsWeekly { get; set; }
        public double? FertileEggsWeekly { get; set; }
        public double? HatchableEggsWeekly { get; set; }
        public double? IncubatedEggsWeeklySTD { get; set; }
        public double? FertileEggsWeeklySTD { get; set; }
        public double? HatchingEggsWeeklySTD { get; set; }

        public double? AvgEggWeight { get; set; }

        public double? MaxTemp { get; set; }

        public double? MinTemp { get; set; }

        public GeneticGraphicDTO() { }
    }

    public class GeneticGraphicResponseDTO
    {
        public List<GeneticGraphicDTO> Data { get; set; }

        public HenStage HenStage { get; set; }
        public int? TotalWeeks  { get; set; }
    }
}
