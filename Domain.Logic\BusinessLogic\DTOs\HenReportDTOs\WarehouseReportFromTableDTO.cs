using System;
using System.Collections.Generic;

namespace Domain.Logic.BusinessLogic.DTOs.HenReportDTOs
{
    public class WarehouseReportFromTableDTO
    {
        public Guid HenBatchId { get; set; }
        public Guid? WarehouseId { get; set; }
        public string Line { get; set; }

        public decimal WaterConsumption { get; set; }
        public decimal WaterPh { get; set; }
        public decimal? WaterChlorineConcentration { get; set; }
        public decimal WaterPillQuantity { get; set; }
        public decimal MinTemp { get; set; }
        public decimal MaxTemp { get; set; }
        public decimal Humidity { get; set; }

        // Ovos Incubáveis (Hatchable Eggs)
        public int CleanNestEggs { get; set; }
        public int DirtyNestEggs { get; set; }
        public int BedEggs { get; set; }

        // Ovos Comerciais (Commercial Eggs)
        public int DoubleYolkEggs { get; set; }
        public int SmallEggs { get; set; }
        public int DefectiveEggs { get; set; }
        public int DirtyRolledEggs { get; set; }
        public int CrackedEggs { get; set; }
        public int ThinShellEggs { get; set; }
        public int EliminatedBrokenEggs { get; set; }

        public decimal FeedIntakeFemale { get; set; }
        public Guid FeedIntakeFemaleOriginId { get; set; }
        public decimal FeedIntakeMale { get; set; }
        public Guid FeedIntakeMaleOriginId { get; set; }
        public decimal TotalFeedIntakeFemale { get; set; }
        public decimal TotalFeedIntakeMale { get; set; }

        public int DeadMale { get; set; }
        public int DeadFemale { get; set; }
        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }

        public List<ClassifiedEggDTO> ClassifiedEggs { get; set; } = new List<ClassifiedEggDTO>();
    }

    public class CreateWarehouseReportFromTableDTO
    {
        public string ReportDate { get; set; }
        public List<WarehouseReportFromTableDTO> Reports { get; set; }
        public List<WarehouseEggDataDTO> WarehouseEggData { get; set; }
        public int TotalHatchableEggs { get; set; }
        public int TotalCommercialEggs { get; set; }
        public int TotalEliminatedBrokenEggs { get; set; }
        public int TotalEggs { get; set; }
    }

    public class WarehouseEggDataDTO
    {
        public Guid WarehouseId { get; set; }
        public List<ClassifiedEggDTO> ClassifiedEggs { get; set; } = new List<ClassifiedEggDTO>();
        
        // Ovos Incubáveis (Hatchable Eggs)
        public int CleanNestEggs { get; set; }
        public int DirtyNestEggs { get; set; }
        public int BedEggs { get; set; }

        // Ovos Comerciais (Commercial Eggs)
        public int DoubleYolkEggs { get; set; }
        public int SmallEggs { get; set; }
        public int DefectiveEggs { get; set; }
        public int DirtyRolledEggs { get; set; }
        public int CrackedEggs { get; set; }
        public int ThinShellEggs { get; set; }
        public int EliminatedBrokenEggs { get; set; }
    }
}