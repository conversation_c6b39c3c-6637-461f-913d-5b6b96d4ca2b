using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.HenBatchViewModel;

namespace WebApp.Models
{
    public class HenBatchViewModel : AbstractContainerViewModel
    {
        public HenStage HenStage { get; set; }

        [Display(Name = Lang.StartDateLabel)]
        public string DateStart { get; set; }

        [Display(Name = Lang.ReportingStartDateLabel)]
        public string ReportingStartDate { get; set; }

        [Display(Name = Lang.OpeningDateLabel)]
        public string OpeningDate { get; set; }

        [Display(Name = Lang.DateEndLabel)]
        public string DateEnd { get; set; }

        [Display(Name = Lang.FirstProductionDateLabel)]
        public string FirstProductionDate { get; set; }

        [Required(ErrorMessage = Lang.GeneticRequired)]
        [Display(Name = Lang.GeneticLabel)]
        public string GeneticId { get; set; }

        [Display(Name = Lang.GeneticLabel)]
        public string GeneticName { get; set; }

        [Required(ErrorMessage = Lang.BatchWeekNumberRequired)]
        [Display(Name = Lang.BatchWeekNumberLabel)]
        [Range(1, 150, ErrorMessage = Lang.BatchWeekNumbererValidation)]
        public int? BatchWeekNumber { get; set; }

        [Display(Name = Lang.HenBatchOriginLabel)]
        public string HenBatchOrigin { get; set; }

        [Range(0, 1000000, ErrorMessage = Lang.CapacityValidation)]
        [Display(Name = Lang.HenAmountLabel)]
        public int HenAmountFemale { get; set; }
        public int HenAmountMale { get; set; }

        [StringLength(220, ErrorMessage = Lang.DescriptionStringLength)]
        [Display(Name = Lang.DescriptionLabel)]
        public string Description { get; set; }

        [Display(Name = Lang.SampleCageLabel)]
        public List<SampleCageViewModel> SampleCages { get; set; }

        [Display(Name = Lang.FormulaLabel)]
        [Required(ErrorMessage = Lang.FormulaRequired)]
        public List<string> FormulasIds { get; set; }

        [Display(Name = Lang.CategoryLabel)]
        public string CategoryId { get; set; }

        [Display(Name = Lang.CategoryLabel)]
        public string CategoryName { get; set; }

        [Display(Name = Lang.EggMaterialLabel)]
        public string EggMaterialId { get; set; }

        public EggColor? EggColor { get; set; }


        public int InitialAmountFemale { get; set; }
        public int InitialAmountMale { get; set; }

        public DistributableViewModel Distribution { get; set; }

        [Display(Name = Lang.CompanyLabel)]
        public string CompanyId { get; set; }
        [Display(Name = Lang.CompanyLabel)]
        public string Company { get; set; }

        [Display(Name = Lang.FarmLabel)]
        public string Farm { get; set; }

        public bool HasReportsOrReceivedBirds { get; set; }

        public List<SpikingReportsViewModel> SpikingReports { get; set; }
        public bool HasTenantHenBatchCategory { get; set; }
        public bool TenantAllowsToReceiveBirdsBeforeStartDate { get; set; }

        [Display(Name = Lang.StartDateOfWeekOneLabel)]
        public string StartDateOfWeekOne { get; set; }
        
        [Display(Name = Lang.AllowBeginReportsOnAnyDateLabel)]
        public bool AllowBeginReportsOnAnyDate { get; set; }

        public HenBatchViewModel()
        {
            Id = Guid.NewGuid().ToString();
            SampleCages = new List<SampleCageViewModel>();
            ContainerProperties = new ContainerPropertiesViewModel();
            Distribution = new DistributableViewModel();
            HasReportsOrReceivedBirds = false;
        }


        public HenBatchViewModel(HenStage henStage)
        {
            Id = Guid.NewGuid().ToString();
            HenStage = henStage;
            BatchWeekNumber = 1;
            SampleCages = new List<SampleCageViewModel>();
            ContainerProperties = new ContainerPropertiesViewModel();
            Distribution = new DistributableViewModel();
            HasReportsOrReceivedBirds = false;
        }

        public HenBatchViewModel(HenBatch henBatch, List<HenBatch> children, IStringLocalizer<SharedResources> localizer, List<SpikingReceptionReport> spikingReceptionReports = null)
        {
            Id = henBatch.Id.ToString();
            Name = henBatch.Name;
            Company = henBatch.Company?.BusinessName;
            CompanyId = henBatch.CompanyId.HasValue ? henBatch.CompanyId.ToString() : null;
            Farm = henBatch.Farm.Name;
            DateStart = henBatch.DateStart.Value.ToString();
            ReportingStartDate = henBatch.ReportingStartDate?.ToString();
            OpeningDate = henBatch.OpeningDate.HasValue ? henBatch.OpeningDate.Value.ToString() : null;
            DateEnd = henBatch.DateEnd.HasValue ? henBatch.DateEnd.Value.ToString() : null;
            FirstProductionDate = henBatch.FirstProductionDate.HasValue ? henBatch.FirstProductionDate.Value.ToString() : null;
            Description = henBatch.Description;
            HenStage = henBatch.HenStage;
            GeneticId = henBatch.GeneticId.ToString();
            GeneticName = henBatch.Genetic.Name;
            CategoryId = henBatch.CategoryId.ToString();
            EggMaterialId = henBatch.EggMaterialId.ToString();
            ContainerProperties = new ContainerPropertiesViewModel(henBatch, localizer);
            AllowBeginReportsOnAnyDate = henBatch.AllowBeginReportsOnAnyDate;
            StartDateOfWeekOne = henBatch.StartDateOfWeekOne.HasValue
                ? henBatch.StartDateOfWeekOne.Value.ToString()
                : "";
            HasReportsOrReceivedBirds =children.Any()
                ?children.SelectMany(c => c.Reports).Any() || children.Sum(hb => hb.InitialHenAmount) > 0
                : henBatch.Reports.Any() || henBatch.InitialHenAmount > 0;

            if (henBatch.FormulasConsumed != null && henBatch.FormulasConsumed.Any())
                FormulasIds = henBatch.FormulasConsumed.Select(f => f.FormulaId.ToString()).ToList();
            
            if (henBatch.Category != null)
            {
                CategoryName = henBatch.Category.Name;
            }

            InitialAmountFemale = henBatch.InitialHenAmountFemale;
            InitialAmountMale = henBatch.InitialHenAmountMale;
            HenAmountFemale = henBatch.HenAmountFemale;
            HenAmountMale = henBatch.HenAmountMale;
            BatchWeekNumber = henBatch.BatchWeekNumber;

            SampleCages = new List<SampleCageViewModel>();
            if (henBatch.SampleCages != null && henBatch.SampleCages.Count > 0)
            {
                foreach (SampleCage cage in henBatch.SampleCages)
                {
                    SampleCages.Add(new SampleCageViewModel(cage));
                }
            }

            Distribution = new DistributableViewModel()
            {
                Distributions = new List<DistributionViewModel>()
            };

            if (children.Any())
            {
                Distribution.Distributions.AddRange(
                    children.Select(c => new DistributionViewModel()
                    {
                        Id = c.Id.ToString(),
                        DetailedName = c.DetailedName,
                        LineId = c.LineId.ToString(),
                        HenAmount = c.HenAmountFemale + c.HenAmountMale,
                        Capacity = decimal.Round(c.AcceptedMaterialType.First(amt => amt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue, 2, MidpointRounding.AwayFromZero) ,
                        CategoryId = c.CategoryId.ToString(),
                        CategoryName = c.Category != null? c.Category.Name: "",
                        ClusterId = c.Line != null && c.Line.Warehouse != null ? c.Line.Warehouse.ClusterId.ToString() : null
                    }).ToList());
            }
            else
            {
                Distribution.Distributions.Add(
                    new DistributionViewModel()
                    {
                        Id = Guid.NewGuid().ToString(),
                        DetailedName = henBatch.DetailedName,
                        LineId = henBatch.LineId.ToString(),
                        HenAmount = henBatch.InitialHenAmountFemale + henBatch.InitialHenAmountMale,
                        Capacity = henBatch.AcceptedMaterialType.First(amt => amt.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue,
                        CategoryId = henBatch.CategoryId.ToString(),
                        CategoryName = henBatch.Category!= null? henBatch.Category.Name : "",
                        ClusterId = henBatch.Line != null && henBatch.Line.Warehouse != null ? henBatch.Line.Warehouse.ClusterId.ToString() : null
                    });
            }

            if (spikingReceptionReports != null && spikingReceptionReports.Any())
                SpikingReports = spikingReceptionReports.Select(srr => new SpikingReportsViewModel(srr)).ToList();
        }

        public override Container MapViewModel()
        {
            DateTime dateStart = string.IsNullOrEmpty(OpeningDate) ? DateTime.Parse(DateStart) : DateTime.Parse(OpeningDate).AddDays(1);
            HenBatch henBatch = new HenBatch()
            {
                Id = Id != null ? Guid.Parse(Id) : Guid.Empty,
                Name = Name,
                BatchWeekNumber = BatchWeekNumber.Value,
                DateStart = dateStart,
                ReportingStartDate = string.IsNullOrEmpty(ReportingStartDate) ? dateStart : DateTime.Parse(ReportingStartDate),
                OpeningDate = string.IsNullOrEmpty(OpeningDate) ? (DateTime?)null : DateTime.Parse(OpeningDate),
                Description = Description,
                SampleCages = new List<SampleCage>(),
                AcceptedMaterialType = new List<ContainerMaterialType>(),
                AllowBeginReportsOnAnyDate = AllowBeginReportsOnAnyDate,
                StartDateOfWeekOne = HenStage == HenStage.Breeding
                ? dateStart
                : string.IsNullOrEmpty(StartDateOfWeekOne)
                    ? CalculateDate(dateStart, BatchWeekNumber.Value)
                    : DateTime.Parse(StartDateOfWeekOne)
            };

            //Genetic
            if (!string.IsNullOrEmpty(GeneticId)) henBatch.GeneticId = new Guid(GeneticId);

            //Category 
            if (!string.IsNullOrEmpty(CategoryId)) henBatch.CategoryId = new Guid(CategoryId);

            //Formulas
            if (FormulasIds != null && FormulasIds.Any())
                henBatch.FormulasConsumed =
                    FormulasIds.Select(f => new HenBatchFormula() { FormulaId = Guid.Parse(f), HenBatchId = henBatch.Id }).ToList();

            BuildContainerProperties(henBatch);

            return henBatch;
        }

        public HenBatch ToEntity(HenStage henStage)
        {
            HenBatch henBatch = ToEntity() as HenBatch;
            henBatch.HenStage = henStage;

            return henBatch;
        }

        public (HenBatch parent, List<HenBatch> children) ToEntities(HenStage henStage)
        {
            HenBatch parent = ToEntity() as HenBatch;
            parent.HenStage = henStage;
            parent.CompanyId = Guid.Parse(CompanyId);
            List<HenBatch> children = new List<HenBatch>();

            if (Distribution.Distributions.Count() > 1)
            {
                foreach (DistributionViewModel item in Distribution.Distributions)
                {
                    Id = item.Id;
                    ContainerProperties.Id = item.Id;
                    ContainerProperties.DetailedName = item.DetailedName;
                    SetBirdsCapacity(item.Capacity);
                    HenBatch child = ToEntity() as HenBatch;
                    child.LineId = Guid.Parse(item.LineId);
                    child.ParentId = parent.Id;
                    child.CompanyId = Guid.Parse(CompanyId);
                    child.HenStage = henStage;
                    if (HasTenantHenBatchCategory)
                        child.CategoryId = Guid.Parse(item.CategoryId);
                    children.Add(child);
                }
            }
            else
            {
                SetBirdsCapacity(Distribution.Distributions.First().Capacity);
                parent.DetailedName = Distribution.Distributions.First().DetailedName;
                parent.LineId = Guid.Parse(Distribution.Distributions.First().LineId);
                if (HasTenantHenBatchCategory)
                    parent.CategoryId = Guid.Parse(Distribution.Distributions.First().CategoryId);
            }

            return (parent, children);
        }

        private void SetBirdsCapacity(decimal capacity)
        {
            MaterialTypeActionsViewModel materialTypeAction = ContainerProperties.MaterialTypeActions
                .First(mta => Guid.Parse(mta.MaterialTypeId) == MaterialTypes.ActivoBiologicoProductivoAve);

            materialTypeAction.CapacityStandarizedValue = capacity;
            materialTypeAction.AllowedCapacity = 0;
            materialTypeAction.MaximumCapacity = 0;
            materialTypeAction.RecommendedCapacity = 0;
        }

        private DateTime CalculateDate(DateTime DateStart, int BatchWeekNumber) 
        {
            return DateStart.AddDays((-BatchWeekNumber + 1) * (int)TimePeriodType.Week);
        }

        public HenBatchFilterDTO ToDTO()
        {
            HenBatchFilterDTO data = new HenBatchFilterDTO()
            {
                HenStage = HenStage,
                SelectedFarm = !string.IsNullOrEmpty(ContainerProperties.FarmId) ? Guid.Parse(ContainerProperties.FarmId) : (Guid?)null,
                GeneticId = !string.IsNullOrEmpty(GeneticId) ? Guid.Parse(GeneticId) : (Guid?)null,
            };

            return data;
        }

        public override IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            IStringLocalizer<SharedResources> localizer = (IStringLocalizer<SharedResources>)validationContext.GetService(typeof(IStringLocalizer<SharedResources>));

            IEnumerable<ValidationResult> errors = base.Validate(validationContext);

            var materialTypeCapacities = (from cp in ContainerProperties.MaterialTypeActions
                .Where(mta => Guid.Parse(mta.MaterialTypeId) == MaterialTypes.ActivoBiologicoProductivoAve)
                                         group cp by 1 into g
                                         select new
                                         {
                                             CapacityStandarizedValue = g.Sum(x => x.CapacityStandarizedValue),
                                             AllowedCapacity = g.Sum(x => x.AllowedCapacity),
                                             MaximumCapacity = g.Sum(x => x.MaximumCapacity),
                                             RecommendedCapacity = g.Sum(x => x.RecommendedCapacity)
                                         })
                                         .First();

            decimal distributionsCapacity = Distribution.Distributions.Sum(d => d.Capacity);

            if (materialTypeCapacities.CapacityStandarizedValue != 0 && distributionsCapacity != materialTypeCapacities.CapacityStandarizedValue)
                errors = errors.Append(new ValidationResult(localizer[Lang.MaxCapacityValidation]));

            if (string.IsNullOrEmpty(ContainerProperties.Code))
                errors = errors.Append(new ValidationResult(localizer[Lang.CodeRequired], new string[1] { "ContainerProperties.Code" }));

            for (int i = 0; i < Distribution.Distributions.Count(); i++)
            {
                //if (Distribution.Distributions[i].HenAmount > Distribution.Distributions[i].Capacity)
                //    errors = errors.Append(new ValidationResult(localizer[Lang.CapacityError], new string[1] { "Distribution.Distributions[" + i + "].Capacity" }));

                if (Distribution.Distributions[i].Capacity < 0)
                    errors = errors.Append(new ValidationResult(localizer[Lang.CapacityZeroError], new string[1] { "Distribution.Distributions[" + i + "].Capacity" }));

                if (HasTenantHenBatchCategory && string.IsNullOrEmpty(Distribution.Distributions[i].CategoryId))
                    errors = errors.Append(new ValidationResult(localizer[Lang.CategoryRequired], new string[1] { "Distribution.Distributions[" + i + "].CategoryId" }));
            }

            // The required dates are defined by the tenant configuration. 
            if (TenantAllowsToReceiveBirdsBeforeStartDate)
            {
                if (string.IsNullOrEmpty(OpeningDate))
                    errors = errors.Append(new ValidationResult(localizer[Lang.OpeningDateRequired], new string[1] { "OpeningDate" }));     
                if (string.IsNullOrEmpty(ReportingStartDate))
                    errors = errors.Append(new ValidationResult(localizer[Lang.ReportingStartDateRequired], new string[1] { "ReportingStartDate" }));
            }

            if (HenStage == HenStage.Laying && !string.IsNullOrEmpty(StartDateOfWeekOne) && !string.IsNullOrEmpty(DateStart) && Convert.ToDateTime(DateStart) < Convert.ToDateTime(StartDateOfWeekOne))
                errors = errors.Append(new ValidationResult(localizer[Lang.StartDateOfWeekOneValidation]));
            if (HenStage == HenStage.Laying && !string.IsNullOrEmpty(StartDateOfWeekOne) && !string.IsNullOrEmpty(OpeningDate) && Convert.ToDateTime(OpeningDate) < Convert.ToDateTime(StartDateOfWeekOne))
                errors = errors.Append(new ValidationResult(localizer[Lang.StartDateOfWeekOneOpeningDateValidation]));

            // ReportingStartDate valid range is defined by the value of the OpeningDate
            if (!string.IsNullOrEmpty(OpeningDate))
            {
                DateTime openingDate =  DateTime.Parse(OpeningDate);
                if (!string.IsNullOrEmpty(ReportingStartDate) && (openingDate.AddDays(6) < DateTime.Parse(ReportingStartDate) || DateTime.Parse(ReportingStartDate) < openingDate.AddDays(-6)))
                    errors = errors.Append(new ValidationResult(localizer[Lang.ReportingStartDateError], new string[1] { "ReportingStartDate" }));
            }

            return errors;
        }
    }
}