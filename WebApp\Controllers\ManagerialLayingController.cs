using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.BusinessLogic.DTOs.Genetic;
using Domain.Logic.BusinessLogic.DTOs.FoodInventoryDTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WebApp.Attributes;
using WebApp.Models;
using WebApp.WebTools.Charts;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.LayingController;
using LangGenetic = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.GeneticReportController;
using LangStatus = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.WeightUniformityReportController;
using LangGeneticReport = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.GeneticReportController;

namespace WebApp.Controllers
{
    [AuthorizeAnyRoles(Roles.BackofficeSuperAdministrator, Roles.ManagerialDashboardLaying, Roles.BackofficeLayingDashboard)]
    public class ManagerialLayingController : DashboardController
    {
        private readonly ICompanyService companyService;
        private readonly IFarmService farmService;
        private readonly IHenReportBusinessLogic henReportBusinessLogic;
        private readonly ILineService lineService;
        private readonly ISearaLayingChartsBusinessLogic searaLayingChartsBusinessLogic;
        private readonly ISearaChartsBusinessLogic searaChartsBusinessLogic;
        private readonly IOperationContext operationContext;
        private readonly IMaterialTypeService materialTypeService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IService<GeneticsParametersReference> geneticsParametersReferenceService;
        private readonly RazorViewRender razorViewRender;
        private readonly IRegionalService regionalService;
        private readonly ICommonChartsBusinessLogic commonChartsBusinessLogic;
        private readonly IInventoryBusinessLogic inventoryBusinessLogic;
        private readonly IHenBatchService henBatchService;

        public ManagerialLayingController(
            IExceptionManager exceptionManager,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IHenBatchService henBatchService,
            IStringLocalizer<SharedResources> localizer,
            IMessageBusinessLogic messageBusinessLogic,
            IStatisticsBusinessLogic statisticsBusinessLogic,
            ICommonChartsBusinessLogic commonChartsBusinessLogic,
            IInventoryBusinessLogic inventoryBusinessLogic,
            IHolidayService holidayService,
            IContainerService<Container> containerService,
            ITaskEntityService taskEntityService,
            IUserService<ApplicationUser> userService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IHappeningBusinessLogic happeningBusinessLogic,
            IMaterialTypeService materialTypeService,
            IOperationContext operationContext,
            ILogger logger,
            ICompanyService companyService,
            IFarmService farmService,
            IHenReportBusinessLogic henReportBusinessLogic,
            ILineService lineService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IService<GeneticsParametersReference> geneticsParametersReferenceService,
            RazorViewRender razorViewRender,
            ISearaLayingChartsBusinessLogic searaLayingChartsBusinessLogic,
            ISearaChartsBusinessLogic searaChartsBusinessLogic,
            IRegionalService regionalService)
            : base(
                  exceptionManager,
                  henBatchBusinessLogic,
                  henBatchService,
                  farmService,
                  localizer,
                  messageBusinessLogic,
                  statisticsBusinessLogic,
                  holidayService,
                  containerService,
                  taskEntityService,
                  userService,
                  fileService,
                  happeningBusinessLogic,
                  materialTypeService,
                  operationContext,
                  logger)
        {
            this.commonChartsBusinessLogic = commonChartsBusinessLogic;
            this.inventoryBusinessLogic = inventoryBusinessLogic;
            this.companyService = companyService;
            this.farmService = farmService;
            this.operationContext = operationContext;
            this.henReportBusinessLogic = henReportBusinessLogic;
            this.lineService = lineService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.geneticsParametersReferenceService = geneticsParametersReferenceService;
            this.razorViewRender = razorViewRender;
            this.searaLayingChartsBusinessLogic = searaLayingChartsBusinessLogic;
            this.searaChartsBusinessLogic = searaChartsBusinessLogic;
            this.regionalService = regionalService;
            this.henBatchService = henBatchService;
        }

        public IActionResult Index()
        {
            ViewData["Title"] = localizer[Lang.IndexTitle];
            return View();
        }

        public IActionResult Inventory()
        {
            ViewData["Title"] = localizer[Lang.ManagerialInventoryTitle];

            InitFilterSelectsInventory(AreaEnum.Laying);

            ViewData["FeedStockStatusLabels"] = new Dictionary<string, string>
            {
                { "danger", "Crítico (≤ 3 dias)" },
                { "warning", "Atenção (≤ 7 dias)" },
                { "success", "Normal (> 7 dias)" }
            };

            return View();
        }

        public IActionResult Dashboard()
        {
            AreaEnum area = AreaEnum.Laying;
            List<AreaEnum> areas = new List<AreaEnum>
            {
                area
            };
            ViewData["Area"] = area;
            ViewData["UserIsAdmin"] = this.operationContext.UserIsInAnyRole(Roles.BackofficeTaskAdministrator,
                                                                            Roles.BackofficeTaskUser,
                                                                            Roles.BackofficeSuperAdministrator,
                                                                            Roles.BackofficeLayingAdministrator).ToString();
            ViewData["DashboardsResources"] = JsLocalizer.GetLocalizedResources(JsLang.LayingDashboard, this.localizer);
            ViewData["Title"] = localizer[Lang.ManagerialDashboardTitle];
            //Calendar
            InitCalendar(areas);

            // Performance chart filter initialization
            InitLists();
            var today = DateTime.Now;
            var minDayDefault = today.AddMonths(-2);

            ViewData["Today"] = today.ToString("MM/yyyy");
            ViewData["MinDayDefault"] = minDayDefault.ToString("MM/yyyy");

            // Histogram filter initialization
            ViewData["DefaultFilterParameters"] = new FilterDataDTO
            {
                HenBatchHistogramWeekNumberBinLimit1 = HistogramDefaultLimits.LayingLimit1,
                HenBatchHistogramWeekNumberBinLimit2 = HistogramDefaultLimits.LayingLimit2,
                HenBatchHistogramWeekNumberBinLimit3 = HistogramDefaultLimits.LayingLimit3,
                HenBatchHistogramWeekNumberBinLimit4 = HistogramDefaultLimits.LayingLimit4
            };

            Guid userId = this.operationContext.GetUserId();
            var user = userService.GetAll(filterByTenant: true).Where(u => u.Id == userId).FirstOrDefault();
            ViewData["UserLanguage"] = user.Language;
            ViewData["HenBatchStatus"] = GetStatusOptions();

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> GetSearaCards(FilterDataDTO filters)
        {
            List<(string, int)> viewList = new List<(string, int)>();

            if (!User.Identity.IsAuthenticated)
            {
                return Unauthorized();
            }

            ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
            DateTime dateFrom = filters.MinDate != null ? filters.MinDate.Value : DateTime.Today.AddDays(-15);
            DateTime dateTo = filters.MaxDate != null ? filters.MaxDate.Value : DateTime.Today;
            if (dateFrom > dateTo)
            {
                return BadRequest("Data final não pode ser anterior à data inicial");
            }

            List<Guid> henBatchIds = new List<Guid>();
            if (filters.HenBatchId.HasValue)
            {
                henBatchIds.Add(filters.HenBatchId.Value);
            }
            else
            {
                henBatchIds = this.searaLayingChartsBusinessLogic.GetHenBatchIdsFromFilters(filters).ToList();
            }

            DashboardInformativeCardsDTO informativeDTO = this.statisticsBusinessLogic.GetSearaLayingCardsData(henBatchIds,
                dateFrom,
                dateTo,
                filters.StartWeek == null ? 0 : filters.StartWeek.Value,
                filters.EndWeek == null ? 0 : filters.EndWeek.Value);

            List<SimpleDashboardInformativeCardDTO> simpleCardDTO = new List<SimpleDashboardInformativeCardDTO>();
            simpleCardDTO.AddRange(informativeDTO.SimpleCardDTOs);

            simpleCardDTO = simpleCardDTO
                .GroupBy(s => s.Title)
                .Select(sg =>
                {
                    var card = new SimpleDashboardInformativeCardDTO
                    {
                        Title = sg.Key,
                        IsSubtitle = sg.First().IsSubtitle,
                        Text = sg.First().Text,
                        SubtitleBold = sg.First().SubtitleBold,
                        SubtitleSimple = sg.First().SubtitleSimple,
                        ValueColor = sg.First().ValueColor,
                        Icon = sg.First().Icon,
                        IconColor = sg.First().IconColor,
                        SubtitleBoldColor = sg.First().SubtitleBoldColor,
                        SubtitleSimpleColor = sg.First().SubtitleSimpleColor,
                        IsSimple = sg.First().IsSimple
                    };

                    decimal avgValue = sg.Sum(s => s.NumberValue) != 0 ? sg.Average(s => s.NumberValue) : 0;

                    if (sg.Key == this.localizer[Lang.HenBatchWeekCardTitle])
                    {
                        card.Value = avgValue != 0 ? Math.Round(avgValue, 2).ToString("F2") : "";
                    }
                    else
                    {
                        card.Value = avgValue != 0 ? avgValue.ToString() : "";
                    }
                    card.NumberValue = avgValue;

                    return card;
                })
                .ToList();

            foreach (var dtos in simpleCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/SimpleDashboardInformativeCard/Default", dtos), 0));

            return Ok(new
            {
                viewList
            });
        }

        #region Charts

        [HttpPost]
        public IActionResult GetSearaLayingFeedIntakeGADChart(FilterDataDTO filters)
        {
            try
            {
                DashboardLineOrBarChartDTO chartDTO = this.searaLayingChartsBusinessLogic.GetSearaLayingFeedIntakeGADChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                // return ChartLoadingError(ex);
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public IActionResult GetSearaMaleFemaleChart(FilterDataDTO filters)
        {
            try
            {
                DashboardLineOrBarChartDTO chartDTO = this.searaLayingChartsBusinessLogic.GetSearaMaleFemaleChart(filters);
                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                // return ChartLoadingError(ex);
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public IActionResult GetGeneticChart(FilterDataDTO filters)
        {
            // bool isParentHenBatch = Convert.ToBoolean(isParent);
            HenBatch henBatch = henBatchService.Get((Guid)filters.HenBatchId);

            try
            {
                GeneticGraphicResponseDTO chartData = this.searaLayingChartsBusinessLogic.GetGeneticChartData(filters);
                PerformanceStandardsChart chartDTO = GetPerformanceStandardsCharts(chartData);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                if (ex.Message != null)
                {
                    return new JsonResult(new { Message = ex.Message });
                }
                return new JsonResult(new { Message = "Não há dados para exibir" });
            }
        }

        [HttpPost]
        public IActionResult GetOIFCChart(FilterDataDTO filters)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                DashboardLineOrBarChartDTO chartDTO = this.searaLayingChartsBusinessLogic.GetOIFCChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public IActionResult GetSaleableChicksChart(FilterDataDTO filters)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                DashboardLineOrBarChartDTO chartDTO = this.searaLayingChartsBusinessLogic.GetSaleableChicksChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetBedEggsChart(FilterDataDTO filters, CancellationToken cancellationToken)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                var chartDTO = await searaLayingChartsBusinessLogic.GetBedEggsChartAsync(filters, cancellationToken);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetDataForEggQualityChart(FilterDataDTO filters, CancellationToken cancellationToken)
        {
            try
            {
                DashboardLineOrBarChartDTO chartDTO = await searaLayingChartsBusinessLogic.GetDataForEggQualityChartAsync(filters,  cancellationToken);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public IActionResult GetViabilityChart(FilterDataDTO filters)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                DashboardLineOrBarChartDTO chartDTO = this.searaLayingChartsBusinessLogic.GetViabilityChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingPerformanceChart(FilterDataDTO filters)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                var performanceResult = this.searaLayingChartsBusinessLogic.PerformanceChart(filters, HenStage.Laying);
                var viabilityChart = performanceResult.Item2;

                // Get the laying hatchering and fertility chart data with date filters
                var layingChart = this.searaLayingChartsBusinessLogic.GetLayingHatcheringAndFertilityChartAsync(filters).GetAwaiter().GetResult();

                // Create the complete chart DTO structure
                var chartDTO = new PerformanceSearaChartsDTO
                {
                    ViabilityChart = viabilityChart,
                    LayingChart = layingChart
                };

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetLayingHatcheringAndFertilityChart(FilterDataDTO filters, CancellationToken cancellationToken)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                var layingChart = await searaLayingChartsBusinessLogic.GetLayingHatcheringAndFertilityChartAsync(filters, cancellationToken);

                return new JsonResult(new { chartDTO = layingChart });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "Não há dados para exibir"
                });
            }
        }

        [HttpPost]
        public IActionResult GetPerformanceChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                // When a specific hen batch is selected, we need to get both parent and child batches
                // When "Todos" (All) is selected, we get all batches regardless of parent/child relationship
                bool includeParents = !filters.HenBatchId.HasValue; // If no specific batch selected, include parents
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, includeParents);

                // If a specific batch is selected, also include its related batches (parent or children)
                if (filters.HenBatchId.HasValue)
                {
                    List<Guid> relatedBatches = GetHenBatchIdsFromFilters(filters, !includeParents);
                    henBatches.AddRange(relatedBatches);
                    henBatches = henBatches.Distinct().ToList();
                }

                List<List<HenBatchStatusDTO>> data = new List<List<HenBatchStatusDTO>>();
                DashboardLineOrBarChartDTO chartDTO = new DashboardLineOrBarChartDTO();

                (data, chartDTO) = this.searaLayingChartsBusinessLogic.PerformanceChart(filters, filters.HenStage);

                return new JsonResult(new { data, chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        #endregion

        #region Filter lists initialization
        private void InitLists()
        {
            if (!User.Identity.IsAuthenticated) return;

            HenStage henStage = HenStage.Laying;

            ContainerFilterDTO items = henBatchBusinessLogic.GetAllContainerFilter(henStage, null, activeBatches: null, orderByNew: true);
            ViewData["Companies"] = GetCompanies();
            var companyIds = companyService.GetAll().Select(c => c.Id).ToList();
            var farmIds = farmService.GetAll().Select(c => c.Id).ToList();
            ViewData["Supervisors"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.SupervisorId, farmIds);
            ViewData["Extensionists"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.TechnicianId, farmIds);
            ViewData["Productors"] = GetFarmsByUnitId(null);
            ViewData["Lines"] = items.Line;
            ViewData["HenBatchesOrderByNew"] = items.ParentHenBatch;
            ViewData["Warehouses"] = items.Warehouse;
            ViewData["Regionals"] = GetRegionalsByTenantId(this.operationContext.GetUserTenantId().Value) ?? new List<SelectListItem>();
            ViewData["Units"] = GetUnitsByRegionalList(null);
            ViewData["GeneticsList"] = GetGeneticsByTenantId(this.operationContext.GetUserTenantId().Value);
            ViewData["Genders"] = GetGenderOptions();
        }

        private void InitFilterSelectsInventory(AreaEnum? area = null)
        {
            if (!User.Identity.IsAuthenticated) return;

            ViewData["Regionals"] = GetRegionalsByTenantId(this.operationContext.GetUserTenantId().Value) ?? new List<SelectListItem>();
            ViewData["Units"] = GetUnitsByRegionalList(null);
            var companyIds = companyService.GetAll().Select(c => c.Id).ToList();
            var farmIds = farmService.GetAll().Select(c => c.Id).ToList();
            ViewData["Supervisors"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.SupervisorId, farmIds);
            ViewData["Extensionists"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.TechnicianId, farmIds);
            ViewData["Productors"] = GetFarmsByUnitId(null);

            ViewData["MaterialTypes"] = GetSelectListFromArray();

            // Get the formula material type ID
            var materialTypeFormula = ((List<SelectListItem>)ViewData["MaterialTypes"])
                .FirstOrDefault(mt => mt.Text.Equals("Fórmula", StringComparison.OrdinalIgnoreCase));

            Guid? formulaTypeId = null;
            if (materialTypeFormula != null && Guid.TryParse(materialTypeFormula.Value, out Guid parsedGuid))
            {
                formulaTypeId = parsedGuid;
            }

            // Get materials filtered by the formula type
            ViewData["Materials"] = GetMaterialsByType(area ?? AreaEnum.Laying, formulaTypeId);
        }

        private List<SelectListItem> GetGeneticsByTenantId(Guid tenantId)
        {
            var genetics = henBatchPerformanceService.GetAllFull()
                      .Where(hbp => hbp.HenBatch.TenantId == tenantId && hbp.GeneticId != null)
                      .Select(hbp => hbp.HenBatch.Genetic)
                      .Distinct()
                      .ToList();

            return genetics.Select(g => new SelectListItem
            {
                Text = g.Name,
                Value = g.Id.ToString()
            }).ToList();
        }


        private List<SelectListItem> GetRegionalsByTenantId(Guid tenantId)
        {
            return regionalService.GetRegionalsByTenantId(tenantId)
                .Select(r => new SelectListItem(r.Name, r.Id.ToString()))
                .ToList();
        }

        private List<SelectListItem> GetFarmsByUnitId(Guid? companyId)
        {
            var query = farmService.GetAll();

            var farmsWithLayingBatches = henBatchService.GetAll()
                .Where(hb => hb.HenStage == HenStage.Laying)
                .Select(hb => hb.FarmId)
                .Distinct();

            query = query.Where(f => farmsWithLayingBatches.Contains(f.Id));

            if (companyId.HasValue)
            {
                query = query.Where(f => f.CompanyId == companyId.Value);
            }

            return query
                .Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString()))
                .ToList();
        }

        public List<SelectListItem> GetSelectListFromArray()
        {
            var array = new MaterialTypeDictionary(localizer).GetById(
                    MaterialTypes.InsumoMateriaPrimaAlimentacionFormula
                );

            return array
                .Select(item => new SelectListItem
                {
                    Value = item.Key.ToString(),
                    Text = (item.Value.DisplayName.IndexOf("fórmula", StringComparison.OrdinalIgnoreCase) >= 0) ? "Fórmula" : item.Value.DisplayName
                })
                .ToList();
        }

        private List<SelectListItem> GetMaterialsByType(AreaEnum? area = null, Guid? materialTypeId = null)
        {
            return containerService.GetMaterialsByTypeOptions(area, materialTypeId);
        }


        private List<SelectListItem> GetGenderOptions()
        {
            return new List<SelectListItem>
    {
        new SelectListItem("Todos", ""),
        new SelectListItem("Machos", "Male"),
        new SelectListItem("Fêmeas", "Female")
    };
        }

        private List<SelectListItem> GetUnitsByRegionalList(Guid? regionalId)
        {
            IQueryable<Company> companies;
            if (regionalId.HasValue)
            {
                companies = companyService.GetCompaniesByRegional(regionalId.Value);
            }
            else
            {
                companies = companyService.GetAll();
            }
            return companies
                    .Select(c => new SelectListItem
                    {
                        Text = c.BusinessName,
                        Value = c.Id.ToString()
                    })
                    .ToList();
        }

        private List<SelectListItem> GetSupervisorsByUnit(Guid? companyId)
        {
            if (!companyId.HasValue)
                return new List<SelectListItem>();

            var users = userService.GetByUnit(companyId.Value);

            return users
                .Where(u => u.SecurityProfiles.Any(sp => sp.SecurityProfileId == SecurityProfiles.SupervisorId) && !u.Inactivated)
                .Select(u => new SelectListItem
                {
                    Text = $"{u.Name} {u.LastName}".Trim(),
                    Value = u.Id.ToString()
                })
                .ToList();
        }

        private List<SelectListItem> GetExtensionistsByUnit(Guid? companyId, Guid? supervisorId)
        {
            if (!companyId.HasValue)
                return new List<SelectListItem>();

            var extensionistsQuery = userService.GetByUnit(companyId.Value)
                .Where(u => u.SecurityProfiles.Any(sp => sp.SecurityProfileId == SecurityProfiles.TechnicianId) && !u.Inactivated);

            if (supervisorId.HasValue)
            {
                IQueryable<Guid?> extensionistsIdsBySupervisor = farmService.GetAll()
                    .Where(f => f.SupervisorId == supervisorId.Value)
                    .Select(f => f.TechnicianId)
                    .Distinct();
                extensionistsQuery = extensionistsQuery.Where(u => extensionistsIdsBySupervisor.Contains(u.Id));
            }

            return extensionistsQuery
                .Select(u => new SelectListItem
                {
                    Text = $"{u.Name} {u.LastName}".Trim(),
                    Value = u.Id.ToString()
                }).ToList();
        }


        private List<SelectListItem> GetHenBatchByStatus(bool? henBatchStatus)
        {
            HenStage henStage = HenStage.Laying;
            ContainerFilterDTO items = henBatchBusinessLogic.GetAllContainerFilter(henStage, null, activeBatches: henBatchStatus ?? null, orderByNew: true);

            return items.ParentHenBatch;
        }

        private List<SelectListItem> GetHenBatchByFarmId(Guid? farmId, bool? henBatchStatus)
        {
            HenStage henStage = HenStage.Laying;
            ContainerFilterDTO items = henBatchBusinessLogic.GetAllContainerFilter(henStage, farmId, activeBatches: henBatchStatus ?? null, orderByNew: true);

            return items.ParentHenBatch;
        }

        [HttpGet]
        public IActionResult GetInventoryCards(string supervisor, string extensionist, string productor, string regional, string unit, string materialType, string material, string sortBy)
        {
            if (!User.Identity.IsAuthenticated)
                return Unauthorized();

            var filters = new FilterStockCardsDTO
            {
                Supervisor = supervisor,
                Extensionist = extensionist,
                Productor = productor,
                Regional = regional,
                Unit = unit,
                MaterialType = materialType,
                Material = material,
                SortBy = sortBy
            };

            filters.HenStage = HenStage.Laying;
            var henBatchIds = inventoryBusinessLogic.GetHenBatchIds(filters, HenStage.Laying);

            var lastClosedWeekConsumptions = inventoryBusinessLogic.GetLastClosedWeekConsumptions(henBatchIds, null, HenStage.Laying);

            var materialsQuery = containerService.GetAll();
            materialsQuery = this.inventoryBusinessLogic.HandleFilters(materialsQuery, filters);

            if (!materialsQuery.Any())
            {
                return Json(new { Message = "Nenhum material encontrado para os filtros aplicados." });
            }

            var materialStockConsumption = inventoryBusinessLogic.GetMaterialStockConsumption(materialsQuery, lastClosedWeekConsumptions, sortBy);

            if (!string.IsNullOrEmpty(material))
            {
                var materialId = Guid.Parse(material);
                materialStockConsumption = materialStockConsumption
                    .Where(m => m.MaterialId == materialId)
                    .ToList();
            }

            return Json(materialStockConsumption);
        }

        [HttpGet]
        public async Task<IActionResult> GetFeedStockIndicators(string supervisor, string extensionist, string productor, string regional, string unit, string materialType, string material, string sortBy)
        {
            if (!User.Identity.IsAuthenticated)
                return Unauthorized();

            var filters = new FilterStockCardsDTO
            {
                Supervisor = supervisor,
                Extensionist = extensionist,
                Productor = productor,
                Regional = regional,
                Unit = unit,
                MaterialType = materialType,
                Material = material,
                SortBy = sortBy,
                HenStage = HenStage.Laying
            };

            try
            {
                var feedStockIndicators = await inventoryBusinessLogic.GetFeedStockIndicators(filters);

                if (!feedStockIndicators.Any())
                {
                    return Json(new { Message = "Nenhum estoque de ração encontrado para os filtros aplicados." });
                }

                // Add traffic light indicators based on days remaining
                var result = feedStockIndicators.Select(indicator => new
                {
                    indicator.MaterialId,
                    indicator.MaterialName,
                    indicator.TotalStock,
                    indicator.Unity,
                    indicator.DailyConsumption,
                    indicator.DaysRemaining,
                    AvailableUntilDate = indicator.AvailableUntilDate.ToString("dd/MM/yyyy"),
                    Status = GetStockStatus(Convert.ToInt32(indicator.DaysRemaining)),
                    ProducerName = indicator.ProducerName // Include producer name
                }).ToList();

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new { Error = ex.Message });
            }
        }

        private string GetStockStatus(int daysRemaining)
        {
            if (daysRemaining <= 3)
                return "danger";  // CSS class
            else if (daysRemaining <= 7)
                return "warning"; // CSS class
            else
                return "success"; // CSS class
        }

        [HttpGet]
        public IActionResult GetMaterialsByTypeAjax(Guid? materialTypeId)
        {
            var materials = GetMaterialsByType(AreaEnum.Laying, materialTypeId);
            return Ok(materials);
        }

        [HttpGet]
        public IActionResult GetUserContextFiltersAjax()
        {
            if (!User.Identity.IsAuthenticated)
                return Unauthorized();

            try
            {
                var userId = this.operationContext.GetUserId();
                var userCompanyIds = this.operationContext.GetUserCompanyIds();
                var userSiteIds = this.operationContext.GetUserSiteIds();

                // Get user's associated data
                var user = userService.GetAllFull().FirstOrDefault(u => u.Id == userId);

                var result = new
                {
                    regional = GetUserRegionals(user, userCompanyIds),
                    unit = GetUserUnits(user, userCompanyIds),
                    productor = GetUserProductors(user, userSiteIds),
                    supervisor = GetUserSupervisors(user, userCompanyIds, userSiteIds),
                    extensionist = GetUserExtensionists(user, userCompanyIds, userSiteIds)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error getting user context filters: {ex.Message}");
            }
        }

        [HttpGet]
        public IActionResult GetUnitsByRegionalAjax(Guid? regionalId)
        {
            var units = GetUnitsByRegionalList(regionalId);
            return Ok(units);
        }

        [HttpGet]
        public IActionResult GetSupervisorByUnitAjax(Guid? unitId)
        {
            var supervisors = GetSupervisorsByUnit(unitId);
            return Ok(supervisors);
        }

        [HttpGet]
        public IActionResult GetExtensionistsByUnitAjax(Guid? unitId, Guid? supervisorId)
        {
            var extensionists = GetExtensionistsByUnit(unitId, supervisorId);
            return Ok(extensionists);
        }

        [HttpGet]
        public IActionResult GetFarmsByUnitIdAjax(Guid? unitId)
        {
            //WARN: Farm e Productor nesse sentido são a mesma coisa
            var productors = GetFarmsByUnitId(unitId);
            return Ok(productors);
        }

        [HttpGet]
        public IActionResult GetHenBatchListByFarmIdAjax(Guid? farmId, bool? henBatchStatus)
        {
            var henBatches = GetHenBatchByFarmId(farmId, henBatchStatus);
            return Ok(henBatches);
        }

        [HttpGet]
        public IActionResult GetWarehousesByHenBatchAjax(Guid selectedHenBatch)
        {
            var warehouses = GetWarehousesByHenBatch(selectedHenBatch);
            return Ok(warehouses);
        }

        [HttpGet]
        public IActionResult GetLinesByWarehouseAjax(Guid? warehouseId, Guid? henBatchId)
        {
            var lines = GetLinesByWarehouse(warehouseId, henBatchId);
            return Ok(lines);
        }

        private List<SelectListItem> GetCompanies()
        {
            return this.companyService.GetAll().OrderBy(c => c.BusinessName)
                .Select(c => new SelectListItem(c.BusinessName, c.Id.ToString())).ToList();
        }

        private List<SelectListItem> GetStatusOptions()
        {
            List<SelectListItem> items = new List<SelectListItem>()
            {
                new SelectListItem(localizer[LangStatus.HenBatchStatusAll], "") { Selected = true },
                new SelectListItem(localizer[LangStatus.HenBatchStatusActive], "active"),
                new SelectListItem(localizer[LangStatus.HenBatchStatusClosed], "closed")
            };

            return items;
        }

        public List<SelectListItem> GetUsersByCompanyWithProfile(List<Guid> companyIds, Guid? profile, List<Guid> farmIds)
        {
            IQueryable<ApplicationUser> companyUsers = userService.GetAllFull();

            // Filtrar usuarios por compañías
            companyUsers = companyUsers.Where(au => au.Companies == null || !au.Companies.Any() ||
                                                    au.Companies.Any(c => companyIds.Contains(c.CompanyId)));

            // Obtener farmsActiveIds solo si el perfil requiere este filtro
            List<Guid?> farmsActiveIds = new List<Guid?>();
            Guid productorProfileId = Guid.Parse("155F3BD0-E1A9-667B-BB06-3A045F9214FB");
            if (profile == productorProfileId)
            {
                farmsActiveIds = henBatchService.GetAll()
                    .Where(hb => hb.HenStage == HenStage.Laying && hb.HenAmountFemale > 0 && hb.HenAmountMale > 0 && hb.Active)
                    .Where(hb => farmIds.Contains(hb.FarmId.Value))
                    .Select(hb => hb.FarmId).ToList();


                // Filtrar usuarios por farmsActiveIds
                companyUsers = companyUsers.Where(au => au.Sites.Any(c => farmsActiveIds.Contains(c.SiteId)));
            }

            // Filtrar usuarios por perfil de seguridad
            companyUsers = companyUsers.Where(au => au.SecurityProfiles.Any(sp => sp.SecurityProfileId == profile));

            // Excluir usuario de integración
            Guid tenantId = this.operationContext.GetUserTenantId().Value;
            Guid excludedUserId = Guid.Parse("daae7110-08a2-50d7-8149-39fb048e5702"); // Usuario de integración

            return companyUsers.Where(u => u.TenantId == tenantId && u.Id != excludedUserId)
                .Select(u => new SelectListItem(u.Name + " " + u.LastName, u.Id.ToString()))
                .ToList();
        }
        #endregion

        #region refresh inputs

        /// <summary>
        /// Gets lines from a hen warehouse to use in henreport index line filter.
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouse(Guid? warehouseId, Guid? henBatchId)
        {
            return henBatchBusinessLogic.GetLinesFiltered(HenStage.Laying, warehouseId, henBatchId);
        }

        /// <summary>
        /// Gets all warehouses by selected hen batch
        /// </summary>
        public List<SelectListItem> GetWarehousesByHenBatch(Guid selectedHenBatch)
        {
            return henBatchBusinessLogic.GetWarehousesByHenBatch(selectedHenBatch, HenStage.Laying);
        }

        #endregion

        private PerformanceStandardsChart GetPerformanceStandardsCharts(GeneticGraphicResponseDTO response)
        {
            //simple: one std value (no max and min). complex: two std values
            List<ChartDataPoint> simpleMortDataFemale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleMortCurrentDataFemale = new List<ChartDataPoint>();

            List<ChartDataPoint> simpleBodyWeightMaxDataFemale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleBodyWeightMaxDataMale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleBodyWeightCurrentDataFemale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleBodyWeightCurrentDataMale = new List<ChartDataPoint>();

            List<ChartDataPoint> simpleTemperatureMaxData = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleTemperatureMinData = new List<ChartDataPoint>();

            //keep track of largest and smallest values to build boundaries
            int minValueBodyWeight = 2800;
            int maxValueBodyWeight = 0;

            PerformanceStandardsChart chart = new PerformanceStandardsChart()
            {
                SimpleDatasets = new List<PerformanceStandardsSimpleDataset>(),
                HenStage = response.HenStage
            };

            //I will also have graphic data related to egg production
            List<ChartDataPoint> simpleCurrentHatchableEggsWeekly = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleCurrentFertileEggsWeekly = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleCurrentTotalProducedEggsWeekly = new List<ChartDataPoint>();

            List<ChartDataPoint> simpleCurrentHatchableEggsWeeklySTD = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleCurrentFertileEggsWeeklySTD = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleTotalProducedEggsWeeklySTD = new List<ChartDataPoint>();


            foreach (GeneticGraphicDTO parameters in response.Data.OrderBy(x => x.WeekNumber))
            {
                //Current
                simpleCurrentTotalProducedEggsWeekly.Add(new ChartDataPoint(parameters.WeekNumber, parameters.TotalProducedEggsWeekly));
                simpleCurrentHatchableEggsWeekly.Add(new ChartDataPoint(parameters.WeekNumber, parameters.HatchableEggsWeekly));
                simpleCurrentFertileEggsWeekly.Add(new ChartDataPoint(parameters.WeekNumber, parameters.FertileEggsWeekly));
                simpleBodyWeightCurrentDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightFemale));
                simpleBodyWeightCurrentDataMale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightMale));
                simpleMortCurrentDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MortalityFemale));
                simpleTemperatureMaxData.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MaxTemp));
                simpleTemperatureMinData.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MinTemp));
                //Standard
                simpleBodyWeightMaxDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightSTDMaxFemale));
                simpleBodyWeightMaxDataMale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightSTDMaxMale));
                simpleCurrentHatchableEggsWeeklySTD.Add(new ChartDataPoint(parameters.WeekNumber, parameters.HatchingEggsWeeklySTD));
                simpleCurrentFertileEggsWeeklySTD.Add(new ChartDataPoint(parameters.WeekNumber, parameters.FertileEggsWeeklySTD));
                simpleTotalProducedEggsWeeklySTD.Add(new ChartDataPoint(parameters.WeekNumber, parameters.TotalProducedEggsWeeklySTD));
                simpleMortDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MortalitySTDFemale));

                SetMinMaxValueBodyWeight(ref minValueBodyWeight, ref maxValueBodyWeight, parameters);

            }
            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[LangGeneticReport.TotalProducedEggsWeekly],
                    YAxisID = "performance",
                    BorderColor = "rgba(255,0,0,0.9)",
                    Data = CorrectedData(simpleTotalProducedEggsWeeklySTD),
                    CurrentData = CorrectedData(simpleCurrentTotalProducedEggsWeekly)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[LangGeneticReport.HatchableEggsWeekly],
                    YAxisID = "performance",
                    BorderColor = "rgba(68,194,37,0.9)",
                    Data = CorrectedData(simpleCurrentHatchableEggsWeeklySTD),
                    CurrentData = CorrectedData(simpleCurrentHatchableEggsWeekly)
                });
            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[LangGeneticReport.FertileEggsWeekly],
                    YAxisID = "performance",
                    BorderColor = "rgba(0,0,205,0.9)",
                    Data = CorrectedData(simpleCurrentFertileEggsWeeklySTD),
                    CurrentData = CorrectedData(simpleCurrentFertileEggsWeekly)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[LangGenetic.BodyWeightDataFemale],
                    YAxisID = "bodyWeight",
                    BorderColor = "rgba(255,215,0,0.9)",
                    Data = CorrectedData(simpleBodyWeightMaxDataFemale),
                    CurrentData = CorrectedData(simpleBodyWeightCurrentDataFemale)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[LangGenetic.BodyWeightDataMale],
                    YAxisID = "bodyWeight",
                    BorderColor = "rgba(255,140,0,0.9)",
                    Data = CorrectedData(simpleBodyWeightMaxDataMale),
                    CurrentData = CorrectedData(simpleBodyWeightCurrentDataMale)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[LangGenetic.MortalityDataFemale],
                    YAxisID = "mortality",
                    BorderColor = "rgba(0,0,0,0.9)",
                    Data = CorrectedData(simpleMortDataFemale),
                    CurrentData = CorrectedData(simpleMortCurrentDataFemale)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = this.localizer[LangGenetic.TempMaxData],
                    YAxisID = "performance",
                    BorderColor = "rgba(0,206,209,0.9)",
                    CurrentData = CorrectedData(simpleTemperatureMaxData)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = this.localizer[LangGenetic.TempMinData],
                    YAxisID = "performance",
                    BorderColor = "rgba(135,206,235,0.9)",
                    CurrentData = CorrectedData(simpleTemperatureMinData)
                });

            // chart.BodyWeightAxesBoundaries = new int[] { (int)(minValueBodyWeight * 0.9), (int)(maxValueBodyWeight * 1.1) };
            chart.BodyWeightAxesBoundaries = new int[] { 0, (int)(maxValueBodyWeight * 1.1) };

            return chart;
        }

        private void SetMinMaxValueBodyWeight(ref int minValueBodyWeight, ref int maxValueBodyWeight, GeneticGraphicDTO parameters)
        {
            int bodyWeightFemale = parameters.BodyWeightFemale.HasValue ? (int)parameters.BodyWeightFemale : 0;
            int bodyWeightMale = parameters.BodyWeightMale.HasValue ? (int)parameters.BodyWeightMale : 0;
            int bodyWeightSTDMinFemale = parameters.BodyWeightSTDMinFemale.HasValue ? (int)parameters.BodyWeightSTDMinFemale : 0;
            int bodyWeightSTDMinMale = parameters.BodyWeightSTDMinMale.HasValue ? (int)parameters.BodyWeightSTDMinMale : 0;
            int bodyWeightSTDMaxFemale = parameters.BodyWeightSTDMaxFemale.HasValue ? (int)parameters.BodyWeightSTDMaxFemale : 0;
            int bodyWeightSTDMaxMale = parameters.BodyWeightSTDMaxMale.HasValue ? (int)parameters.BodyWeightSTDMaxMale : 0;

            var minValue = Math.Min(Math.Min(Math.Min(Math.Min(bodyWeightFemale, minValueBodyWeight), bodyWeightMale), bodyWeightSTDMinFemale), bodyWeightSTDMinMale);

            maxValueBodyWeight = Math.Max(Math.Max(Math.Max(Math.Max(bodyWeightFemale, bodyWeightMale), bodyWeightSTDMaxFemale), bodyWeightSTDMaxMale), maxValueBodyWeight);

            if (minValue > 0)
                minValueBodyWeight = minValue;
        }

        private List<ChartDataPoint> CorrectedData(List<ChartDataPoint> chartDataPoints)
        {
            List<ChartDataPoint> correctChartDataPoints = new List<ChartDataPoint>();
            double firstXWithValue = 0;
            double lastXWithValue = 0;
            firstXWithValue = chartDataPoints.Any(d => d.Y.HasValue && d.Y > 0) ? chartDataPoints.FirstOrDefault(d => d.Y.HasValue && d.Y > 0).X : firstXWithValue;
            lastXWithValue = chartDataPoints.Any(d => d.Y.HasValue && d.Y > 0) ? chartDataPoints.LastOrDefault(d => d.Y.HasValue && d.Y > 0).X : lastXWithValue;

            correctChartDataPoints = chartDataPoints.Select(d => d.Y == 0 && d.X > firstXWithValue && d.X < lastXWithValue ?
                new ChartDataPoint(d.X, (chartDataPoints.FirstOrDefault(s => s.X == d.X - 1).Y + chartDataPoints.FirstOrDefault(s => s.X == d.X + 1).Y) / 2, 0)
                :
                d.Y == 0 ? new ChartDataPoint(d.X, (double?)null)
                :
                d).ToList();

            return correctChartDataPoints;
        }

        #region User Context Filter Methods

        private List<SelectListItem> GetUserRegionals(ApplicationUser user, List<Guid> userCompanyIds)
        {
            var regionals = new List<SelectListItem>();

            if (user?.Companies != null && user.Companies.Any())
            {
                var userRegionalIds = user.Companies
                    .Where(c => c.Company?.RegionalId != null)
                    .Select(c => c.Company.RegionalId.Value)
                    .Distinct()
                    .ToList();

                regionals = regionalService.GetAll()
                    .Where(r => userRegionalIds.Contains(r.Id))
                    .Select(r => new SelectListItem(r.Name, r.Id.ToString()))
                    .ToList();
            }
            else if (userCompanyIds != null && userCompanyIds.Any())
            {
                var userRegionalIds = companyService.GetAll()
                    .Where(c => userCompanyIds.Contains(c.Id) && c.RegionalId != null)
                    .Select(c => c.RegionalId.Value)
                    .Distinct()
                    .ToList();

                regionals = regionalService.GetAll()
                    .Where(r => userRegionalIds.Contains(r.Id))
                    .Select(r => new SelectListItem(r.Name, r.Id.ToString()))
                    .ToList();
            }
            else
            {
                // If no specific restrictions, return all regionals
                regionals = GetRegionalsByTenantId(this.operationContext.GetUserTenantId().Value);
            }

            return regionals;
        }

        private List<SelectListItem> GetUserUnits(ApplicationUser user, List<Guid> userCompanyIds)
        {
            var units = new List<SelectListItem>();

            if (user?.Companies != null && user.Companies.Any())
            {
                var userCompanyIdsList = user.Companies.Select(c => c.CompanyId).ToList();
                units = companyService.GetAll()
                    .Where(c => userCompanyIdsList.Contains(c.Id))
                    .Select(c => new SelectListItem(c.BusinessName, c.Id.ToString()))
                    .ToList();
            }
            else if (userCompanyIds != null && userCompanyIds.Any())
            {
                units = companyService.GetAll()
                    .Where(c => userCompanyIds.Contains(c.Id))
                    .Select(c => new SelectListItem(c.BusinessName, c.Id.ToString()))
                    .ToList();
            }
            else
            {
                // If no specific restrictions, return all units
                units = GetUnitsByRegionalList(null);
            }

            return units;
        }

        private List<SelectListItem> GetUserProductors(ApplicationUser user, List<Guid> userSiteIds)
        {
            var productors = new List<SelectListItem>();

            if (user?.Sites != null && user.Sites.Any())
            {
                var userSiteIdsList = user.Sites.Select(s => s.SiteId).ToList();
                productors = farmService.GetAll()
                    .Where(f => userSiteIdsList.Contains(f.Id))
                    .Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString()))
                    .ToList();
            }
            else if (userSiteIds != null && userSiteIds.Any())
            {
                productors = farmService.GetAll()
                    .Where(f => userSiteIds.Contains(f.Id))
                    .Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString()))
                    .ToList();
            }
            else
            {
                // If no specific restrictions, return all farms with laying batches
                productors = GetFarmsByUnitId(null);
            }

            return productors;
        }

        private List<SelectListItem> GetUserSupervisors(ApplicationUser user, List<Guid> userCompanyIds, List<Guid> userSiteIds)
        {
            var supervisors = new List<SelectListItem>();
            var companyIds = new List<Guid>();

            if (user?.Companies != null && user.Companies.Any())
            {
                companyIds = user.Companies.Select(c => c.CompanyId).ToList();
            }
            else if (userCompanyIds != null && userCompanyIds.Any())
            {
                companyIds = userCompanyIds;
            }
            else
            {
                companyIds = companyService.GetAll().Select(c => c.Id).ToList();
            }

            var farmIds = new List<Guid>();
            if (user?.Sites != null && user.Sites.Any())
            {
                farmIds = user.Sites.Select(s => s.SiteId).ToList();
            }
            else if (userSiteIds != null && userSiteIds.Any())
            {
                farmIds = userSiteIds;
            }
            else
            {
                farmIds = farmService.GetAll().Select(f => f.Id).ToList();
            }

            // Check if user has access to only one farm and that farm has a supervisor
            if (farmIds.Count == 1)
            {
                var farm = farmService.GetAll().FirstOrDefault(f => f.Id == farmIds.First());
                if (farm?.SupervisorId != null)
                {
                    var supervisor = userService.GetAll().FirstOrDefault(u => u.Id == farm.SupervisorId);
                    if (supervisor != null)
                    {
                        supervisors.Add(new SelectListItem(supervisor.Name + " " + supervisor.LastName, supervisor.Id.ToString()));
                        return supervisors;
                    }
                }
            }

            supervisors = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.SupervisorId, farmIds);
            return supervisors;
        }

        private List<SelectListItem> GetUserExtensionists(ApplicationUser user, List<Guid> userCompanyIds, List<Guid> userSiteIds)
        {
            var extensionists = new List<SelectListItem>();
            var companyIds = new List<Guid>();

            if (user?.Companies != null && user.Companies.Any())
            {
                companyIds = user.Companies.Select(c => c.CompanyId).ToList();
            }
            else if (userCompanyIds != null && userCompanyIds.Any())
            {
                companyIds = userCompanyIds;
            }
            else
            {
                companyIds = companyService.GetAll().Select(c => c.Id).ToList();
            }

            var farmIds = new List<Guid>();
            if (user?.Sites != null && user.Sites.Any())
            {
                farmIds = user.Sites.Select(s => s.SiteId).ToList();
            }
            else if (userSiteIds != null && userSiteIds.Any())
            {
                farmIds = userSiteIds;
            }
            else
            {
                farmIds = farmService.GetAll().Select(f => f.Id).ToList();
            }

            // Check if user has access to only one farm and that farm has a technician (extensionist)
            if (farmIds.Count == 1)
            {
                var farm = farmService.GetAll().FirstOrDefault(f => f.Id == farmIds.First());
                if (farm?.TechnicianId != null)
                {
                    var technician = userService.GetAll().FirstOrDefault(u => u.Id == farm.TechnicianId);
                    if (technician != null)
                    {
                        extensionists.Add(new SelectListItem(technician.Name + " " + technician.LastName, technician.Id.ToString()));
                        return extensionists;
                    }
                }
            }

            extensionists = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.TechnicianId, farmIds);
            return extensionists;
        }

        #endregion
    }
}