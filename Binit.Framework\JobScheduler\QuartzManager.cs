using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data.SqlClient;
using Binit.Framework.Constants.DAL;
using Binit.Framework.Helpers.Configuration;
using Microsoft.Extensions.Configuration;
using Quartz;
using Quartz.Impl;

namespace Binit.Framework.JobScheduler
{
    /// <summary>
    /// Quartz scheduler manager.
    /// </summary>
    public static class QuartzManager
    {
        public static IScheduler CreateScheduler(IConfiguration configuration)
        {
            var dbConfig = new DatabaseConfiguration().Bind(configuration);

            // Clean up problematic timezone triggers before starting
            CleanProblematicTimezones(dbConfig.LogConnection);

            NameValueCollection props = new NameValueCollection
            {
                { "quartz.serializer.type", "json" },
                { "quartz.jobStore.type", "Quartz.Impl.AdoJobStore.JobStoreTX, Quartz" },
                { "quartz.jobStore.dataSource", "default" },
                { "quartz.dataSource.default.connectionString", dbConfig.LogConnection },
                { "quartz.plugin.timeZoneConverter.type", "Quartz.Plugin.TimeZoneConverter.TimeZoneConverterPlugin, Quartz.Plugins.TimeZoneConverter" },
                { "quartz.jobStore.misfireThreshold", "60000" },
                { "quartz.jobStore.tablePrefix", "QRTZ_" },
                { "quartz.scheduler.instanceName", "QuartzScheduler" },
                { "quartz.scheduler.instanceId", "AUTO" }
            };

            // Setup the DataSource and JobStore configurations according to the Log Provider.
            switch (dbConfig.LogProvider)
            {
                case DatabaseProviders.SqlServer:
                    props.Add(new NameValueCollection{
                        { "quartz.dataSource.default.provider", "SqlServer" },
                        { "quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.SqlServerDelegate, Quartz" }
                    });
                    break;
                case DatabaseProviders.Sqlite:
                    props.Add(new NameValueCollection{
                        { "quartz.dataSource.default.provider", "SQLite-Microsoft" },
                        { "quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.SQLiteDelegate, Quartz" }
                    });
                    break;
                case DatabaseProviders.Oracle:
                    props.Add(new NameValueCollection{
                        { "quartz.dataSource.default.provider", "OracleODPManaged" },
                        { "quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.OracleDelegate, Quartz" }
                    });
                    break;
            }

            StdSchedulerFactory factory = new StdSchedulerFactory(props);
            var scheduler = factory.GetScheduler().Result;


            return scheduler;
        }

        public static void Configure(IScheduler scheduler, IServiceProvider provider)
        {
            scheduler.JobFactory = new JobFactory(provider);

            scheduler.ListenerManager.AddJobListener((IJobListener)provider.GetService(typeof(JobListenerBase)));
        }

        private static void CleanProblematicTimezones(string connectionString)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    // First, get all triggers that have problematic timezones
                    var selectCommand = @"
                        SELECT DISTINCT ct.TRIGGER_NAME, ct.TRIGGER_GROUP 
                        FROM QRTZ_CRON_TRIGGERS ct 
                        WHERE ct.TIME_ZONE_ID = 'America/Sao_Paulo'";
                    
                    var triggersToDelete = new List<(string name, string group)>();
                    
                    using (var selectCmd = new SqlCommand(selectCommand, connection))
                    using (var reader = selectCmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            triggersToDelete.Add((reader.GetString(0), reader.GetString(1)));
                        }
                    }
                    
                    if (triggersToDelete.Count > 0)
                    {
                        Console.WriteLine($"Found {triggersToDelete.Count} triggers with problematic timezone 'America/Sao_Paulo'");
                        
                        // Delete in the correct order to respect foreign key constraints
                        foreach (var trigger in triggersToDelete)
                        {
                            // 1. Delete from QRTZ_CRON_TRIGGERS first
                            var deleteCronCommand = @"
                                DELETE FROM QRTZ_CRON_TRIGGERS 
                                WHERE TRIGGER_NAME = @triggerName AND TRIGGER_GROUP = @triggerGroup";
                            
                            using (var command = new SqlCommand(deleteCronCommand, connection))
                            {
                                command.Parameters.AddWithValue("@triggerName", trigger.name);
                                command.Parameters.AddWithValue("@triggerGroup", trigger.group);
                                command.ExecuteNonQuery();
                            }
                            
                            // 2. Delete from QRTZ_TRIGGERS
                            var deleteTriggerCommand = @"
                                DELETE FROM QRTZ_TRIGGERS 
                                WHERE TRIGGER_NAME = @triggerName AND TRIGGER_GROUP = @triggerGroup";
                            
                            using (var command = new SqlCommand(deleteTriggerCommand, connection))
                            {
                                command.Parameters.AddWithValue("@triggerName", trigger.name);
                                command.Parameters.AddWithValue("@triggerGroup", trigger.group);
                                command.ExecuteNonQuery();
                            }
                            
                            Console.WriteLine($"Deleted trigger: {trigger.name} in group: {trigger.group}");
                        }
                        
                        Console.WriteLine($"Successfully cleaned up {triggersToDelete.Count} problematic timezone triggers");
                    }
                    else
                    {
                        Console.WriteLine("No problematic timezone triggers found");
                    }
                    
                    // Also clean up any orphaned triggers that might exist
                    var cleanOrphansCommand = @"
                        DELETE FROM QRTZ_TRIGGERS 
                        WHERE NOT EXISTS (
                            SELECT 1 FROM QRTZ_CRON_TRIGGERS ct 
                            WHERE ct.TRIGGER_NAME = QRTZ_TRIGGERS.TRIGGER_NAME 
                            AND ct.TRIGGER_GROUP = QRTZ_TRIGGERS.TRIGGER_GROUP
                        )
                        AND TRIGGER_TYPE = 'CRON'";
                    
                    using (var command = new SqlCommand(cleanOrphansCommand, connection))
                    {
                        var orphansDeleted = command.ExecuteNonQuery();
                        if (orphansDeleted > 0)
                        {
                            Console.WriteLine($"Cleaned up {orphansDeleted} orphaned cron triggers");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception but don't fail startup
                Console.WriteLine($"Warning: Could not clean problematic timezone triggers: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
