﻿using Binit.Framework;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Excel;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using DAL.Interfaces;
using Domain.Entities.DTOs;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Entities.Model.Views;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.BusinessLogic.DTOs.HenBatchDTOs;
using Domain.Logic.DTOs.HenBatchDTOs;
using Domain.Logic.Interfaces;
using Domain.Logic.Validations;
using Domain.Logic.Validations.BusinessLogicValidations.HenBatchBusinessLogicValidations;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.HenBatchBusinessLogic;

namespace Domain.Logic.BusinessLogic
{
    public class HenBatchBusinessLogic : IHenBatchBusinessLogic
    {
        #region Properties
        private readonly BusinessValidationManager<HenBatch> businessValidationManager;
        private readonly BusinessValidationManager<ShippingNote> businessValidationManagerMoveBirds;
        private readonly BusinessValidationManager<IEnumerable<ShippingNote>> businessValidationManagerMoveBirdsUpgrade;
        private readonly BusinessValidationManager<(HenBatch parent, List<HenBatch> children)> businessValidationManagerDistributions;
        private readonly IContainerBusinessLogic containerBusinessLogic;
        private readonly IContainerService<Container> containerService;
        private readonly IExceptionManager exceptionManager;
        private readonly IFarmService farmService;
        private readonly IFormulaService formulaService;
        private readonly IGeneticService geneticService;
        private readonly IHenBatchService henBatchService;
        private readonly IHenBatchPerformanceBusinessLogic henBatchPerformanceBusinessLogic;
        private readonly IHenWarehouseService henwarehouseService;
        private readonly ILineService lineService;
        private readonly IMaterialService materialService;
        private readonly IPersonService personService;
        private readonly IService<GeneticsParametersReference> geneticsParametersReferenceService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IExcelExportBusinessLogic excelExportBusinessLogic;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IOperationContext operationContext;
        private readonly IUnitOfWork unitOfWork;
        private readonly IServiceTenantDependent<SampleCage> sampleCageService;
        private readonly SelectListItemComparer selectListItemComparer;
        private readonly IHenReportService henReportService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IUpdateHenBatchPerformanceBusinessLogic updateHenBatchPerformanceBusinessLogic;
        #endregion

        #region Constructor
        public HenBatchBusinessLogic(
            BusinessValidationManager<HenBatch> businessValidationManager,
            BusinessValidationManager<ShippingNote> businessValidationManagerMoveBirds,
            BusinessValidationManager<IEnumerable<ShippingNote>> businessValidationManagerMoveBirdsUpgrade,
            BusinessValidationManager<(HenBatch parent, List<HenBatch> children)> businessValidationManagerDistributions,
            IContainerBusinessLogic containerBusinessLogic,
            IContainerService<Container> containerService,
            IExceptionManager exceptionManager,
            IFarmService farmService,
            IFormulaService formulaService,
            IGeneticService geneticService,
            IHenBatchService henBatchService,
            IHenWarehouseService henwarehouseService,
            IHenBatchPerformanceBusinessLogic henBatchPerformanceBusinessLogic,
            ILineService lineService,
            IMaterialService materialService,
            IPersonService personService,
            IService<GeneticsParametersReference> geneticsParametersReferenceService,
            IStringLocalizer<SharedResources> localizer,
            IExcelExportBusinessLogic excelExportBusinessLogic,
            IService<TenantConfiguration> tenantConfigurationService,
            IOperationContext operationContext,
            IUnitOfWork unitOfWork,
            IServiceTenantDependent<SampleCage> sampleCageService,
            IHenReportService henReportService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IUpdateHenBatchPerformanceBusinessLogic updateHenBatchPerformanceBusinessLogic)
        {
            this.businessValidationManager = businessValidationManager;
            this.businessValidationManagerMoveBirds = businessValidationManagerMoveBirds;
            this.businessValidationManagerMoveBirdsUpgrade = businessValidationManagerMoveBirdsUpgrade;
            this.businessValidationManagerDistributions = businessValidationManagerDistributions;
            this.containerBusinessLogic = containerBusinessLogic;
            this.containerService = containerService;
            this.exceptionManager = exceptionManager;
            this.farmService = farmService;
            this.formulaService = formulaService;
            this.geneticsParametersReferenceService = geneticsParametersReferenceService;
            this.geneticService = geneticService;
            this.henBatchService = henBatchService;
            this.henwarehouseService = henwarehouseService;
            this.henBatchPerformanceBusinessLogic = henBatchPerformanceBusinessLogic;
            this.lineService = lineService;
            this.localizer = localizer;
            this.materialService = materialService;
            this.personService = personService;
            this.excelExportBusinessLogic = excelExportBusinessLogic;
            this.tenantConfigurationService = tenantConfigurationService;
            this.operationContext = operationContext;
            this.unitOfWork = unitOfWork;
            this.sampleCageService = sampleCageService;
            this.selectListItemComparer = new SelectListItemComparer();
            this.henReportService = henReportService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.updateHenBatchPerformanceBusinessLogic = updateHenBatchPerformanceBusinessLogic;
        }
        #endregion

        /// <summary>
        /// Assign formula to batches from a warehouse if it is not assigned already
        /// </summary>
        public void AddFormulaToWarehouseBatches(Guid warehouseId, Guid formulaId)
        {
            formulaId = this.formulaService.GetAll().Where(f => f.Id == formulaId).Select(f => f.OutputId).FirstOrDefault();

            List<HenBatch> henBatches = this.henBatchService
                .GetAll()
                .Include(hb => hb.FormulasConsumed)
                .Where(hb => hb.Line.WarehouseId == warehouseId && hb.DateEnd == null && hb.Active)
                .ToList();

            foreach (HenBatch henBatch in henBatches)
            {
                if (!henBatch.FormulasConsumed.Any(fc => fc.FormulaId == formulaId))
                {
                    henBatch.FormulasConsumed.Add(new HenBatchFormula() { HenBatchId = henBatch.Id, FormulaId = formulaId });
                    HenBatch dbHenBatch = this.henBatchService.Get(henBatch.Id);
                    henBatch.CopyTo(dbHenBatch);
                    this.henBatchService.Update(dbHenBatch);
                }
            }

        }

        /// <summary>
        /// Set HenBatch Parent the Correct Area Enum .
        /// </summary>
        public async Task SetParentAreas(List<HenBatch> childrens)
        {
            List<AreaEnum> areas = childrens.SelectMany(l => l.AreaContainers).Select(ac => ac.AreaEnum).Distinct().ToList();

            HenBatch henBatchParent = childrens.FirstOrDefault().Parent;

            henBatchParent.AreaContainers = new List<AreaContainer>();
            foreach (AreaEnum area in areas)
            {
                henBatchParent.AreaContainers.Add(new AreaContainer()
                {
                    ContainerId = henBatchParent.Id,
                    AreaEnum = area
                });
            }

            await henBatchService.UpdateAsync(henBatchParent);

        }

        /// <summary>
        /// Creates a HenBatch, fetch it, set its name and detailed name and updates it.
        /// </summary>
        public async Task SetPropertiesAndCreate(HenBatch henBatch)
        {
            if (!henBatch.ParentId.HasValue)
            {
                bool codeUniqueness = !henBatchService.GetAll().Any(hb => hb.Code.ToUpper() == henBatch.Code.ToUpper() && hb.FarmId == henBatch.FarmId);

                if (!codeUniqueness)
                    throw new ValidationException($"ContainerProperties.{nameof(henBatch.Code)}", this.localizer[Lang.CodeIsUnique]);
            }

            // If it is not a parent henbatch
            if (henBatch.LineId.HasValue)
            {
                // validate 
                businessValidationManager
                       .BuildValidation<DateHenBatchValidation>()
                       .BuildValidation<LineCapacityBatchValidation>()
                       .Validate(henBatch);

                if (!businessValidationManager.Succeeded)
                    throw new ValidationException(null, new BusinessValidationResult<HenBatch>(businessValidationManager).UnsuccessfulValidations);

                //Set hen batch areas container from line area container
                List<AreaEnum> areas = GetLineAreas(henBatch.LineId).Result;
                henBatch.AreaContainers = areas.Select(a => new AreaContainer()
                {
                    AreaEnum = a,
                    ContainerId = henBatch.Id
                }).ToList();

                //Set hen batch origin containers from line origin containers
                IEnumerable<Guid> lineOrigins = GetLineOriginContainers(henBatch.LineId);
                henBatch.OriginContainers = lineOrigins.Select(o => new ContainerContainer()
                {
                    ContainerId = henBatch.Id,
                    OriginId = o
                }).ToList();

            }

            await henBatchService.CreateAsync(entity: henBatch);
            await CreateSampleCage(henBatch);
        }

        /// <summary>
        /// Creates a sample cage automatically when creating a hen batch.
        /// </summary>
        private async Task CreateSampleCage(HenBatch henBatch)
        {
            SampleCage sampleCageEntity = new SampleCage
            {
                HenBatchId = henBatch.Id,
                Name = henBatch.HenBatchType != HenBatchTypeEnum.Parent
                ? await lineService.GetAll().Where(l => l.Id == henBatch.LineId).Select(l => l.Name).FirstAsync()
                : farmService.Get(henBatch.FarmId.Value).Name + " - " + henBatch.Code,
                SectorId = henBatch.SectorId,
                FarmId = henBatch.FarmId,
                CompanyId = henBatch.CompanyId,
                Active = true
            };
            await sampleCageService.CreateAsync(sampleCageEntity);
        }

        /// <summary>
        /// Creates a henbatch if there is only one distribution or parent henbatch with children in case of multiple distribution
        /// </summary>
        public async Task<HenBatch> SetPropertiesAndCreate((HenBatch parent, List<HenBatch> children) henBatches)
        {
            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            bool hasCluster = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");

            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                if (!henBatches.children.Any())
                {
                    henBatches.parent.Name = localizer[Lang.EntityName];

                    await SetPropertiesAndCreate(henBatches.parent);
                }

                else
                {
                    businessValidationManagerDistributions
                        .BuildValidation<ParentCapacityValidation>()
                        .Validate(henBatches);

                    if (!businessValidationManagerDistributions.Succeeded)
                        throw new ValidationException(null, new BusinessValidationResult<(HenBatch parent, List<HenBatch> children)>(businessValidationManagerDistributions).UnsuccessfulValidations);

                    string genetic = await geneticService.GetAll().Where(f => f.Id == henBatches.parent.GeneticId).Select(f => f.Name).FirstOrDefaultAsync();
                    var farm = await farmService.GetAll().Where(f => f.Id == henBatches.parent.FarmId).Select(f => new { f.Name, f.DayOfWeek }).FirstOrDefaultAsync();

                    henBatches.parent.Name = localizer[Lang.EntityName];

                    await SetPropertiesAndCreate(henBatches.parent);

                    foreach (HenBatch child in henBatches.children)
                    {
                        child.DateStart = child.DateStart.Value.GetPastSelectedDay(farm.DayOfWeek);
                        child.Name = localizer[Lang.EntityName];

                        await SetPropertiesAndCreate(child);
                    }
                    await SetParentAreas(henBatches.children);
                }

            });

            return henBatches.parent;
        }

        private IEnumerable<Guid> GetLineOriginContainers(Guid? lineId)
        {
            return lineService
                .GetAll()
                .Where(l => l.Id == lineId)
                .SelectMany(l => l.OriginContainers)
                .Select(oc => oc.OriginId)
                .ToList();
        }

        private async Task<List<AreaEnum>> GetLineAreas(Guid? lineId)
        {
            return await lineService.GetAll()
                .Where(l => l.Id == lineId)
                .SelectMany(l => l.AreaContainers)
                .Select(ac => ac.AreaEnum)
                .ToListAsync();
        }

        /// <summary>
        /// Creates a HenBatch, fetch it, set its detailed name and updates it.
        /// </summary>
        public async Task SetPropertiesAndUpdate(HenBatch henBatch)
        {
            // If it is parent henbatch.
            if (!henBatch.ParentId.HasValue)
            {
                bool codeUniqueness = !henBatchService.GetAll().Any(hb => hb.Id != henBatch.Id && !hb.ParentId.HasValue && hb.Code.ToUpper() == henBatch.Code.ToUpper() && hb.FarmId == henBatch.FarmId);

                if (!codeUniqueness)
                    throw new ValidationException($"ContainerProperties.{nameof(henBatch.Code)}", this.localizer[Lang.CodeIsUnique]);
            }

            // If it is single or child henbatch.
            if (henBatch.LineId.HasValue)
            {
                // Validate.
                businessValidationManager
                   .BuildValidation<LineCapacityBatchValidation>()
                   //.BuildValidation<HenBatchCapacityValidation>()
                   .Validate(henBatch);

                if (!businessValidationManager.Succeeded)
                    throw new ValidationException(null, new BusinessValidationResult<HenBatch>(businessValidationManager).UnsuccessfulValidations);
            }

            HenBatch dbHenBatch = await henBatchService.GetFullAsync(henBatch.Id);
            dbHenBatch.AcceptedMaterialType = henBatch.AcceptedMaterialType;
            dbHenBatch.Description = henBatch.Description;
            dbHenBatch.FormulasConsumed = henBatch.FormulasConsumed;
            dbHenBatch.BatchWeekNumber = henBatch.BatchWeekNumber;
            dbHenBatch.GeneticId = henBatch.GeneticId;
            dbHenBatch.CategoryId = henBatch.CategoryId;
            dbHenBatch.Aliases = henBatch.Aliases;
            dbHenBatch.LineId = henBatch.LineId;
            dbHenBatch.Code = henBatch.Code;
            dbHenBatch.AllowBeginReportsOnAnyDate = henBatch.AllowBeginReportsOnAnyDate;
            dbHenBatch.DateStart = henBatch.DateStart;
            dbHenBatch.OpeningDate = henBatch.OpeningDate;
            dbHenBatch.StartDateOfWeekOne = henBatch.StartDateOfWeekOne;
            dbHenBatch.ReportingStartDate = henBatch.ReportingStartDate;

            await henBatchService.UpdateAsync(dbHenBatch);
            await UpdateSampleCage(dbHenBatch);
        }

        private async Task UpdateSampleCage(HenBatch henBatch)
        {
            SampleCage sampleCageEntity = sampleCageService.GetAll().FirstOrDefault(s => s.HenBatchId == henBatch.Id);
            if (sampleCageEntity != null)
            {
                sampleCageEntity.Name = henBatch.HenBatchType != HenBatchTypeEnum.Parent
                ? await lineService.GetAll().Where(l => l.Id == henBatch.LineId).Select(l => l.Name).FirstAsync()
                : farmService.Get(henBatch.FarmId.Value).Name + " - " + henBatch.Code;
                await sampleCageService.UpdateAsync(sampleCageEntity);
            }
            else
                await CreateSampleCage(henBatch);
        }

        /// <summary>
        /// Updates a henbatch if there is only one distribution or parent henbatch with children in case of multiple distribution
        /// </summary>
        public async Task SetPropertiesAndUpdate((HenBatch parent, List<HenBatch> children) henBatches)
        {
            string genetic = geneticService.GetAll().Where(f => f.Id == henBatches.parent.GeneticId).Select(f => f.Name).First();
            var farm = farmService.GetAll().Where(f => f.Id == henBatches.parent.FarmId).Select(f => new { f.Name, f.DayOfWeek }).FirstOrDefault();

            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                List<HenBatch> currentParent = henBatchService.GetAll(true)
                .Where(hb => hb.Id == henBatches.parent.Id)
                .ToList();

                List<HenBatch> currentChildren = henBatchService.GetAll(true)
                    .Where(hb => hb.ParentId == henBatches.parent.Id)
                    .ToList();

                if (henBatches.children.Any())
                {
                    businessValidationManagerDistributions
                        .BuildValidation<ParentCapacityValidation>()
                        .Validate(henBatches);

                    if (!businessValidationManagerDistributions.Succeeded)
                        throw new ValidationException(null, new BusinessValidationResult<(HenBatch parent, List<HenBatch> children)>(businessValidationManagerDistributions).UnsuccessfulValidations);
                }

                //Originally a single distribution
                if (!currentChildren.Any())
                {
                    //No distributions added
                    if (!henBatches.children.Any())
                        await SetPropertiesAndUpdate(henBatches.parent);

                    //Distributions added
                    else
                    {
                        {
                            await SetPropertiesAndUpdate(henBatches.parent);

                            foreach (HenBatch child in henBatches.children)
                            {
                                var query = henwarehouseService.GetAll()
                                .Where(hw => hw.Lines.Any(l => l.Id == child.LineId))
                                .Select(hw => new { Cluster = hw.Cluster.Name, HenWarehouse = hw.Name })
                                .First();

                                child.DateStart = child.DateStart.Value.GetPastSelectedDay(farm.DayOfWeek);
                                child.Name = localizer[Lang.EntityName];

                                await SetPropertiesAndCreate(child);
                            }
                        }
                    }
                }
                //Originally multiple distributions
                else
                {
                    //Multiple distributions are maintained
                    if (henBatches.children.Any())
                    {
                        EntityComparer<HenBatch> comparer = new EntityComparer<HenBatch>();

                        //Child henbatch not kept are deleted
                        foreach (HenBatch child in currentChildren.Except(henBatches.children, comparer))
                            await Delete(child.Id);

                        //New child henbatch are created
                        foreach (HenBatch child in henBatches.children.Except(currentChildren, comparer))
                        {
                            var query = henwarehouseService.GetAll()
                                .Where(hw => hw.Lines.Any(l => l.Id == child.LineId))
                                .Select(hw => new { Cluster = hw.Cluster.Name, HenWarehouse = hw.Name })
                                .First();

                            child.DateStart = child.DateStart.Value.GetPastSelectedDay(farm.DayOfWeek);
                            child.Name = localizer[Lang.EntityName];

                            await SetPropertiesAndCreate(child);
                        }

                        //Child henbatch kept are updated
                        foreach (HenBatch child in henBatches.children.Intersect(currentChildren, comparer))
                            await SetPropertiesAndUpdate(child);

                        await SetPropertiesAndUpdate(henBatches.parent);
                    }
                    //Single distribution is maintained
                    else
                    {
                        //Original distributions are deleted
                        foreach (HenBatch child in currentChildren)
                            await Delete(child.Id);

                        await SetPropertiesAndUpdate(henBatches.parent);
                    }
                }
            });
        }

        /// <summary>
        /// Get clusters which have henwarehouses with available lines for a given farm
        /// </summary>
        public IQueryable<Cluster> GetAvailableClusters(HenStage henStage, Guid? farmId = null)
        {
            IQueryable<HenWarehouse> henWarehouses = henwarehouseService.GetWithAvailableLines(henStage)
                .Include(w => w.Cluster);

            if (farmId.HasValue)
                henWarehouses = henWarehouses.Where(hw => hw.FarmId == farmId);

            return henWarehouses.Select(hw => hw.Cluster).Distinct();
        }

        /// <summary>
        /// Get farms which have warehouses with available lines for the given henstage.
        /// </summary>
        public IQueryable<Farm> GetAvailableFarms(HenStage? henStage)
        {
            IQueryable<HenWarehouse> henWarehouses = henwarehouseService.GetWithAvailableLines(henStage)
                .Where(hw => hw.FarmId.HasValue)
                .Include(w => w.Farm);

            return henWarehouses.Select(hw => hw.Farm).Distinct();
        }

        /// <summary>
        /// Get henwarehouses with available lines for a given henstage. 
        /// If indicated, only those belonging to the given cluster.
        /// </summary>
        public IQueryable<HenWarehouse> GetAvailableHenWarehouses(HenStage? henStage = null, Guid? clusterId = null)
        {
            IQueryable<HenWarehouse> henWarehouses = henwarehouseService.GetWithAvailableLines(henStage);

            if (clusterId.HasValue)
                henWarehouses = henWarehouses.Where(hw => hw.ClusterId == clusterId);

            return henWarehouses;
        }

        /// <summary>
        /// Get available lines for a given hen warehouse. 
        /// </summary>
        public IQueryable<Line> GetAvailableLines(Guid? henWarehouseId)
        {
            IQueryable<Line> lines = lineService.GetAll().Where(l =>
                !l.HenBatches.Any(hb => hb.DateEnd == null));

            if (henWarehouseId.HasValue)
                lines = lines.Where(hw => hw.WarehouseId == henWarehouseId);

            return lines;
        }

        /// <summary>
        ///  creates the select list for farms with hen batches
        /// </summary>
        public List<SelectListItem> GetFarms(HenStage? henStage = null, Guid? selected = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAll(asNoTracking: true);

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            return batches.Select(b => b.Farm)
                .OrderBy(f => f.Name)
                .Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString(), selected.HasValue && f.Id == selected)).Distinct()
                .ToList();
        }

        /// <summary>
        ///  creates the select list for clusters with hen batches
        /// </summary>
        public List<SelectListItem> GetClusters(HenStage? henStage = null, Guid? selectedFarm = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAllFull();

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            if (selectedFarm.HasValue)
                batches = batches.Where(b => b.Line.Warehouse.Cluster.FarmId.Value == selectedFarm.Value);

            return batches.Select(b => b.Line.Warehouse.Cluster)
                .Distinct()
                .OrderBy(c => c.Name)
                .Select(c => new SelectListItem(c.Name, c.Id.ToString()))
                .ToList();
        }

        /// <summary>
        ///  creates the select list for warehouses with hen batches
        /// </summary>
        public List<SelectListItem> GetWarehouses(HenStage? henStage = null, Guid? selectedCluster = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAllFull();

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            if (selectedCluster.HasValue)
                batches = batches.Where(b => b.Line.Warehouse.ClusterId == selectedCluster.Value);

            return batches.Select(b => b.Line.Warehouse)
                .Distinct()
                .OrderBy(w => w.Name)
                .Select(w => new SelectListItem(w.Name, w.Id.ToString()))
                .ToList();
        }

        /// <summary>
        ///  creates the select list for warehouses of hen batches
        /// </summary>
        public List<SelectListItem> GetWarehousesByHenBatch(Guid selectedHenBatch, HenStage? henStage = null)
        {
            if (selectedHenBatch == Guid.Empty)
            {
                return GetWarehouses(henStage ?? HenStage.Laying);
            }

            IQueryable<HenBatch> children = henBatchService.GetAll().Where(hb => hb.ParentId.Value == selectedHenBatch);

            if (children.Count() == 0)
            {
                HenBatch parent = henBatchService.GetFull(selectedHenBatch);
                return new List<SelectListItem>() { new SelectListItem(parent.Line.Warehouse.Name, parent.Line.Warehouse.Id.ToString()) };
            }

            bool isSelected = children.Count() == 1;
            return children.Where(b => b.Line.Warehouse != null).Select(b => b.Line.Warehouse)
                .OrderBy(w => w.Name).ToList()
                .Select(w => new SelectListItem(w.Name, w.Id.ToString(), isSelected))
                .Distinct(selectListItemComparer)
                .ToList();
        }

        /// <summary>
        ///  creates the select list for lines of selected warehouse and hen batch  
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouseAndHenBatch(Guid selectedWarehouse, Guid selectedHenBatch, HenStage? henStage = null)
        {
            // Handle "Todos" case - when selectedHenBatch is empty, return all lines for the warehouse with the specified hen stage
            if (selectedHenBatch == Guid.Empty)
            {
                return GetLines(henStage ?? HenStage.Laying, selectedWarehouse != Guid.Empty ? (Guid?)selectedWarehouse : null);
            }

            IQueryable<HenBatch> children = henBatchService.GetAll()
                .Where(hb => hb.ParentId.Value == selectedHenBatch);
            if (selectedWarehouse != null && selectedWarehouse != Guid.Empty)
                children = children.Where(hb => hb.Line.WarehouseId == selectedWarehouse);

            if (children.Count() == 0)
            {
                HenBatch parent = henBatchService.GetFull(selectedHenBatch);
                return new List<SelectListItem>() { new SelectListItem(parent.Line.Name, parent.Line.Id.ToString()) };
            }

            bool isSelected = children.Count() == 1;
            return children.Select(hb => hb.Line)
                .Distinct()
                .OrderBy(l => l.Name)
                .Select(l => new SelectListItem(l.Name, l.Id.ToString(), isSelected))
                .ToList();
        }

        /// <summary>
        ///  creates the select list for lines with hen batches
        /// </summary>
        public List<SelectListItem> GetLines(HenStage? henStage = null, Guid? selectedWarehouse = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAll().Include(b => b.Line);

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            if (selectedWarehouse.HasValue)
                batches = batches.Where(b => b.Line.WarehouseId == selectedWarehouse.Value);

            return batches.Select(b => b.Line)
                .Distinct()
                .OrderBy(l => l.Name)
                .Select(l => new SelectListItem(l.Name, l.Id.ToString()))
                .ToList();
        }

        /// <summary>
        ///  creates the select list for lines with hen batches, filter.
        /// </summary>
        public List<SelectListItem> GetLinesFiltered(HenStage? henStage = null, Guid? selectedWarehouse = null, Guid? selectedHenBatch = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAll().Include(b => b.Line);

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            if (selectedWarehouse.HasValue)
                batches = batches.Where(b => b.Line.WarehouseId == selectedWarehouse.Value);

            if (selectedHenBatch.HasValue)
                batches = batches.Where(b => b.ParentId == selectedHenBatch.Value);

            return batches.Select(b => b.Line)
                .Distinct()
                .OrderBy(l => l.Name)
                .Select(l => new SelectListItem(l.Name, l.Id.ToString()))
                .ToList();
        }

        /// <summary>
        ///  creates the select list for lines with hen batches, filter.
        ///  Example of DetailedName returned: "Lote | 0224FO | Aviário 1 | Box 1"
        /// </summary>
        public List<SelectListItem> GetLinesHenBatchFiltered(HenStage? henStage = null, Guid? selectedWarehouse = null, Guid? selectedHenBatch = null)
        {
            IQueryable<HenBatch> batches = containerService
                .GetAll()
                .OfType<HenBatch>();

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);
 
            if (selectedWarehouse.HasValue) {
                var lineIdsInWarehouse = containerService.GetAll()
                    .Where(c => ((Line)c).WarehouseId == selectedWarehouse.Value)
                    .Select(c => c.Id)
                    .ToList();

                if (lineIdsInWarehouse.Any())
                {
                    batches = batches.Where(b => b.LineId.HasValue && lineIdsInWarehouse.Contains(b.LineId.Value));
                }
            }

            if (selectedHenBatch.HasValue)
                batches = batches.Where(b => b.ParentId == selectedHenBatch.Value);

            return batches.Select(b => new SelectListItem
            {
                Text = b.DetailedName,
                Value = b.Id.ToString()
            }).ToList();
        }

        /// <summary>
        ///  creates the select list for cages with hen batches
        /// </summary>
        public List<SelectListItem> GetCages(HenStage? henStage = null, Guid? selectedLine = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAll().Include(hb => hb.SampleCages);

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            if (selectedLine.HasValue)
                batches = batches.Where(b => b.LineId == selectedLine.Value && b.Active);

            List<SampleCage> sampleCages = new List<SampleCage>();

            foreach (HenBatch hb in batches)
            {
                if (hb.SampleCages != null)
                {
                    sampleCages.AddRange(hb.SampleCages);
                }
            }

            return sampleCages.Distinct()
                .OrderBy(l => l.Name)
                .Select(l => new SelectListItem(l.Name, l.Id.ToString()))
                .ToList();

        }


        /// <summary>
        ///  returns options for genetics 
        /// </summary>
        public List<SelectListItem> GetGenetics(HenStage? henStage = null, Guid? selectedGenetic = null, Guid? selectedFarm = null)
        {
            // Get farm specific genetics if have one.
            List<FarmGenetic> farmGenetics = new List<FarmGenetic>();

            if (selectedFarm != null)
                farmGenetics = farmService.GetAllIgnoringClaims()
                    .Include(f => f.Genetics).ThenInclude(g => g.Genetic)
                    .FirstOrDefault(f => f.Id == selectedFarm.Value).Genetics;

            List<Genetic> genetics = new List<Genetic>();
            if (farmGenetics.Any())
            {
                genetics = farmGenetics.Select(fg => fg.Genetic).Where(g => g.Active)
                            .Distinct() // avoid repetition (it may occur)
                            .ToList();
            }
            else
            {
                //Fetch active genetics  
                genetics = this.geneticService.GetAll().Where(g => g.Active).ToList();

                //Since no genetic data about Breeding has been uploaded, this restriction is bypassed. Once
                //the according data is uploaded, this condition should be removed.
                if (henStage == HenStage.Laying)
                {
                    //Fetch genetics parameters references for specified henstage -> there are many registers per genetic id
                    List<GeneticsParametersReference> geneticsParametersReferences = new List<GeneticsParametersReference>();
                    if (henStage.HasValue)
                        geneticsParametersReferences = this.geneticsParametersReferenceService.GetAll()
                            .Where(g => g.HenStage == henStage).ToList();
                    else
                        geneticsParametersReferences = this.geneticsParametersReferenceService.GetAll().ToList();
                    //I only keep those which have geneticParametersReferences
                    genetics = genetics.Where(g => geneticsParametersReferences.Any(gp => gp.GeneticsId == g.Id)).ToList();
                }
            }

            if (selectedGenetic == null)
                if (genetics.Count() == 1)
                {
                    return genetics.Select(c => new SelectListItem(c.Name, c.Id.ToString(), true)).OrderBy(g => g.Text).ToList();
                }
                else
                {
                    return genetics.Select(c => new SelectListItem(c.Name, c.Id.ToString())).OrderBy(g => g.Text).ToList();
                }
            else
                return genetics.Select(c => new SelectListItem(c.Name, c.Id.ToString(), c.Id == selectedGenetic)).OrderBy(g => g.Text).ToList();
        }

        /// <summary>
        ///  removes the days of the week in wich the farm cannot enter a hen batch 
        /// </summary>
        public (List<int>, int) GetDisabledWeekDays(Guid id, string discriminator = null, bool isForOpeningDate = false)
        {
            DayOfWeek dayOfWeek;
            switch (discriminator)
            {
                case ContainerTypes.Farm:
                    dayOfWeek = farmService.Get(id).DayOfWeek;
                    break;
                default:
                    dayOfWeek = containerService.GetDayOfWeek(id);
                    break;
            }

            List<int> week = new List<int> { 0, 1, 2, 3, 4, 5, 6 };
            int farmDay = (int)dayOfWeek;

            if (isForOpeningDate)
                farmDay = farmDay - 1 < 0 ? 6 : farmDay - 1;

            week.Remove(farmDay);
            return (week, farmDay);
        }

        /// <summary>
        /// returns HenBatches
        /// </summary>
        public List<SelectListItem> GetHenBatches(HenStage? henStage = null, Guid? lineId = null, bool activeBatches = false, bool orderByNew = false)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAllFull(henStage);

            if (activeBatches)
                henBatches = henBatches.Where(hb => hb.DateEnd == null);
            if (lineId.HasValue)
                henBatches = henBatches.Where(hb => hb.LineId == lineId);
            if (orderByNew)
            {
                IOrderedQueryable<HenBatch> henBatcheOrderByNew = henBatches.OrderBy(hb => hb.CreatedDate);
                return henBatcheOrderByNew.Select(c => new SelectListItem(c.DetailedName, c.Id.ToString())).ToList();
            }
            else
            {
                return henBatches.Select(c => new SelectListItem(c.DetailedName, c.Id.ToString()))
                    .ToList().OrderBy(h => h.Text).ToList();
            }
        }

        public List<SelectListItem> GetParentHenBatches(HenStage? henStage = null, bool? activeBatches = null, bool orderByNew = false, bool isMainFilter = false)
        {
            IQueryable<HenBatch> henBatches = henStage.HasValue ?
                this.henBatchService.GetAll().Where(hb => hb.HenStage == henStage.Value && !hb.ParentId.HasValue) :
                this.henBatchService.GetAll().Where(hb => !hb.ParentId.HasValue);

            henBatches = activeBatches switch
            {
                true => henBatches.Where(hb => !hb.DateEnd.HasValue),
                false => henBatches.Where(hb => hb.DateEnd.HasValue),
                _ => henBatches
            };

            if (orderByNew && isMainFilter)
            {
                IOrderedQueryable<HenBatch> henBatcheOrderByNew = henBatches.OrderBy(hb => hb.CreatedDate);
                return henBatcheOrderByNew.Select(c => new SelectListItem(c.DetailedName, c.Id.ToString())).ToList();
            }

            return henBatches.Select(hb => new SelectListItem(hb.Code, hb.Id.ToString()))
                .ToList().OrderBy(h => h.Text).ToList();
        }

        /// <summary>
        /// Returns parentHenBatches, warehouses and lines 
        /// </summary>
        public ContainerFilterDTO GetAllContainerFilter(HenStage henStage, Guid? farmId, bool? activeBatches = null, bool orderByNew = false)
        {
            IQueryable<HenBatch> henBatches = henBatchService.GetAll().Include(hb => hb.Line).ThenInclude(l => l.Warehouse).Where(hb => hb.HenStage == henStage);
            // filter to parentHenBatches
            IQueryable<HenBatch> parentHenBatches = henBatches.Where(hb => !hb.ParentId.HasValue);

            parentHenBatches = activeBatches switch
            {
                true => parentHenBatches.Where(hb => !hb.DateEnd.HasValue),
                false => parentHenBatches.Where(hb => hb.DateEnd.HasValue),
                _ => parentHenBatches
            };

            if (farmId.HasValue)
                parentHenBatches = parentHenBatches.Where(hb => hb.FarmId == farmId);

            if (orderByNew)
                parentHenBatches = parentHenBatches.OrderByDescending(hb => hb.CreatedDate);

            List<SelectListItem> parentHenBatchesItems = parentHenBatches.Select(c => new SelectListItem(c.DetailedName, c.Id.ToString())).ToList();

            // filter to warehouses
            Guid? newestParentHenBatch = parentHenBatchesItems.Any() ? new Guid(parentHenBatchesItems.FirstOrDefault().Value) : (Guid?)null;
            IQueryable<HenBatch> children = henBatches.Where(hb => hb.ParentId.Value == newestParentHenBatch);

            List<SelectListItem> defaultListItem = new List<SelectListItem>();
            List<SelectListItem> warehouse = defaultListItem;

            bool onlyChild = children.Count() == 1;
            if (!children.Any() && parentHenBatches.Any())
            {
                HenBatch parent = parentHenBatches.FirstOrDefault(hb => hb.Id == newestParentHenBatch);
                warehouse = new List<SelectListItem>() { new SelectListItem(parent.Line.Warehouse.Name, parent.Line.Warehouse.Id.ToString()) };
            }
            else if (children.Any())
            {
                warehouse = children.Where(b => b.Line.Warehouse != null).Select(b => b.Line.Warehouse)
                    .OrderBy(w => w.Name).ToList()
                    .Select(w => new SelectListItem(w.Name, w.Id.ToString(), onlyChild))
                    .Distinct(selectListItemComparer)
                    .ToList();
            }

            // filter to lines
            List<SelectListItem> lines = defaultListItem;
            if (onlyChild)
                lines = children.Select(b => b.Line).Distinct().OrderBy(l => l.Name).Select(l => new SelectListItem(l.Name, l.Id.ToString())).ToList();

            // filter to cages
            List<SelectListItem> cages = defaultListItem;
            List<SampleCage> sampleCages = new List<SampleCage>();
            foreach (HenBatch child in children)
            {
                if (child.SampleCages != null)
                {
                    sampleCages.AddRange(child.SampleCages);
                }
            }
            cages = sampleCages.Distinct().OrderBy(l => l.Name).Select(l => new SelectListItem(l.Name, l.Id.ToString())).ToList();
            return new ContainerFilterDTO()
            {
                ParentHenBatch = parentHenBatchesItems,
                Warehouse = warehouse,
                Line = lines,
                Cages = cages,
            };
        }

        /// <summary>
        /// returns Genetics by HenBatch
        /// </summary>
        public Genetic GetGeneticByHenBatch(Guid HBId)
        {
            Genetic genetics = this.henBatchService.GetAllWithGenetic()
                                                    .Where(hb => hb.Id == HBId)
                                                    .FirstOrDefault()
                                                    .Genetic;
            return genetics;
        }

        /// <summary>
        /// returns HenBatches by Status
        /// </summary>
        public List<SelectListItem> GetHenBatchesByStatus(string status, HenStage? henStage = null)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAllFull(henStage);

            if (status == HenBatchStatus.Open)
                henBatches = henBatches.Where(hb => hb.DateEnd == null);
            if (status == HenBatchStatus.Closed)
                henBatches = henBatches.Where(hb => hb.DateEnd != null);

            return henBatches.Select(c => new SelectListItem(c.ToString(), c.Id.ToString()))
                .ToList().OrderBy(h => h.Text).ToList();
        }

        /// <summary>
        /// Get list of hen batch Ids contained in the given container. Only parents or only children (self contained batches belong to all categories)
        /// </summary>
        public List<Guid> GetHenBatchIdsByContainer(Guid containerId, string containerType = null, HenStage? henStage = null, bool activeBatches = true, bool parents = true)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll();

            if (activeBatches)
                henBatches = henBatches.Where(hb => hb.Active == activeBatches);

            if (string.IsNullOrEmpty(containerType))
                containerType = this.containerService.GetAll().FirstOrDefault(c => c.Id == containerId)?.ContainerType;

            if (henStage != null)
                henBatches = henBatches.Where(hb => hb.HenStage == henStage.Value);

            if (containerType == ContainerTypes.Farm)
                henBatches = henBatches.Where(hb => hb.FarmId == containerId);
            else if (containerType == ContainerTypes.Line)
                henBatches = henBatches.Where(hb => hb.LineId == containerId);
            else if (containerType == ContainerTypes.HenWarehouse)
                henBatches = henBatches.Where(hb => hb.Line.WarehouseId == containerId);
            else if (containerType == ContainerTypes.HenBatch)
            {
                HenBatch selectedHenBatch = henBatches.Where(hb => hb.Id == containerId).FirstOrDefault();
                // verified that the selected parent henbatch has distributions
                if (!selectedHenBatch.LineId.HasValue)
                    henBatches = henBatches.Where(hb => hb.ParentId == containerId);
                else
                    henBatches = henBatches.Where(hb => hb.Id == containerId);
            }
            else
            {
                // May be it is a cluster
                List<Guid> henWarehouses = this.henwarehouseService.GetAll().Where(hwh => hwh.ClusterId == containerId).Select(hwh => hwh.Id).ToList();
                henBatches = henBatches.Where(hb => henWarehouses.Contains(hb.Line.WarehouseId));
            }

            if (parents)
            {
                List<Guid> henBatchSelfContainedIds = henBatches.Where(hb => hb.ParentId == null && hb.LineId != null).Select(hb => hb.Id).ToList();
                List<Guid> henBatchParentIds = henBatches.Where(hb => hb.ParentId.HasValue).Select(hb => hb.ParentId.Value).ToList();
                return henBatchService.GetAll()
                        .Where(hb => henBatchSelfContainedIds.Contains(hb.Id) || henBatchParentIds.Contains(hb.Id))
                        .Select(hb => hb.Id).ToList();
            }
            else
                return henBatches.Select(hb => hb.Id).ToList();
        }

        /// <summary>
        /// return available options for the line acording to user selection 
        /// </summary>
        public List<SelectListItem> GetAvailableLines(HenBatchFilterDTO d)
        {
            List<SelectListItem> availableLines = new List<SelectListItem>();

            if (d.SelectedLine == null || d.SelectedLine == Guid.Empty)
            {
                if (d.SelectedWarehouse == null || d.SelectedWarehouse == Guid.Empty)
                    availableLines = this.lineService.GetAll().Where(l => l.HenBatches.Count(H => H.DateEnd == null) == 0 || l.HenBatches.Any(h => h.Id == d.CurrentBatchId)).Select(l => new SelectListItem(l.Name, l.Id.ToString())).ToList();
                else
                    availableLines = this.lineService.GetAll().Where(l => l.WarehouseId == d.SelectedWarehouse && (l.HenBatches.Count(H => H.DateEnd == null) == 0 || l.HenBatches.Any(h => h.Id == d.CurrentBatchId))).Select(l => new SelectListItem(l.Name, l.Id.ToString())).ToList();
            }
            else
            {
                availableLines = this.lineService.GetAll().Where(l => l.WarehouseId == d.SelectedWarehouse).Select(l => new SelectListItem(l.Name, l.Id.ToString(), l.Id == d.SelectedLine)).ToList();
            }


            availableLines = availableLines.OrderBy(l => l.Text).ToList();

            return availableLines;

        }

        /// <summary>
        /// Get hen batches that belong to the given genetic ids
        /// </summary>
        public List<SelectListItem> GetAllByGenetic(List<Guid> geneticIds, HenStage? henStage, bool? active)
        {
            List<Guid> genetics = this.geneticService.GetAll().Where(g => geneticIds.Contains(g.Id)).Select(g => g.Id).ToList();
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll();

            if (henStage.HasValue)
                henBatches = henBatches.Where(hb => hb.HenStage == henStage);

            if (active.HasValue)
            {
                if (active.Value)
                    henBatches = henBatches.Where(hb => hb.DateEnd == null);
                else
                    henBatches = henBatches.Where(hb => hb.DateEnd != null);
            }

            if (genetics != null && genetics.Any())
                henBatches = henBatches.Where(hb => genetics.Contains(hb.GeneticId));

            return henBatches.Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString())).ToList();
        }

        /// <summary>
        /// Get all distinct genetics from hen batches
        /// </summary>
        public List<SelectListItem> GetAllGenetics(bool? activeBatches = null)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll();

            if (activeBatches.HasValue)
            {
                if (activeBatches.Value)
                    henBatches = henBatches.Where(hb => hb.DateEnd == null);
                else
                    henBatches = henBatches.Where(hb => hb.DateEnd != null);
            }

            return henBatches.Select(hb => new SelectListItem(hb.Genetic.Name, hb.GeneticId.ToString())).Distinct().ToList();
        }

        /// <summary>
        /// Get all distinct formulas from hen batches
        /// </summary>
        public List<SelectListItem> GetAllFormulasByHenStage(HenStage henStage, bool? activeBatches = null)
        {
            IQueryable<HenBatch> henBatches = this.henBatchService.GetAll().Where(hb => hb.HenStage == henStage).Include(hb => hb.FormulasConsumed).ThenInclude(f => f.Formula);

            if (activeBatches.HasValue)
            {
                if (activeBatches.Value)
                    henBatches = henBatches.Where(hb => hb.DateEnd == null);
                else
                    henBatches = henBatches.Where(hb => hb.DateEnd != null);
            }
            return henBatches.SelectMany(hb => hb.FormulasConsumed).ToList().Select(hbf => new SelectListItem(hbf.Formula.Name, hbf.FormulaId.ToString())).Distinct(selectListItemComparer).ToList();
        }

        /// <summary>
        /// Returns all hen batches filtered by genetic, status, start and end date including all its relationships.
        /// </summary>
        public IQueryable<HenBatch> GetAllFiltered(Dictionary<string, string> filters, HenStage? henStage = null, bool? active = null, HenBatchTypeEnum? henBatchType = null)
        {
            IQueryable<HenBatch> henBatches = henBatchService.GetAll(asNoTracking: true, useDefaultSorting: false, henBatchType: henBatchType);

            if (henStage.HasValue)
                henBatches = henBatches.Where(hb => hb.HenStage == henStage.Value);

            if (active.HasValue)
                henBatches = active.Value switch
                {
                    true => henBatches.Where(hb => !hb.DateEnd.HasValue),
                    _ => henBatches.Where(hb => hb.DateEnd.HasValue)
                };

            if (filters.Keys.Contains("henStage") && !string.IsNullOrEmpty(filters["henStage"]))
            {
                HenStage henStageSelected = (HenStage)Enum.Parse(typeof(HenStage), filters["henStage"], true);

                henBatches = henBatches.Where(hb => hb.HenStage == henStageSelected);
            }

            if (filters.Keys.Contains("genetic") && !string.IsNullOrEmpty(filters["genetic"]))
            {
                Guid genId = new Guid(filters["genetic"]);
                henBatches = henBatches.Where(hb => hb.Genetic.Id == genId);
            }

            if (filters.Keys.Contains("status") && !string.IsNullOrEmpty(filters["status"]))
            {
                henBatches = filters["status"] switch
                {
                    HenBatchStatus.Open => henBatches.Where(hb => !hb.DateEnd.HasValue),
                    HenBatchStatus.Closed => henBatches.Where(hb => hb.DateEnd.HasValue),
                    _ => henBatches
                };
            }
            if (filters.Keys.Contains("start-date-start") && !string.IsNullOrEmpty(filters["start-date-start"]))
            {
                DateTime startDateStart = DateTime.Parse(filters["start-date-start"]).Date;
                henBatches = henBatches.Where(hb => hb.DateStart.Value.Date >= startDateStart);
            }

            if (filters.Keys.Contains("start-date-end") && !string.IsNullOrEmpty(filters["start-date-end"]))
            {
                DateTime startDateEnd = DateTime.Parse(filters["start-date-end"]).Date;
                henBatches = henBatches.Where(hb => hb.DateStart.Value.Date <= startDateEnd);
            }

            if (filters.Keys.Contains("end-date-start") && !string.IsNullOrEmpty(filters["end-date-start"]))
            {
                DateTime endDateStart = DateTime.Parse(filters["end-date-start"]).Date;
                henBatches = henBatches.Where(hb => hb.DateEnd.Value.Date >= endDateStart);
            }

            if (filters.Keys.Contains("end-date-end") && !string.IsNullOrEmpty(filters["end-date-end"]))
            {
                DateTime endDateEnd = DateTime.Parse(filters["end-date-end"]).Date;
                henBatches = henBatches.Where(hb => hb.DateEnd.Value.Date <= endDateEnd);
            }

            if (filters.Keys.Contains("farm") && !string.IsNullOrEmpty(filters["farm"]))
            {
                Guid farmId = new Guid(filters["farm"]);
                henBatches = henBatches.Where(hb => hb.FarmId == farmId);
            }

            if (filters.Keys.Contains("cluster") && !string.IsNullOrEmpty(filters["cluster"]))
            {
                Guid clusterId = new Guid(filters["cluster"]);
                List<Guid?> parentIds = henBatches.Where(hb => hb.Line.Warehouse.ClusterId == clusterId && hb.ParentId.HasValue).Select(hb => hb.ParentId).ToList();
                henBatches = henBatches.Where(hb => hb.Line.Warehouse.ClusterId == clusterId || parentIds.Contains(hb.Id));
            }

            if (filters.Keys.Contains("warehouse") && !string.IsNullOrEmpty(filters["warehouse"]))
            {
                Guid warehouseId = new Guid(filters["warehouse"]);
                List<Guid?> parentIds = henBatches.Where(hb => hb.Line.WarehouseId == warehouseId && hb.ParentId.HasValue).Select(hb => hb.ParentId).ToList();
                henBatches = henBatches.Where(hb => hb.Line.WarehouseId == warehouseId || parentIds.Contains(hb.Id));
            }

            if (filters.Keys.Contains("line") && !string.IsNullOrEmpty(filters["line"]))
            {
                Guid lineId = new Guid(filters["line"]);
                List<Guid?> parentIds = henBatches.Where(hb => hb.LineId == lineId && hb.ParentId.HasValue).Select(hb => hb.ParentId).ToList();
                henBatches = henBatches.Where(hb => hb.LineId == lineId || parentIds.Contains(hb.Id));
            }

            if (filters.Keys.Contains("formula") && !string.IsNullOrEmpty(filters["formula"]))
            {
                Guid formulaId = new Guid(filters["formula"]);
                henBatches = henBatches.Where(hb => hb.FormulasConsumed.Any(f => f.FormulaId == formulaId));
            }

            henBatches = henBatches.Select(hb => new HenBatch()
            {
                Id = hb.Id,
                ParentId = hb.ParentId,
                LineId = hb.Line.Id,
                Name = hb.Name,
                DetailedName = hb.DetailedName,
                HenStage = hb.HenStage,
                Genetic = new Genetic() { Id = hb.Genetic.Id, Name = hb.Genetic.Name },
                Category = new HenBatchCategory() { Id = hb.Category.Id, Name = hb.Category.Name },
                DateStart = hb.DateStart,
                StartDateOfWeekOne = hb.StartDateOfWeekOne,
                DateEnd = hb.DateEnd,
                FirstProductionDate = hb.FirstProductionDate,
                CapitalizationDate = hb.CapitalizationDate,
                FemalesOnFirstProductionDate = hb.FemalesOnFirstProductionDate,
                HenAmountFemale = hb.HenAmountFemale,
                HenAmountMale = hb.HenAmountMale,
                InitialHenAmountFemale = hb.InitialHenAmountFemale,
                InitialHenAmountMale = hb.InitialHenAmountMale,
                Code = hb.Code,
                Farm = new Farm() { Id = hb.Farm.Id, Name = hb.Farm.Name, DayOfWeek = hb.Farm.DayOfWeek, Code = hb.Farm.Code },
                BatchWeekNumber = hb.BatchWeekNumber,
                ContainerType = hb.ContainerType,
                Line = new Line()
                {
                    Id = hb.Line.Id,
                    Name = hb.Line.Name,
                    Warehouse = new HenWarehouse()
                    {
                        Id = hb.Line.Warehouse.Id,
                        Name = hb.Line.Warehouse.Name,
                        Cluster = new Cluster()
                        {
                            Id = hb.Line.Warehouse.Cluster.Id,
                            Name = hb.Line.Warehouse.Cluster.Name
                        },
                        Code = hb.Line.Warehouse.Code
                    },
                    Code = hb.Line.Code
                },
                FormulasConsumed = hb.FormulasConsumed.Select(fc => new HenBatchFormula()
                {
                    Formula = new Material()
                    {
                        Id = fc.Formula.Id,
                        Name = fc.Formula.Name
                    }
                })
                .ToList()
            });
            return henBatches;
        }

        /// <summary>
        /// Returns all hen batches filtered From HenBatchGridView
        /// </summary>
        public IQueryable<HenBatchGridView> GetAllForIndex(Dictionary<string, string> filters)
        {
            IQueryable<HenBatchGridView> henBatches = henBatchService.GetAllView();

            if (filters.Keys.Contains("henStage") && !string.IsNullOrEmpty(filters["henStage"]))
            {
                HenStage henStageSelected = (HenStage)Enum.Parse(typeof(HenStage), filters["henStage"], true);

                henBatches = henBatches.Where(hb => hb.HenStage == henStageSelected);
            }

            if (filters.Keys.Contains("genetic") && !string.IsNullOrEmpty(filters["genetic"]))
            {
                Guid genId = new Guid(filters["genetic"]);
                henBatches = henBatches.Where(hb => hb.GeneticId == genId);
            }

            if (filters.Keys.Contains("status") && !string.IsNullOrEmpty(filters["status"]))
            {
                henBatches = filters["status"] switch
                {
                    HenBatchStatus.Open => henBatches.Where(hb => !hb.DateEnd.HasValue),
                    HenBatchStatus.Closed => henBatches.Where(hb => hb.DateEnd.HasValue),
                    _ => henBatches
                };
            }
            if (filters.Keys.Contains("start-date-start") && !string.IsNullOrEmpty(filters["start-date-start"]))
            {
                DateTime startDateStart = DateTime.Parse(filters["start-date-start"]).Date;
                henBatches = henBatches.Where(hb => hb.DateStart.Value.Date >= startDateStart);
            }

            if (filters.Keys.Contains("start-date-end") && !string.IsNullOrEmpty(filters["start-date-end"]))
            {
                DateTime startDateEnd = DateTime.Parse(filters["start-date-end"]).Date;
                henBatches = henBatches.Where(hb => hb.DateStart.Value.Date <= startDateEnd);
            }

            if (filters.Keys.Contains("end-date-start") && !string.IsNullOrEmpty(filters["end-date-start"]))
            {
                DateTime endDateStart = DateTime.Parse(filters["end-date-start"]).Date;
                henBatches = henBatches.Where(hb => hb.DateEnd.Value.Date >= endDateStart);
            }

            if (filters.Keys.Contains("end-date-end") && !string.IsNullOrEmpty(filters["end-date-end"]))
            {
                DateTime endDateEnd = DateTime.Parse(filters["end-date-end"]).Date;
                henBatches = henBatches.Where(hb => hb.DateEnd.Value.Date <= endDateEnd);
            }

            if (filters.Keys.Contains("farm") && !string.IsNullOrEmpty(filters["farm"]))
            {
                Guid farmId = new Guid(filters["farm"]);
                henBatches = henBatches.Where(hb => hb.FarmId == farmId);
            }

            if (filters.Keys.Contains("cluster") && !string.IsNullOrEmpty(filters["cluster"]))
            {
                Guid clusterId = new Guid(filters["cluster"]);
                List<Guid?> parentIds = henBatches.Where(hb => hb.ClusterId == clusterId && hb.ParentId.HasValue).Select(hb => hb.ParentId).ToList();
                henBatches = henBatches.Where(hb => hb.ClusterId == clusterId || parentIds.Contains(hb.Id));
            }

            if (filters.Keys.Contains("warehouse") && !string.IsNullOrEmpty(filters["warehouse"]))
            {
                Guid warehouseId = new Guid(filters["warehouse"]);
                List<Guid?> parentIds = henBatches.Where(hb => hb.WarehouseId == warehouseId && hb.ParentId.HasValue).Select(hb => hb.ParentId).ToList();
                henBatches = henBatches.Where(hb => hb.WarehouseId == warehouseId || parentIds.Contains(hb.Id));
            }

            if (filters.Keys.Contains("line") && !string.IsNullOrEmpty(filters["line"]))
            {
                Guid lineId = new Guid(filters["line"]);
                List<Guid?> parentIds = henBatches.Where(hb => hb.LineId == lineId && hb.ParentId.HasValue).Select(hb => hb.ParentId).ToList();
                henBatches = henBatches.Where(hb => hb.LineId == lineId || parentIds.Contains(hb.Id));
            }

            if (filters.Keys.Contains("formula") && !string.IsNullOrEmpty(filters["formula"]))
            {
                string formulaId = filters["formula"];
                henBatches = henBatches.Where(hb => hb.FormulaId.Contains(formulaId));
            }

            return henBatches;
        }

        /// <summary>
        /// Closes asynchronously the hen batch setting the end date with the current timestamp.
        /// Before closing, check that a hen batch performance report was submitted this week
        /// </summary>
        public async Task<BusinessValidationResult<HenBatch>> CloseAsync(Guid id, string reason, DateTime closingDate)
        {
            try
            {
                HenBatch henBatch = henBatchService.GetWithMaterial(id);
                henBatch.DateEnd = closingDate;
                henBatch.Reason = reason;
                henBatch.Active = false;
                businessValidationManager
                        .BuildValidation<ClosingDateRangeHenBatchValidation>()
                        .BuildValidation<EmptyHenBatchReportValidation>()
                        .BuildValidation<ShippingNoteHenBatchValidation>()
                        .BuildValidation<ClosingWithHensHenBatchValidation>()
                        .BuildValidation<PendingAdjustmentValidation>()
                        .BuildValidation<InconsistencyValidation>()
                        .Validate(henBatch);

                if (businessValidationManager.Succeeded)
                {
                    List<HenBatch> children = henBatchService.GetAll().Where(hb => hb.ParentId.HasValue && hb.ParentId == id).ToList();
                    foreach (HenBatch child in children)
                    {
                        child.DateEnd = closingDate;
                        child.Reason = reason;
                        child.Active = false;
                        await henBatchService.UpdateAsync(child);
                    }
                    await henBatchService.UpdateAsync(henBatch);
                }

                return new BusinessValidationResult<HenBatch>(businessValidationManager);
            }
            catch (Exception ex)
            {
                throw exceptionManager.Handle(ex);
            }
        }

        /// <summary>
        /// Delete hen batch asynchronically only if it has no hen report associated.
        /// Also, removes hen batch from the line.
        /// </summary>
        private async Task Delete(Guid id, bool deleteDistributions = false)
        {
            // Gets hen batch with all its relationships included.
            HenBatch dbhenBatch = await henBatchService.GetAll()
                    .Include(hb => hb.AcceptedMaterialType)
                    .Include(hb => hb.OriginContainers)
                    .Include(hb => hb.AreaContainers)
                    .FirstAsync(hb => hb.Id == id);

            await ValidateAndDelete(dbhenBatch);

            if (deleteDistributions)
            {
                HenBatch[] distributions = await henBatchService.GetAll()
                    .Include(hb => hb.AcceptedMaterialType)
                    .Include(hb => hb.OriginContainers)
                    .Include(hb => hb.AreaContainers)
                    .Where(hb => hb.ParentId == id)
                    .ToArrayAsync();

                foreach (HenBatch distribution in distributions)
                    await ValidateAndDelete(distribution);
            }


            async Task ValidateAndDelete(HenBatch henBatch)
            {
                businessValidationManager
                    .BuildValidation<NoHenBatchReportValidation>()
                    .BuildValidation<ClosingWithHensHenBatchValidation>()
                    .Validate(henBatch);

                if (!businessValidationManager.Succeeded)
                    throw new ValidationException(null, new BusinessValidationResult<HenBatch>(businessValidationManager).UnsuccessfulValidations);

                // Delete hen Batch accepted material type
                henBatch.AcceptedMaterialType.Clear();

                // Delete hen Batch origin containers
                henBatch.OriginContainers.Clear();

                // Delete hen Batch area containers
                henBatch.AreaContainers.Clear();

                // Delete hen batch.
                await henBatchService.DeleteAsync(henBatch);
            }

        }

        public async Task DeleteAsync(Guid id, bool deleteDistributions = false)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                await Delete(id, deleteDistributions);
            });
        }

        /// <summary>
        /// Get list of origin or destination options for bird movement, whether it is internal or external. 
        /// </summary>
        public List<MovementOptionDTO> GetBirdMovementOptions(Guid id, string intern, bool origin = true)
        {
            HenBatch henBatch = henBatchService.Get(id);
            //Build origin options 
            if (origin)
            {
                List<HenStage> possibleHenStagesOrigins = henBatch.HenStage switch
                {
                    HenStage.Breeding => new List<HenStage>() { HenStage.Breeding },
                    HenStage.Laying => new List<HenStage>() { HenStage.Breeding, HenStage.Laying },
                    _ => new List<HenStage>() { }
                };

                if (intern == MovementTypes.Internal && possibleHenStagesOrigins.Any())
                {
                    //Internal
                    //Right hen stage, active and with the right hen in stock
                    IQueryable<HenBatch> henBatches = henBatchService.GetAll().Where(hb => possibleHenStagesOrigins.Contains(hb.HenStage)
                        && !hb.DateEnd.HasValue
                        && hb.HenAmountFemale > 0
                        && hb.GeneticId == henBatch.GeneticId);
                    return henBatches.OrderBy(hb => hb.DetailedName).Select(hb => new MovementOptionDTO(hb, true)).ToList();
                }
                else
                {
                    //External
                    IQueryable<Person> people = personService.GetAllActive().Where(p => p.PersonRole == PersonRoleEnum.Supplier);
                    return people.OrderBy(p => p.Name).Select(p => new MovementOptionDTO(p)).ToList();
                }
            }
            else
            { //Build destination options
                List<HenStage> possibleHenStagesDestinations = henBatch.HenStage switch
                {
                    HenStage.Breeding => new List<HenStage>() { HenStage.Breeding, HenStage.Laying },
                    HenStage.Laying => new List<HenStage>() { HenStage.Laying },
                    _ => new List<HenStage>() { }
                };

                if (intern == MovementTypes.Internal && possibleHenStagesDestinations.Any())
                {
                    //Internal
                    //Right hen stage, active, with room for more hens and of the right genetic
                    IQueryable<HenBatch> henBatches = henBatchService.GetAll(asNoTracking: true)
                        .Where(hb => possibleHenStagesDestinations.Contains(hb.HenStage) &&
                              !hb.DateEnd.HasValue &&
                               hb.AcceptedMaterialType.FirstOrDefault(am => am.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue - (hb.HenAmountFemale + hb.HenAmountMale) > 0 &&
                               hb.GeneticId == henBatch.GeneticId)
                        .Include(hb => hb.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType);

                    return henBatches.OrderBy(hb => hb.DetailedName).Select(hb => new MovementOptionDTO(hb, false)).ToList();
                }
                else
                {
                    //External
                    IQueryable<Person> people = personService.GetAllActive().Where(p => p.PersonRole == PersonRoleEnum.Customer);
                    return people.OrderBy(p => p.Name).Select(p => new MovementOptionDTO(p)).ToList();
                }
            }
        }

        /// <summary>
        /// Get list of origin or destination options for internal bird movement of the same henstage.
        /// </summary>
        public List<MovementOptionDTO> GetSameStageBirdMovementOptions(HenStage? henStage = null, Guid? id = null)
        {
            List<MovementOptionDTO> options = new List<MovementOptionDTO>();

            if (henStage.HasValue) // origin options
            {
                List<MovementOptionDTO> origins = henBatchService.GetAll()
                    .Include(hb => hb.AcceptedMaterialType).ThenInclude(amt => amt.MaterialType)
                        .Where(hb => hb.HenStage == henStage
                            && !hb.DateEnd.HasValue
                            && hb.HenAmountFemale > 0)
                        .OrderBy(o => o.DetailedName)
                        .Select(hb => new MovementOptionDTO(hb, false))
                        .ToList();

                options.AddRange(origins);
            }

            if (id.HasValue) // destination options
            {
                HenBatch henBatch = henBatchService.Get(id.Value);
                int minWeek = henBatch.BatchWeekNumber - 5;
                int maxWeek = henBatch.BatchWeekNumber + 5;

                List<MovementOptionDTO> destinations = henBatchService.GetAll()
                    .Where(hb => hb.HenStage == henBatch.HenStage
                        && hb.Id != henBatch.Id
                        && !hb.DateEnd.HasValue
                        && hb.AcceptedMaterialType.FirstOrDefault(am => am.MaterialTypeId == MaterialTypes.ActivoBiologicoProductivoAve).CapacityStandarizedValue - (hb.HenAmountMale + hb.HenAmountFemale) > 0
                        && hb.GeneticId == henBatch.GeneticId
                        && (hb.BatchWeekNumber >= minWeek && hb.BatchWeekNumber <= maxWeek))
                    .OrderBy(o => o.DetailedName)
                    .Select(hb => new MovementOptionDTO(hb, true)).ToList();

                options.AddRange(destinations);
            }

            return options;
        }

        /// <summary>
        /// Get list of material options for bird movement, for user to match with genetic. 
        /// </summary>
        public List<SelectListItem> GetMaterialOptions(bool origin = true)
        {
            //If the hen batch is the destination of the birds, the user has to match the genetic name with one of the materials
            if (!origin)
                return materialService.GetAllWithMaterialType()
                    .Where(m => m.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve)).OrderBy(m => m.Name)
                    .Select(m => new SelectListItem(m.Name, m.Id.ToString())).ToList();
            else // if the hen batch is the origin of the birds, the user has to match the material in stock with a genetic
                return geneticService.GetAll().Select(g => new SelectListItem(g.Name, g.Id.ToString())).ToList();
        }

        /// <summary>
        /// Moves birds from an origin to a destination. 
        /// </summary>
        public async Task MoveBirds(BirdMovementDTO dto)
        {
            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            async Task AsyncLogic()
            {
                ShippingNote shippingNote = new ShippingNote()
                {
                    Date = dto.Date,
                    OriginId = dto.OriginId,
                    DestinationId = dto.DestinationId
                };

                var materials = await henBatchService.GetAll(true)
                    .Where(hb => hb.Id == dto.OriginId.Value)
                    .Select(hb => new
                    {
                        female = hb.MaterialContainers.Where(mc => mc.Material.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAveHembra)).Select(mc => mc.MaterialId).FirstOrDefault(),
                        male = hb.MaterialContainers.Where(mc => mc.Material.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAveMacho)).Select(mc => mc.MaterialId).FirstOrDefault()
                    }).FirstAsync();

                if (dto.QuantityFemale > 0)
                {
                    shippingNote.MaterialsShipped.Add(new MaterialShipped()
                    {
                        MaterialId = materials.female,
                        Quantity = dto.QuantityFemale
                    });
                }
                if (dto.QuantityMale > 0)
                {
                    shippingNote.MaterialsShipped.Add(new MaterialShipped()
                    {
                        MaterialId = materials.male,
                        Quantity = dto.QuantityMale
                    });
                }

                BusinessValidationResult<ShippingNote> result = ValidateMoveBirds(shippingNote);

                if (result != null && !result.IsValid)
                    throw new ValidationException(null, result.UnsuccessfulValidations);

                BusinessValidationResult<ShippingNote> SNResult = await containerBusinessLogic.RelateShippingNote(shippingNote, false, false);

                if (SNResult != null && !SNResult.IsValid)
                    throw new ValidationException(null, SNResult.UnsuccessfulValidations);
            }
        }

        /// <summary>
        /// Moves birds from an origin to a destination.
        /// Joins material selections and movment validation to avoid multiples queries to DB.
        /// </summary>
        private async Task MoveBirds(IEnumerable<BirdMovementDTO> movements)
        {
            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            async Task AsyncLogic()
            {
                List<ShippingNote> shippingNotes = movements.Select(m => new ShippingNote()
                {
                    Date = m.Date,
                    OriginId = m.OriginId,
                    DestinationId = m.DestinationId
                }).ToList();

                IEnumerable<Guid> origins = movements.Select(d => d.OriginId.Value);

                var materials = unitOfWork.GetModelDbContext().Set<MaterialContainer>().TagWith($"{nameof(HenBatchBusinessLogic)} 1577")
                    .Where(mc =>
                        origins.Contains(mc.ContainerId)
                            && (mc.Material.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAveHembra)
                                || mc.Material.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAveMacho)))
                    .Select(mc => new
                    {
                        Container = mc.ContainerId,
                        Material = mc.MaterialId,
                        MaterialTypePath = mc.Material.MaterialType.Path
                    })
                    .AsEnumerable()
                    .GroupBy(mc =>
                        mc.Container,
                        (k, v) => new
                        {
                            Origin = k,
                            MaleMaterial = v.Where(v => v.MaterialTypePath.Contains(MaterialTypePaths.ActivoBiologicoProductivoAveMacho)).Select(mc => mc.Material).FirstOrDefault(),
                            FemaleMaterial = v.Where(v => v.MaterialTypePath.Contains(MaterialTypePaths.ActivoBiologicoProductivoAveHembra)).Select(mc => mc.Material).FirstOrDefault()
                        })
                    .ToList();

                foreach (BirdMovementDTO dto in movements)
                {
                    List<ShippingNote> shippingNoteSameOrigin = shippingNotes.Where(sn => sn.OriginId == dto.OriginId).ToList();
                    ShippingNote shippingNote;
                    if (shippingNoteSameOrigin.Count() > 1)
                        shippingNote = shippingNoteSameOrigin.First(sn => sn.DestinationId == dto.DestinationId);
                    else
                        shippingNote = shippingNoteSameOrigin.First();

                    var material = materials.First(m => m.Origin == dto.OriginId);

                    if (dto.QuantityFemale > 0)
                    {
                        if (!shippingNote.MaterialsShipped.Any(ms => ms.MaterialId == material.FemaleMaterial))
                        {
                            shippingNote.MaterialsShipped.Add(new MaterialShipped()
                            {
                                MaterialId = material.FemaleMaterial,
                                Quantity = dto.QuantityFemale
                            });
                        }
                        else
                        {
                            shippingNote.MaterialsShipped.First(ms => ms.MaterialId == material.FemaleMaterial).Quantity += dto.QuantityFemale;
                        }
                    }
                    if (dto.QuantityMale > 0)
                    {
                        if (!shippingNote.MaterialsShipped.Any(ms => ms.MaterialId == material.MaleMaterial))
                        {
                            shippingNote.MaterialsShipped.Add(new MaterialShipped()
                            {
                                MaterialId = material.MaleMaterial,
                                Quantity = dto.QuantityMale
                            });
                        }
                        else
                        {
                            shippingNote.MaterialsShipped.First(ms => ms.MaterialId == material.MaleMaterial).Quantity += dto.QuantityMale;
                        }
                    }
                }

                BusinessValidationResult<IEnumerable<ShippingNote>> result = ValidateMoveBirds(shippingNotes);

                if (result != null && !result.IsValid)
                {
                    throw new ValidationException(null, result.UnsuccessfulValidations);
                }

                foreach (ShippingNote item in shippingNotes)
                {
                    BusinessValidationResult<ShippingNote> SNResult = await containerBusinessLogic.RelateShippingNote(item, false, false);

                    if (SNResult != null && !SNResult.IsValid)
                    {
                        throw new ValidationException(null, SNResult.UnsuccessfulValidations);
                    }
                }
            }
        }

        /// <summary>
        /// Updates FirstProductionDate in distributions and parent
        /// </summary>
        public async Task FirstProductionDate(List<FirstProductionDateDTO> dtos, Guid henbatchId, DateTime? capitalizationDate)
        {
            IIncludableQueryable<HenBatch, HenWarehouse> henBatches = henBatchService.GetAll()
                                            .Include(hb => hb.Line).ThenInclude(hb => hb.Warehouse);

            IQueryable<HenBatch> childrenQuery = henBatches.Where(hb => hb.ParentId == henbatchId && hb.HenAmountFemale > 0);
            List<HenBatch> childrenHenBatches = childrenQuery.ToList();

            DateTime? previousCapitalizationDate = capitalizationDate.HasValue ? capitalizationDate.Value.AddDays(-1) : (DateTime?)null;
            IQueryable<HenReport> henReports = previousCapitalizationDate != null ? henReportService.GetAll() : null;
            bool childHenReports = true;
            if (previousCapitalizationDate != null)
            {
                List<bool> hasAnyReport = new List<bool>();
                foreach (HenBatch hb in childrenHenBatches)
                    hasAnyReport.Add(henReports.Where(hr => hr.HenBatchId == hb.Id && hr.Date.Date == previousCapitalizationDate.Value.Date).Any());
                childHenReports = hasAnyReport.All(r => r == true);
            }

            List<ValidationException> errors = new List<ValidationException>();
            uint totalFemalesOnFirstProductionDate = 0;
            uint previousTotalFemalesOnFirstProductionDate = 0;
            if (!childHenReports && childrenHenBatches.Any())
                errors.Add(new ValidationException($"Validations", this.localizer[Lang.CapitalizationDateValidation]));
            else if (childrenHenBatches.Any())
            {
                foreach (HenBatch hb in childrenHenBatches)
                {
                    // Get the first production date of the henbatch from its henWarehouse
                    string fpDate = dtos.Where(fpd => fpd.HenWarehouseId == hb.Line.WarehouseId)
                                        .Select(fpd => fpd.Date)
                                        .FirstOrDefault();
                    DateTime? fpDateTime = fpDate != null ? DateTime.Parse(fpDate).Date : (DateTime?)null;

                    // Get henreport of the henbatch on the date previous to the capitalization date
                    HenReport childHenReport = previousCapitalizationDate != null
                                            ? henReports.Where(hr => hr.HenBatchId == hb.Id && hr.Date.Date == previousCapitalizationDate.Value.Date)
                                                .OrderByDescending(hr => hr.CreatedDate)
                                                .FirstOrDefault()
                                            : null;

                    if (hb.FirstProductionDate != null)
                        previousTotalFemalesOnFirstProductionDate = hb.FemalesOnFirstProductionDate + previousTotalFemalesOnFirstProductionDate;
                    hb.FemalesOnFirstProductionDate = childHenReport != null ? Convert.ToUInt32(childHenReport.HenAmountFemale) : 0;
                    totalFemalesOnFirstProductionDate = totalFemalesOnFirstProductionDate + hb.FemalesOnFirstProductionDate;
                    hb.FirstProductionDate = fpDateTime;
                }
            }

            HenBatch parentHenBatch = henBatches.Where(hb => hb.Id == henbatchId).FirstOrDefault();

            if (!childrenHenBatches.Any())
            {
                DateTime? fpDateTime = dtos[0].Date != null ? DateTime.Parse(dtos[0].Date).Date : (DateTime?)null;
                HenReport parentHenReport = previousCapitalizationDate != null
                                                ? henReports.Where(hr => hr.HenBatchId == henbatchId && hr.Date.Date == previousCapitalizationDate.Value.Date)
                                                    .OrderByDescending(hr => hr.CreatedDate)
                                                    .FirstOrDefault()
                                                : null;
                if (parentHenReport == null)
                    errors.Add(new ValidationException($"Validations", this.localizer[Lang.CapitalizationDateValidation]));
                else
                {
                    parentHenBatch.FemalesOnFirstProductionDate = parentHenReport != null ? Convert.ToUInt32(parentHenReport.HenAmountFemale) : 0;
                    parentHenBatch.FirstProductionDate = fpDateTime;
                }
            }
            else if (!errors.Any())
            {
                string date = dtos.Min(fpd => fpd.Date);
                parentHenBatch.FemalesOnFirstProductionDate = totalFemalesOnFirstProductionDate;
                parentHenBatch.FirstProductionDate = date != null ? DateTime.Parse(date).Date : (DateTime?)null;
            }

            if (errors.Any())
                throw new ValidationException("", errors.SelectMany(e => e.Errors).Distinct().ToDictionary(e => e.Key, e => e.Value));

            childrenHenBatches.Add(parentHenBatch);
            foreach (HenBatch henBatch in childrenHenBatches)
            {
                string fpDate = "";
                if (henBatch.ParentId != null)
                    fpDate = dtos.Where(fpd => fpd.HenWarehouseId == henBatch.Line.WarehouseId)
                                   .Select(fpd => fpd.Date)
                                   .FirstOrDefault();
                else if (childrenHenBatches.Count() > 1)
                    fpDate = dtos.Where(fpd => fpd.Date != null)
                                   .Select(fpd => fpd.Date)
                                   .Min();
                else
                    fpDate = dtos[0].Date;

                henBatch.CapitalizationDate = capitalizationDate;

                if (capitalizationDate != null)
                    await henBatchPerformanceBusinessLogic.UpdateFirstProductionDate(henBatch);
                else
                    await henBatchPerformanceBusinessLogic.RevertFirstProductionDate(henBatch);
                await henBatchService.UpdateAsync(henBatch);
            }
            int week = henBatchService.GetCurrentWeekNumberForDate(parentHenBatch.Id, capitalizationDate.Value);
            await updateHenBatchPerformanceBusinessLogic.UpdatePerformancesForASpecificHenBatch(parentHenBatch.Id, week);

        }

        private IEnumerable<ExcelExportDTO> GetDTOs(HenBatch henBatch)
        {
            yield return new ExcelExportDTO()
            {
                FarmCode = henBatch.Farm.Code,
                FarmName = henBatch.Farm.Name,
                HenBatch = henBatch.Code,
                Henwarehouse = henBatch.Line.Code,
                Line = henBatch.Line.Warehouse.Code,
                DateStart = henBatch.OpeningDate.HasValue ? henBatch.OpeningDate.Value : henBatch.DateStart.Value,
                FirstReception = henBatch.HenReportCreationMinDate,
                Week = henBatch.BatchWeekNumber + (DateTime.Today.Date - henBatch.DateStart.Value.GetPastSelectedDay(henBatch.Farm.DayOfWeek)).Days / 7,
                Genetic = henBatch.Genetic.Name,
                HenAmount = henBatch.HenAmountFemale + henBatch.HenAmountMale,
                HenAmountFemale = henBatch.HenAmountFemale,
                HenAmountMale = henBatch.HenAmountMale,
                InitialHenAmount = henBatch.InitialHenAmountFemale + henBatch.InitialHenAmountMale,
                InitialHenAmountFemale = henBatch.InitialHenAmountFemale,
                InitialHenAmountMale = henBatch.InitialHenAmountMale,
            };
        }

        public async Task<ExportResult> ExportExcel(string searchTerm, Dictionary<string, string> data, HenStage? henStage)
        {
            if (data == null)
            {
                Dictionary<string, string> defaultSettings = new Dictionary<string, string>
                {
                    ["henStage"] = henStage.HasValue ? henStage.ToString() : "",
                    ["status"] = HenBatchStatus.Open,
                    ["start-date-start"] = "",
                    ["start-date-end"] = "",
                    ["end-date-start"] = "",
                    ["end-date-end"] = "",
                    ["genetic"] = "",
                    ["farm"] = "",
                    ["cluster"] = "",
                    ["warehouse"] = "",
                    ["line"] = "",
                    ["formula"] = ""
                };
                data = defaultSettings;
            }

            IQueryable<HenBatch> query = GetAllFiltered(data, henStage);

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(hb =>
                    hb.Farm.Name.Contains(searchTerm) ||
                    hb.Line.Warehouse.Cluster.Name.Contains(searchTerm) ||
                    hb.Line.Warehouse.Name.Contains(searchTerm) ||
                    hb.Line.Name.Contains(searchTerm)
                );
            }

            IQueryable<ExcelExportDTO> dtos = query.ToList().SelectMany(r => GetDTOs(r)).AsQueryable();

            string fileDownloadName = string.Format(localizer[Lang.ExcelExportFilename], EnumHelper<HenStage>.GetDisplayName(henStage.Value, localizer), DateTime.Now.ToString("dd/MM/yyyy"));

            return await excelExportBusinessLogic.ExportExcel(dtos, fileDownloadName);
        }

        /// <summary>
        /// Get the first date of movement of hens of the parent hen batch.
        /// </summary>
        public DateTime? GetFirstDateHenMovementForParent(IEnumerable<HenBatch> children)
        {
            Guid[] ids = children.Select(c => c.Id).ToArray();

            return henBatchService.GetAll(true)
                .Where(hb => ids.Contains(hb.Id))
                .SelectMany(hb => hb.DestinationShippingNotes)
                .Where(sn => sn.MaterialsShipped.Any(ms => ms.Material.MaterialType.Path.StartsWith(MaterialTypePaths.ActivoBiologicoProductivoAve)))
                .Select(sn => (DateTime?)sn.Date)
                .DefaultIfEmpty()
                .Min();
        }

        /// <summary>
        /// Asynchronously retrieves a parent or single henbatch that belongs to a given farm by code.
        /// </summary>
        public async Task<HenBatch> GetByCodeAndFarmAsync(string code, string farmCode)
        {
            HenBatch henBatch;

            Guid? farmId = await farmService.GetAll().Where(f => f.Code.ToUpper() == farmCode.ToUpper()).Select(f => f.Id).FirstOrDefaultAsync();

            if (farmId == null)
                throw new NotFoundException(localizer[Lang.FarmNotFoundEx]);
            else
                henBatch = await henBatchService.GetAll().FirstOrDefaultAsync(c => c.Code.ToUpper() == code.ToUpper() && c.FarmId == farmId && !c.ParentId.HasValue);

            if (henBatch == null)
                throw new NotFoundException(localizer[Lang.HenBatchNotFoundEx]);

            return henBatch;
        }

        /// <summary>
        /// Returns parent hen batches whose children have available space for birds.
        /// </summary>
        public IQueryable<HenBatch> GetAvailableParents(HenStage? henStage = null)
        {
            IQueryable<HenBatch> parents = henBatchService.GetAll()
                .Include(hb => hb.Farm)
                .Include(hb => hb.Genetic).ThenInclude(g => g.MaleMaterial)
                .Include(hb => hb.Genetic).ThenInclude(g => g.FemaleMaterial)
                .Include(hb => hb.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                .Where(hb => hb.LineId == null && hb.DateEnd == null);

            IQueryable<Guid?> childrenParent = henBatchService.GetAllWithAvailableSpace(true, true, MaterialTypePaths.ActivoBiologicoProductivoAve)
                .Where(c => c.ContainerType == ContainerTypes.HenBatch && c.DateEnd == null && c.ParentId.HasValue)
                .Select(hb => hb.ParentId)
                .Distinct();

            if (henStage.HasValue)
                parents = parents.Where(p => p.HenStage == henStage);

            return parents.Where(p => childrenParent.Contains(p.Id));
        }

        /// <summary>
        ///  returns a list of origins containers for the childrens hen batches 
        /// </summary>
        public List<Container> GetChildsOrigins(Guid parentId, HenStage? henStage = null)
        {
            if (!henStage.HasValue)
                henStage = henBatchService.Get(parentId).HenStage;

            IQueryable<HenBatch> childrens = henBatchService.GetAllWithAvailableSpace(true, true, MaterialTypePaths.ActivoBiologicoProductivoAve)
                .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(o => o.AcceptedMaterialType).ThenInclude(am => am.MaterialType)
                .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin).ThenInclude(o => o.MaterialContainers).ThenInclude(mc => mc.Material).ThenInclude(m => m.MaterialType)
                .Where(c => c.ContainerType == ContainerTypes.HenBatch && c.DateEnd == null && c.ParentId.HasValue);

            IQueryable<Container> origins = childrens.SelectMany(c => c.OriginContainers).Select(o => o.Origin).Distinct();

            if (henStage == HenStage.Breeding)
                origins = origins.Where(o =>
                o.MaterialContainers.Any(mc =>
                        mc.Material.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAve)
                        && mc.Quantity > 0)
                || o.AcceptedMaterialType.Any(am =>
                        am.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAve)
                        && (am.ActionEnum == ActionsEnum.Produce || am.ActionEnum == ActionsEnum.ConsumeAndProduce))
                );

            if (henStage == HenStage.Laying)
                origins = origins.Where(o =>
                o.MaterialContainers.Any(mc =>
                        mc.Material.MaterialType.Path.Contains(MaterialTypePaths.ActivoBiologicoProductivoAve)
                        && mc.Quantity > 0)
                );

            return origins.ToList();
        }

        /// <summary>
        ///  validate and set FirstProductionDate to the hen batch parent and all its children and update
        /// </summary>
        public async Task SetFirstProductionDateAndUpdate(Guid id, DateTime date)
        {
            DateTime? firstDateReceptionBirds;
            HenBatch henBatchParent = henBatchService.Get(id);
            List<Guid> henBatchesChild = henBatchService.GetAll().Where(hb => hb.ParentId == id).Select(hb => hb.Id).ToList();

            firstDateReceptionBirds = henBatchService.GetFirstHenMovementDate(id);

            if (henBatchParent.DateStart > date && firstDateReceptionBirds > date)
                throw new Exception(localizer[Lang.DateCannotBeLess]);

            henBatchParent.FirstProductionDate = date;
            await UpdateParentAndChildren(henBatchParent);
        }

        private async Task UpdateParentAndChildren(HenBatch henBatchParent)
        {
            List<HenBatch> henBatches = henBatchService.GetAll().Where(hb => hb.ParentId == henBatchParent.Id).ToList();

            foreach (HenBatch henBatchChildren in henBatches)
                henBatchChildren.FirstProductionDate = henBatchParent.FirstProductionDate;

            henBatches.Add(henBatchParent);

            foreach (HenBatch henBatch in henBatches)
                await henBatchService.UpdateAsync(henBatch);
        }

        private BusinessValidationResult<ShippingNote> ValidateMoveBirds(ShippingNote shippingNote)
        {
            businessValidationManagerMoveBirds
                   .BuildValidation<DateMoveBirdHenBatchValidation>()
                   .Validate(shippingNote);

            if (businessValidationManagerMoveBirds.Succeeded)
                return null;

            return new BusinessValidationResult<ShippingNote>(businessValidationManagerMoveBirds);
        }

        private BusinessValidationResult<IEnumerable<ShippingNote>> ValidateMoveBirds(IEnumerable<ShippingNote> shippingNotes)
        {
            businessValidationManagerMoveBirdsUpgrade
                   .BuildValidation<DateMoveBirdHenBatchesValidation>()
                   .Validate(shippingNotes);

            if (businessValidationManagerMoveBirds.Succeeded)
                return null;

            return new BusinessValidationResult<IEnumerable<ShippingNote>>(businessValidationManagerMoveBirdsUpgrade);
        }

        /// <summary>
        /// Gets all the casualty reasons by area with it's quantities for male and female
        /// </summary>
        public (List<string> headers, List<Dictionary<string, string>> table) GetDistributionQuantitiesTable(Guid henBatchId)
        {
            List<string> headers = new List<string>()
            {
                localizer[Lang.HenBatchHeader],
                localizer[Lang.HenwarehouseHeader],
                localizer[Lang.LineHeader],
                localizer[Lang.FemaleInitialHenamountHeader],
                localizer[Lang.FemaleFinalHenamountHeader],
                localizer[Lang.MaleInitialHenamountHeader],
                localizer[Lang.MaleFinalHenamountHeader]
            };

            List<Dictionary<string, string>> table = new List<Dictionary<string, string>>();

            var henBatches = henBatchService.GetAll().Where(hb => hb.ParentId == henBatchId)
                .Select(hb => new
                {
                    HenBatchId = hb.Id,
                    HenWarehouse = hb.Line.Warehouse.Name,
                    Line = hb.Line.Name,
                    hb.HenAmountFemale,
                    hb.HenAmountMale,
                })
                .OrderBy(hb => hb.HenWarehouse).ThenBy(hb => hb.Line)
                .ToArray();

            foreach (var item in henBatches)
            {
                Dictionary<string, string> row = NewRowDictionary();

                row["henbatchId"] = item.HenBatchId.ToString();
                row["henwarehouse"] = item.HenWarehouse.ToString();
                row["line"] = item.Line.ToString();
                row["femaleInitialHenamount"] = item.HenAmountFemale.ToString();
                row["maleInitialHenamount"] = item.HenAmountMale.ToString();

                table.Add(row);
            }

            return (headers, table);
        }

        private Dictionary<string, string> NewRowDictionary()
        {
            Dictionary<string, string> dict = new Dictionary<string, string>();

            List<string> keys = new List<string>()
                {
                    "henbatchId",
                    "henwarehouse",
                    "line",
                    "femaleInitialHenamount",
                    "femaleFinalHenamount",
                    "maleInitialHenamount",
                    "maleFinalHenamount"
                };

            foreach (string key in keys)
                dict.Add(key, "");

            return dict;
        }

        public async Task RedistributeHen(HenRedistributionsDTO redistributions)
        {
            IEnumerable<HenRedistributionsDTO.HenRedistributionDTO> adjustedUp = redistributions.Redistributions
                .Where(d => d.FemaleFinalHenamount > d.FemaleInitialHenamount || d.MaleFinalHenamount > d.MaleInitialHenamount);

            List<HenAdjustment> adjustedDownFemaleHenBatches = redistributions.Redistributions
                .Where(d => d.FemaleFinalHenamount < d.FemaleInitialHenamount)
                .Select(d => new HenAdjustment()
                {
                    Id = d.HenBatchId,
                    Difference = d.FemaleInitialHenamount - d.FemaleFinalHenamount
                })
                .ToList();

            List<HenAdjustment> adjustedDownMaleHenBatches = redistributions.Redistributions
                .Where(d => d.MaleFinalHenamount < d.MaleInitialHenamount)
                .Select(d => new HenAdjustment()
                {
                    Id = d.HenBatchId,
                    Difference = d.MaleInitialHenamount - d.MaleFinalHenamount
                })
                .ToList();

            List<BirdMovementDTO> movements = new List<BirdMovementDTO>();

            foreach (HenRedistributionsDTO.HenRedistributionDTO item in adjustedUp)
            {
                int femaleAdjustent = item.FemaleFinalHenamount - item.FemaleInitialHenamount;
                int maleAdjustment = item.MaleFinalHenamount - item.MaleInitialHenamount;

                while (femaleAdjustent > 0 || maleAdjustment > 0)
                {
                    BirdMovementDTO movement = CreateMovement(adjustedDownFemaleHenBatches, adjustedDownMaleHenBatches, item, ref femaleAdjustent, ref maleAdjustment, redistributions.Date);
                    movements.Add(movement);
                }
            }

            if (movements.Count == 0)
            {
                return;
            }

            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                await MoveBirds(movements);
                UpdateInitialHenAmounts(movements);
            });
        }

        private void UpdateInitialHenAmounts(List<BirdMovementDTO> movements)
        {
            IEnumerable<Guid?> origins = movements.Select(M => M.OriginId);

            List<HenBatch> henBatches = henBatchService.GetAll()
                .Where(hb => origins.Contains(hb.Id))
                .ToList();

            henBatches.ForEach(hb =>
            {
                hb.InitialHenAmountFemale -= movements.Where(m => m.OriginId == hb.Id).Select(m => m.QuantityFemale).First();
                hb.InitialHenAmountMale -= movements.Where(m => m.OriginId == hb.Id).Select(m => m.QuantityMale).First();
                henBatchService.Update(hb);
            });
        }

        public void UpdateInitialHenAmount(BirdMovementDTO movement)
        {
            HenBatch henBatch = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == movement.OriginId);

            if (henBatch != null)
            {
                henBatch.InitialHenAmountFemale -= movement.QuantityFemale;
                henBatch.InitialHenAmountMale -= movement.QuantityMale;
                henBatchService.Update(henBatch);
            }
        }

        private static BirdMovementDTO CreateMovement(List<HenAdjustment> adjustedDownFemaleHenBatches, List<HenAdjustment> adjustedDownMaleHenBatches, HenRedistributionsDTO.HenRedistributionDTO item, ref int femaleAdjustent, ref int maleAdjustment, DateTime date)
        {
            BirdMovementDTO movement = new BirdMovementDTO()
            {
                Date = date,
                DestinationId = item.HenBatchId,
            };

            if (femaleAdjustent > 0)
            {
                HenAdjustment femaleOrigin = adjustedDownFemaleHenBatches.FirstOrDefault(hb => hb.Difference > 0);
                movement.OriginId = femaleOrigin.Id;

                if (femaleAdjustent > femaleOrigin.Difference)
                {
                    movement.QuantityFemale = femaleOrigin.Difference;
                    femaleAdjustent -= femaleOrigin.Difference;
                    femaleOrigin.Difference = 0;
                }
                else
                {
                    movement.QuantityFemale = femaleAdjustent;
                    femaleOrigin.Difference -= femaleAdjustent;
                    femaleAdjustent = 0;
                }
            }
            if (maleAdjustment > 0)
            {
                HenAdjustment maleOrigin = null;

                if (movement.OriginId.HasValue)
                    maleOrigin = adjustedDownMaleHenBatches.FirstOrDefault(hb => hb.Id == movement.OriginId && hb.Difference > 0);
                else
                {
                    maleOrigin = adjustedDownMaleHenBatches.FirstOrDefault(hb => hb.Difference > 0);
                    movement.OriginId = maleOrigin.Id;
                }

                if (maleOrigin != null)
                {
                    if (maleAdjustment > maleOrigin.Difference)
                    {
                        movement.QuantityMale = maleOrigin.Difference;
                        maleAdjustment -= maleOrigin.Difference;
                        maleOrigin.Difference = 0;
                    }
                    else
                    {
                        movement.QuantityMale = maleAdjustment;
                        maleOrigin.Difference -= maleAdjustment;
                        maleAdjustment = 0;
                    }
                }
            }
            return movement;
        }

        internal class HenAdjustment
        {
            public Guid Id { get; set; }

            public int Difference { get; set; }
        }

        /// <summary>
        /// Gets page of capitalization by farm code
        /// </summary>
        public async Task<HenBatchCapitalizationPage> GetCapitalization(HenBatchCapitalizationReqDTO henBatchCapitalizationDTO)
        {
            Farm farm = await farmService.GetAll(true)
                .Where(f => f.Code == henBatchCapitalizationDTO.FarmCode)
                .FirstOrDefaultAsync();

            HenBatchCapitalizationPage response = new HenBatchCapitalizationPage();

            if (farm is null)
                throw new Exception(localizer[Lang.FarmNotFoundEx]);

            henBatchCapitalizationDTO.To ??= DateTime.Now;

            var henBatchesToCalculate = await henBatchService
                .GetAll(true, false)
                .Where(hb => hb.FarmId == farm.Id && henBatchCapitalizationDTO.From <= hb.CapitalizationDate && henBatchCapitalizationDTO.To >= hb.CapitalizationDate)
                .Select(hb => new
                {
                    hb.Id,
                    hb.ParentId,
                    hb.LineId,
                    HenBatchCode = $"{hb.Farm.Code} - {hb.Code}",
                    LineCode = hb.Line.Code,
                    WarehouseCode = hb.Line.Warehouse.Code,
                    hb.CapitalizationDate,
                    hb.FemalesOnFirstProductionDate,
                })
                .ToListAsync();

            if (!henBatchesToCalculate.Any())
                return response;

            var parentHenBatches = henBatchesToCalculate.Where(hb => !hb.ParentId.HasValue && !hb.LineId.HasValue);
            var uniqueHenBatches = henBatchesToCalculate.Where(hb => !hb.ParentId.HasValue && hb.LineId.HasValue);

            var henBatchesId = henBatchesToCalculate.Select(hb => hb.Id);

            var performanceData = await henBatchPerformanceService.GetAll()
                .Where(hbp => henBatchesId.Contains(hbp.HenBatchId) && hbp.FarmId == farm.Id)
                .Select(hbp => new
                {
                    hbp.HenBatchId,
                    hbp.Date,
                    hbp.HenAmountMale
                })
                .ToListAsync();

            foreach (var data in parentHenBatches)
            {
                HenBatchCapitalizationDTO henBatchCapitalization = new HenBatchCapitalizationDTO()
                {
                    HenBatchName = data.HenBatchCode,
                    Date = DateTime.Today,
                    CapitalizationDate = data.CapitalizationDate,
                };

                var childHenBatches = henBatchesToCalculate.Where(hb => hb.ParentId == data.Id);
                foreach (var aChild in childHenBatches)
                {
                    CapitalizationFemaleDTO capitalizationFemale = new CapitalizationFemaleDTO
                    {
                        HenWarehouseBox = $"{aChild.HenBatchCode} - {aChild.WarehouseCode} - {aChild.LineCode}",
                        FemalesOnCapitalizationDate = aChild.FemalesOnFirstProductionDate,
                        HenAmountMale = performanceData.FirstOrDefault(hbp => hbp.HenBatchId == aChild.Id && hbp.Date <= aChild.CapitalizationDate && hbp.Date.AddDays(7) >= aChild.CapitalizationDate).HenAmountMale,
                    };

                    henBatchCapitalization.CapitalizationFemale.Add(capitalizationFemale);
                }

                response.Items.Add(henBatchCapitalization);
            }

            foreach (var aHenBatch in uniqueHenBatches)
            {
                HenBatchCapitalizationDTO henBatchCapitalization = new HenBatchCapitalizationDTO()
                {
                    HenBatchName = aHenBatch.HenBatchCode,
                    Date = DateTime.Today,
                    CapitalizationDate = aHenBatch.CapitalizationDate,
                };

                CapitalizationFemaleDTO capitalizationFemale = new CapitalizationFemaleDTO
                {
                    HenWarehouseBox = $"{aHenBatch.HenBatchCode} - {aHenBatch.WarehouseCode} - {aHenBatch.LineCode}",
                    FemalesOnCapitalizationDate = aHenBatch.FemalesOnFirstProductionDate,
                    HenAmountMale = performanceData.FirstOrDefault(hbp => hbp.HenBatchId == aHenBatch.Id && hbp.Date <= aHenBatch.CapitalizationDate && hbp.Date.AddDays(7) >= aHenBatch.CapitalizationDate).HenAmountMale,
                };

                henBatchCapitalization.CapitalizationFemale.Add(capitalizationFemale);

                response.Items.Add(henBatchCapitalization);
            }

            response.Items = response.Items.Skip((henBatchCapitalizationDTO.Page - 1) * henBatchCapitalizationDTO.PageSize).Take(henBatchCapitalizationDTO.PageSize).ToList();
            response.PageSize = response.Items.Count();
            response.Page = henBatchCapitalizationDTO.Page;

            return response;
        }
    }
}
