using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Threading.Tasks;
using WebApp.Attributes;
using Microsoft.EntityFrameworkCore;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.LayingController;

namespace WebApp.Controllers
{
    [AuthorizeAnyRoles(Roles.BackofficeSuperAdministrator, Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingDashboard)]
    public class LayingController : DashboardController
    {
        private readonly ICompanyService companyService;
        private readonly IFarmService farmService;
        private readonly IOperationContext operationContext;
        private readonly IHenReportBusinessLogic henReportBusinessLogic;
        private readonly ILineService lineService;
        private readonly IMaterialTypeService materialTypeService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly ISearaChartsBusinessLogic searaChartsBusinessLogic;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly RazorViewRender razorViewRender;

        public LayingController(
            IExceptionManager exceptionManager,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IHenBatchService henBatchService,
            IStringLocalizer<SharedResources> localizer,
            IMessageBusinessLogic messageBusinessLogic,
            IStatisticsBusinessLogic statisticsBusinessLogic,
            IHolidayService holidayService,
            IContainerService<Container> containerService,
            ITaskEntityService taskEntityService,
            IUserService<ApplicationUser> userService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IHappeningBusinessLogic happeningBusinessLogic,
            IMaterialTypeService materialTypeService,
            IOperationContext operationContext,
            ILogger logger,
            ICompanyService companyService,
            IFarmService farmService,
            IHenReportBusinessLogic henReportBusinessLogic,
            ILineService lineService,
            IHenBatchPerformanceService henBatchPerformanceService,
            ISearaChartsBusinessLogic searaChartsBusinessLogic,
            IGeneticsParameterService geneticsParameterService,
            RazorViewRender razorViewRender)
            : base(
                  exceptionManager,
                  henBatchBusinessLogic,
                  henBatchService,
                  farmService,
                  localizer,
                  messageBusinessLogic,
                  statisticsBusinessLogic,
                  holidayService,
                  containerService,
                  taskEntityService,
                  userService,
                  fileService,
                  happeningBusinessLogic,
                  materialTypeService,
                  operationContext,
                  logger)
        {
            this.companyService = companyService;
            this.farmService = farmService;
            this.operationContext = operationContext;
            this.henReportBusinessLogic = henReportBusinessLogic;
            this.lineService = lineService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.searaChartsBusinessLogic = searaChartsBusinessLogic;
            this.geneticsParameterService = geneticsParameterService;
            this.razorViewRender = razorViewRender;
        }

        public IActionResult Dashboard()
        {
            AreaEnum area = AreaEnum.Laying;
            List<AreaEnum> areas = new List<AreaEnum>
            {
                area
            };
            ViewData["Area"] = area;
            ViewData["UserIsAdmin"] = this.operationContext.UserIsInAnyRole(Roles.BackofficeTaskAdministrator,
                                                                            Roles.BackofficeTaskUser,
                                                                            Roles.BackofficeSuperAdministrator,
                                                                            Roles.BackofficeLayingAdministrator).ToString();

            ViewData["DashboardsResources"] = JsLocalizer.GetLocalizedResources(JsLang.BreedingDashboard, this.localizer);
            ViewData["Title"] = localizer[Lang.DashboardTitle];
            //Calendar
            InitCalendar(areas);

            // Performance chart filter initialization
            InitLists();
            DateTime today = DateTime.Today;
            ViewData["MinDayDefault"] = today.AddMonths(-1).Date.ToShortDateString();
            ViewData["Today"] = today.Date.ToShortDateString();

            // Histogram filter initialization
            ViewData["DefaultFilterParameters"] = new FilterDataDTO
            {
                HenBatchHistogramWeekNumberBinLimit1 = HistogramDefaultLimits.LayingLimit1,
                HenBatchHistogramWeekNumberBinLimit2 = HistogramDefaultLimits.LayingLimit2,
                HenBatchHistogramWeekNumberBinLimit3 = HistogramDefaultLimits.LayingLimit3,
                HenBatchHistogramWeekNumberBinLimit4 = HistogramDefaultLimits.LayingLimit4
            };

            // Messages
            Guid userId = this.operationContext.GetUserId();
            var user = userService.GetAll(filterByTenant: true).Where(u => u.Id == userId).FirstOrDefault();
            ViewData["UserLanguage"] = user.Language;

            return View();
        }

        [AuthorizeAnyRoles(Roles.BackofficeSuperAdministrator, Roles.SearaDashboardCardsLaying)]
        public async Task<IActionResult> GetDashboardInformativeCards(string from, string to, Guid? henBatchId, Guid? companyId, Guid? userFilterId)
        {
            List<(string, int)> viewList = new List<(string, int)>();

            if (!henBatchId.HasValue && !companyId.HasValue && !userFilterId.HasValue)
            {
                return Ok(new
                {
                    viewList
                });
            }

            DateTime dateFrom = DateTime.Parse(from);
            DateTime dateTo = DateTime.Parse(to);
            DashboardInformativeCardsDTO dto = new DashboardInformativeCardsDTO();
            List<SimpleDashboardInformativeCardDTO> simpleCardDTO = new List<SimpleDashboardInformativeCardDTO>();
            List<InformativeCardDTO> listCardDTO = new List<InformativeCardDTO>();
            List<WidthCardDTO> widthCardDTO = new List<WidthCardDTO>();

            if (henBatchId.HasValue)
            {
                IQueryable<HenBatchPerformance> henBatchPerformance = henBatchPerformanceService.GetAll()
                                            .Where(hbp => hbp.Date >= dateFrom && hbp.Date <= dateTo)
                                            .Where(hbp => hbp.HenBatchId == henBatchId || hbp.HenBatch.ParentId == henBatchId);

                IQueryable<HenBatch> henBatch = henBatchService.GetAll().Where(hb => hb.Id == henBatchId);
                Guid geneticId = henBatch.Select(hb => hb.GeneticId).FirstOrDefault();
                IQueryable<GeneticsParametersReference> geneticsParameter = geneticsParameterService.GetAll()
                                                                                                    .Where(gp => gp.GeneticsId == geneticId);
                HenStage henStage = henBatch.Select(hb => hb.HenStage).FirstOrDefault();
                // Verificamos si henBatchId tiene valor (no es null) antes de agregarlo a la lista
                if (henBatchId.HasValue)
                {
                    dto.HenBatchIds.Add(henBatchId.Value); // Agregar el valor de henBatchId a la lista
                }
                dto = this.statisticsBusinessLogic.GetCardsHenBatchData(dto, henBatchPerformance, geneticsParameter, henStage);
                simpleCardDTO = dto.SimpleCardDTOs;
                listCardDTO = dto.ListCardDTOs;
                widthCardDTO = dto.WidthCardDTOs;
            }

            if (companyId.HasValue)
            {
                List<Guid> henBatchIds = GetHenBatchesByCompany(companyId.Value);

                List<HenBatchPerformance> henBatchPerformances = new List<HenBatchPerformance>();
                List<GeneticsParametersReference> geneticsParameter = new List<GeneticsParametersReference>();

                foreach (var id in henBatchIds)
                {
                    henBatchPerformances.AddRange(GetHenBatchPerformace(dateFrom, dateTo, id).ToList()); // Convertir IQueryable a List
                    geneticsParameter.AddRange(GetGenetics(id).ToList()
                        .Where(g => !geneticsParameter.Any(existing => existing.Id == g.Id))); // Evita duplicados por ID
                }

                DashboardInformativeCardsDTO informativeDTO = new DashboardInformativeCardsDTO();
                informativeDTO.HenBatchIds.AddRange(henBatchIds); // Agregar todos los IDs
                informativeDTO = this.statisticsBusinessLogic.GetCardsHenBatchData(informativeDTO,
                    henBatchPerformances.AsQueryable(),
                    geneticsParameter.AsQueryable(),
                    HenStage.Laying);
                simpleCardDTO.AddRange(informativeDTO.SimpleCardDTOs);
                listCardDTO.AddRange(informativeDTO.ListCardDTOs);
                widthCardDTO.AddRange(informativeDTO.WidthCardDTOs);
            }

            if (userFilterId.HasValue)
            {
                List<Guid> henBatchIds = GetHenBatchesByUser(userFilterId.Value);

                List<HenBatchPerformance> henBatchPerformances = new List<HenBatchPerformance>();
                List<GeneticsParametersReference> geneticsParameter = new List<GeneticsParametersReference>();

                foreach (var id in henBatchIds)
                {
                    henBatchPerformances.AddRange(GetHenBatchPerformace(dateFrom, dateTo, id).ToList()); // Convertir IQueryable a List
                    geneticsParameter.AddRange(GetGenetics(id).ToList()
                        .Where(g => !geneticsParameter.Any(existing => existing.Id == g.Id))); // Evita duplicados por ID
                }

                DashboardInformativeCardsDTO informativeDTO = new DashboardInformativeCardsDTO();
                informativeDTO.HenBatchIds.AddRange(henBatchIds); // Agregar todos los IDs
                informativeDTO = this.statisticsBusinessLogic.GetCardsHenBatchData(informativeDTO,
                    henBatchPerformances.AsQueryable(),
                    geneticsParameter.AsQueryable(),
                    HenStage.Laying);
                simpleCardDTO.AddRange(informativeDTO.SimpleCardDTOs);
                listCardDTO.AddRange(informativeDTO.ListCardDTOs);
                widthCardDTO.AddRange(informativeDTO.WidthCardDTOs);
            }

            if (userFilterId.HasValue || companyId.HasValue)
            {
                simpleCardDTO = simpleCardDTO
                    .GroupBy(s => s.Title)
                    .Select(sg => new SimpleDashboardInformativeCardDTO
                    {
                        Title = sg.Key,
                        IsSubtitle = sg.First().IsSubtitle,
                        Text = sg.First().Text,
                        SubtitleBold = sg.First().SubtitleBold,
                        SubtitleSimple = sg.First().SubtitleSimple,
                        Value = sg.Sum(s => s.NumberValue) != 0 ? sg.Average(s => s.NumberValue).ToString() : "", //calculo de valor
                        ValueColor = sg.First().ValueColor,
                        Icon = sg.First().Icon,
                        IconColor = sg.First().IconColor,
                        SubtitleBoldColor = sg.First().SubtitleBoldColor,
                        SubtitleSimpleColor = sg.First().SubtitleSimpleColor,
                        IsSimple = sg.First().IsSimple
                    })
                    .ToList();

                listCardDTO = listCardDTO
                    .GroupBy(l => l.Title)
                    .Select(lg => new InformativeCardDTO
                    {
                        Title = lg.Key,
                        InformativeCards = lg.SelectMany(l => l.InformativeCards)
                            .GroupBy(item => new { item.Text })
                            .Select(itemGroup => new InformativeCardListItemDTO
                            {
                                IsSubtitle = itemGroup.First().IsSubtitle,
                                Text = itemGroup.Key.Text,
                                TextColor = itemGroup.First().TextColor,
                                Value = itemGroup.Sum(i => i.NumberValue) > 0 ? (itemGroup.Average(i => i.NumberValue)).ToString("F2") : "", //calculo de valor
                                ValueColor = itemGroup.First().ValueColor,
                                ValueTooltipText = itemGroup.First().ValueTooltipText
                            })
                            .ToList()
                    })
                    .ToList();

                widthCardDTO = widthCardDTO
                    .GroupBy(w => w.Title)
                    .Select(wg => new WidthCardDTO
                    {
                        Title = wg.Key,
                        WidthCards = wg.SelectMany(w => w.WidthCards)
                            .GroupBy(item => item.Text)
                            .Select(itemGroup => new WidthCardListItemDTO
                            {
                                Text = itemGroup.Key,
                                Value = itemGroup.Sum(i => i.NumberValue) > 0 ? (itemGroup.Average(i => i.NumberValue)).ToString("F2") : "", //calculo
                                ValueColor = itemGroup.First().ValueColor,
                                ValueTooltipText = itemGroup.First().ValueTooltipText
                            })
                            .ToList()
                    })
                    .ToList();
            }

            foreach (SimpleDashboardInformativeCardDTO sdi in simpleCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/SimpleDashboardInformativeCard/Default", sdi), 0));

            foreach (InformativeCardDTO icDTO in listCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/ListDashboardInformativeCard/Default", icDTO), 1));

            foreach (WidthCardDTO wDTO in widthCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/WidthDashboardInformativeCard/Default", wDTO), 2));

            return Ok(new
            {
                viewList
            });

        }

        #region Charts
        [HttpPost]
        public IActionResult GetDataForLayingMaleFemaleDistributionChart(FilterDataDTO filters)
        {

            try
            {
                base.ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                return new JsonResult(this.statisticsBusinessLogic.GetDataForMaleFemaleDistributionChart(filters, HenStage.Laying));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }


        [HttpPost]
        public IActionResult GetDataForLayingDashboardMortalityChart(FilterDataDTO filters)
        {
            try
            {
                base.ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                return new JsonResult(this.statisticsBusinessLogic.GetDataForMortalityChart(filters, HenStage.Laying));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpGet]
        public IActionResult GetDataForLayingDashboardCapacityChart()
        {
            return new JsonResult(this.statisticsBusinessLogic.GetDataForCapacityChart(HenStage.Laying));
        }

        [HttpGet]
        public IActionResult GetDataForLayingDashboardGeneticsChart()
        {
            return new JsonResult(this.statisticsBusinessLogic.GetDataForGeneticsChart(HenStage.Laying));
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardHenBatchAgeChart(FilterDataDTO data)
        {
            try
            {
                data.HenStage = HenStage.Laying;
                return new JsonResult(this.statisticsBusinessLogic.GetDataForHenBatchAgeChart(data));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardHenDayAndFeedIntakeChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                ParseDatesFromFilters(filters, ChartTimeSpan.WeekSpan, weeks: true);
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, true);

                return new JsonResult(this.statisticsBusinessLogic.GetDataForHenDayAndFeedIntakeChart(henBatches, filters.MinDate.Value, filters.MaxDate.Value));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardHenBatchPerformanceChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                ParseDatesFromFilters(filters, ChartTimeSpan.WeekSpan, weeks: true);
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, true);

                return new JsonResult(this.statisticsBusinessLogic.GetDataForBatchPerformanceChart(HenStage.Laying, filters.MinDate.Value, filters.MaxDate.Value, henBatches));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardPerformanceChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, false);

                List<List<HenBatchStatusDTO>> data = new List<List<HenBatchStatusDTO>>();
                DashboardLineOrBarChartDTO chartDTO = new DashboardLineOrBarChartDTO();

                (data, chartDTO) = this.statisticsBusinessLogic.GetDataForPerformanceChart(henBatches, filters.MinDate.Value, filters.MaxDate.Value, filters.HenStage);

                return new JsonResult(new { data, chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpGet]
        public IActionResult GetDataForLayingDashboardHenAmountByGroupChart()
        {
            return new JsonResult(this.statisticsBusinessLogic.GetDataForColorAndGeneticHenBatchChart(HenStage.Laying));
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardBirdWeightChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                SampleCageChartsDTO chartDTO = this.statisticsBusinessLogic.GetDataForSampleCageCharts(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardSearaPerformanceCharts(FilterDataDTO filters)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                bool searchParentHenBatches = filters.HenBatchId.HasValue && !filters.WarehouseId.HasValue && !filters.LineId.HasValue;

                List<Guid> henBatchesThatSatisfyOriginalFilter = GetHenBatchIdsFromFilters(filters, parents: searchParentHenBatches);
                PerformanceSearaChartsDTO chartDTO;

                if (filters.LineId.HasValue)
                {
                    filters.LineId = default;
                    List<Guid> henBatchesByWarehouse = GetHenBatchIdsFromFilters(filters, parents: searchParentHenBatches);
                    chartDTO = this.searaChartsBusinessLogic.GetDataForSearaPerformanceChart(HenStage.Laying, henBatchesThatSatisfyOriginalFilter, henBatchesByWarehouse);
                }
                else
                    chartDTO = this.searaChartsBusinessLogic.GetDataForSearaPerformanceChart(HenStage.Laying, henBatchesThatSatisfyOriginalFilter);


                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardContaminatedEggsChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                DashboardLineOrBarChartDTO chartDTO = this.searaChartsBusinessLogic.GetDataForContaminatedEggsChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardPreSortingEggsCharts(FilterDataDTO filters)
        {
            try
            {
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                filters.LineId = (Guid?)null;
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, parents: false);

                DashboardLineOrBarChartDTO chartDTO = this.searaChartsBusinessLogic.GetDataForPreSortedEggsChart(henBatches, filters.HenBatchId.Value);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardEggQualityChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                DashboardLineOrBarChartDTO chartDTO = this.searaChartsBusinessLogic.GetDataForEggQualityChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForLayingDashboardEggDensityChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Laying;

                DashboardLineOrBarChartDTO chartDTO = this.searaChartsBusinessLogic.GetDataForEggDensityChart(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }
        #endregion

        #region Filter lists initialization
        private void InitLists()
        {
            HenStage henStage = HenStage.Laying;

            ContainerFilterDTO items = henBatchBusinessLogic.GetAllContainerFilter(henStage, null, activeBatches: true, orderByNew: true);

            ViewData["Companies"] = GetCompanies();
            var companyIds = companyService.GetAll().Select(c => c.Id).ToList();
            var farmIds = farmService.GetAll().Select(c => c.Id).ToList();
            ViewData["Supervisors"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.SupervisorId, farmIds);
            ViewData["Extensionists"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.TechnicianId, farmIds);
            ViewData["Productors"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.ProductorLayingId, farmIds);

            ViewData["Lines"] = items.Line;
            ViewData["HenBatchesOrderByNew"] = items.ParentHenBatch;
            ViewData["Warehouses"] = items.Warehouse;
            ViewData["Cages"] = items.Cages;
        }

        private List<SelectListItem> GetCompanies()
        {
            return this.companyService.GetAll().OrderBy(c => c.BusinessName)
                .Select(c => new SelectListItem(c.BusinessName, c.Id.ToString())).ToList();
        }

        public List<SelectListItem> GetUsersByCompanyWithProfile(List<Guid> companyIds, Guid? profile, List<Guid> farmIds)
        {
            IQueryable<ApplicationUser> companyUsers = userService.GetAllFull();

            // Filtrar usuarios por compañías
            companyUsers = companyUsers.Where(au => au.Companies == null || !au.Companies.Any() ||
                                                    au.Companies.Any(c => companyIds.Contains(c.CompanyId)));

            // Obtener farmsActiveIds solo si el perfil requiere este filtro
            List<Guid?> farmsActiveIds = new List<Guid?>();
            Guid productorProfileId = Guid.Parse("155F3BD0-E1A9-667B-BB06-3A045F9214FB");
            if (profile == productorProfileId)
            {
                farmsActiveIds = henBatchService.GetAll()
                    .Where(hb => hb.HenStage == HenStage.Laying && hb.HenAmountFemale > 0 && hb.HenAmountMale > 0 && hb.Active)
                    .Where(hb => farmIds.Contains(hb.FarmId.Value))
                    .Select(hb => hb.FarmId).ToList();


                // Filtrar usuarios por farmsActiveIds
                companyUsers = companyUsers.Where(au => au.Sites.Any(c => farmsActiveIds.Contains(c.SiteId)));
            }

            // Filtrar usuarios por perfil de seguridad
            companyUsers = companyUsers.Where(au => au.SecurityProfiles.Any(sp => sp.SecurityProfileId == profile));

            // Excluir usuario de integración
            Guid tenantId = this.operationContext.GetUserTenantId().Value;
            Guid excludedUserId = Guid.Parse("daae7110-08a2-50d7-8149-39fb048e5702"); // Usuario de integración

            return companyUsers.Where(u => u.TenantId == tenantId && u.Id != excludedUserId)
                .Select(u => new SelectListItem(u.Name + " " + u.LastName, u.Id.ToString()))
                .ToList();
        }
        #endregion

        #region refresh inputs
        /// <summary>
        /// Gets farms from a henStage to use in henreport index farm filter.
        /// </summary>
        public List<SelectListItem> GetFarmsByHenStage()
        {
            return henBatchBusinessLogic.GetFarms(HenStage.Laying);
        }

        /// <summary>
        /// Gets clusters from a farm to use in henreport index cluster filter.
        /// </summary>
        public List<SelectListItem> GetClustersByFarm(Guid? farmId)
        {
            return henBatchBusinessLogic.GetClusters(HenStage.Laying, farmId);
        }

        /// <summary>
        /// Gets hen warehouses from a cluster to use in henreport index warehouses filter.
        /// </summary>
        public List<SelectListItem> GetWarehousesByCluster(Guid? clusterId)
        {
            return henBatchBusinessLogic.GetWarehouses(HenStage.Laying, clusterId);
        }

        /// <summary>
        /// Gets lines from a hen warehouse to use in henreport index line filter.
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouse(Guid? warehouseId)
        {
            return henBatchBusinessLogic.GetLines(HenStage.Laying, warehouseId);
        }

        /// <summary>
        /// Gets cages from a line to use in henreport index cage filter.
        /// </summary>
        public List<SelectListItem> GetCagesbyLine(Guid? lineId)
        {
            return henBatchBusinessLogic.GetCages(HenStage.Laying, lineId);
        }

        /// <summary>
        /// Gets all henbatches to use in hen report index henbatch filter
        /// </summary>
        public List<SelectListItem> GetHenBatchesByLine(Guid? lineId)
        {
            return henBatchBusinessLogic.GetHenBatches(HenStage.Laying, lineId, true);
        }

        /// <summary>
        /// Gets all warehouses by selected hen batch
        /// </summary>
        public List<SelectListItem> GetWarehousesByHenBatch(Guid selectedHenBatch)
        {
            return henBatchBusinessLogic.GetWarehousesByHenBatch(selectedHenBatch, HenStage.Laying);
        }

        /// <summary>
        /// Gets lines by selected hen batch and selected warehouse.
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouseAndHenBatch(Guid selectedWarehouse, Guid selectedHenBatch)
        {
            return henBatchBusinessLogic.GetLinesByWarehouseAndHenBatch(selectedWarehouse, selectedHenBatch, HenStage.Laying);
        }

        #endregion refresh inputs

        #region Create Lists

        /// <summary>
        /// Gets the henbatch and its genetic from a line.
        /// </summary>
        public async Task<IActionResult> GetHenbatch(Guid lineId)
        {
            Line line = await this.lineService.GetFullAsync(lineId);
            HenBatch henBatch = line.HenBatches.FirstOrDefault(hb => hb.DateEnd == null);
            return Json(new
            {
                henbatchId = henBatch.Id,
                geneticName = henBatch.Genetic.Name,
                henAmount = henBatch.HenAmountFemale + henBatch.HenAmountMale,
                henbatchName = henBatch.DetailedName
            });
        }

        public IQueryable<HenBatchPerformance> GetHenBatchPerformace(DateTime from, DateTime to, Guid henBatchId)
        {
            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService.GetAll()
                .Where(hbp => hbp.Date >= from && hbp.Date <= to)
                .Where(hbp => hbp.HenBatchId == henBatchId || hbp.HenBatch.ParentId == henBatchId);
            return henBatchPerformances;
        }

        public List<Guid> GetHenBatchesByCompany(Guid companyId)
        {
            List<Guid> henBatches = henBatchService.GetAll()
                .Where(hb => hb.HenStage == HenStage.Laying && hb.CompanyId == companyId && hb.HenAmountFemale > 0 && hb.Active && hb.ParentId == null)
                .Select(hb => hb.Id)
                .ToList();

            return henBatches;
        }

        public List<Guid> GetHenBatchesByUser(Guid userFilterId)
        {
            ApplicationUser user = userService.GetAllFull().FirstOrDefault(u => u.Id == userFilterId);
            List<Guid> farms = user.Sites.Select(s => s.SiteId).ToList();

            List<Guid> henBatches = henBatchService.GetAll()
               .Where(hb => hb.HenStage == HenStage.Breeding && hb.HenAmountFemale > 0 && hb.Active && hb.ParentId == null)
               .Where(hb => farms.Contains(hb.FarmId.Value))
               .Select(hb => hb.Id).ToList();

            return henBatches;
        }
        #endregion

        #region GetAllUsers
        private string GetAllUsers(Guid userId)
        {
            List<SelectListItem> users = userService.GetAll(asNoTracking: true, filterByTenant: true).Where(u => u.Id != userId).OrderBy(u => u.LastName).Select(u => new SelectListItem { Value = u.Id.ToString(), Text = u.LastName + ", " + u.Name }).ToList();
            string usershtml = "<select id = 'dropd' class='form-control' multiple='multiple'>";
            foreach (SelectListItem user in users)
            {
                usershtml = usershtml + "<option id=" + user.Value + ">" + user.Text + "</option>";
            }
            usershtml += "</select>";

            return usershtml;
        }
        #endregion

        public IQueryable<GeneticsParametersReference> GetGenetics(Guid henBatchId)
        {
            IQueryable<GeneticsParametersReference> genetics = geneticsParameterService.GetAll().Include(g => g.Genetics).Where(g => g.Genetics.HenBatch.Any(hb => hb.Id == henBatchId));
            return genetics;
        }
    }
}