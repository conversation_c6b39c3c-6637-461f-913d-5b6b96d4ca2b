// Tooltip formatter to show the percentage that represents the difference between the real weight and STD
var dashboardBirdWeightChartToolTipFormatter = function () {
  // Tooltip header in bold text showing the XAxis label
  // followed by the exact point(e.g.: "Week of year 19:")

  //WARN: In some cases the property is defined (renderTo) but has no value (undefined)
  const birdWeightChart = Highcharts.charts.find(
    (obj) => obj !== undefined && obj.renderTo?.id == "BirdWeight"
  );
  const birdWeightChartSeries = birdWeightChart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  // Build body of tooltip. Each line displays the series name followed
  // by the value and units if given in bold text
  var body = "";
  // currentIndex is the index selected
  // STDSeriesIndex is the index of the curve that represents the standard value
  let currentIndex = this.series.index;
  let STDSeriesIndex = currentIndex % 2;
  // loop through each series
  for (var i = 0; i < birdWeightChartSeries.length; i++) {
    // show only points different than zero, stdValue and realValue
    if (
      (STDSeriesIndex == i || STDSeriesIndex + 2 == i) &&
      birdWeightChartSeries[i].points[this.point.x].y !== 0
    ) {
      var birdWeightSuffix = "";
      const currentNumberFormatter =
        birdWeightChartSeries[i].chart.numberFormatter;
      let currentValueBirdWeight =
        birdWeightChartSeries[i].points[this.point.x].y;
      // suffix is added only to actual values
      if (i != 0 && i != 1) {
        // the series 0 and 1 are the std
        var femaleWeightSTD = birdWeightChartSeries[0].points[this.point.x].y;
        var maleWeightSTD = birdWeightChartSeries[1].points[this.point.x].y;

        let associatedIndex =
          currentIndex == 0 || currentIndex == 1
            ? currentIndex + 2
            : currentIndex;
        var birdWeightRealValue =
          birdWeightChartSeries[associatedIndex].points[this.point.x].y;

        var percentage =
          currentIndex % 2 == 0
            ? (birdWeightRealValue * 100) / femaleWeightSTD
            : (birdWeightRealValue * 100) / maleWeightSTD;
        let percentageDifference =
          percentage !== Infinity ? (100 - percentage) * -1 : 100;

        birdWeightSuffix =
          percentageDifference >= 0
            ? " (+" + currentNumberFormatter(percentageDifference) + "%)"
            : " (" + currentNumberFormatter(percentageDifference) + "%)";
      }

      // values are rounded to no decimals
      body +=
        '<span style="color:' +
        birdWeightChartSeries[i].color +
        '">\u25CF</span> ' +
        birdWeightChartSeries[i].name +
        ": <b>" +
        birdWeightChartSeries[i].chart.numberFormatter(
          currentValueBirdWeight,
          2
        ) +
        " " +
        birdWeightChartSeries[i].userOptions.toolTip.valueSuffix +
        birdWeightSuffix +
        "</b><br/>";
    }
  }
  // Append body to header and return
  return header + body;
};

const dashboardBirdWeightChartPlotOptions = {
  spline: {
    dashStyle: "Solid",
  },
  series: {
    dashStyle: "ShortDot",
    lineWidth: 2,
    shadow: true,
    marker: {
      enabled: false,
      symbol: "circle",
      states: {
        hover: {
          enabled: true,
        },
      },
    },
  },
};

// Tooltip formatter to show the percentage that represents the difference between the real uniformity and STD, real cv and STD
var dashboardUniformityChartToolTipFormatter = function () {
  const chart = Highcharts.charts.find(
    (obj) => obj !== undefined && obj.renderTo?.id == "Uniformity"
  );
  const chartSeries = chart.series;
  var header =
    "<b>" + this.series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

  let body = "";
  let suffix = "";
  let currentIndex = this.series.index;

  const indexOfTooltip = new Map();
  indexOfTooltip.set(1, 4);
  indexOfTooltip.set(2, 5);
  indexOfTooltip.set(4, 1);
  indexOfTooltip.set(5, 2);

  const currentNumberFormatter =
    chartSeries[currentIndex].chart.numberFormatter;

  const greenColor = "#28B463";
  const redColor = "#E74C3C";

  let currentValue = chartSeries[currentIndex].points[this.point.x].y;
  if (currentValue !== 0) {
    // show only points stdValue
    if (currentIndex == 0 || currentIndex == 3) {
      body +=
        '<span style="color:' +
        chartSeries[currentIndex].color +
        '">\u25CF</span> ' +
        chartSeries[currentIndex].name +
        ": <b>" +
        chartSeries[currentIndex].chart.numberFormatter(currentValue, 2) +
        " " +
        chartSeries[currentIndex].userOptions.toolTip.valueSuffix +
        suffix +
        "</b><br/>";
    }
    // show only points realValue
    if (currentIndex != 0 && currentIndex != 3) {
      const otherIndexToShow = indexOfTooltip.get(currentIndex);
      let uniformityIndex;
      let cvIndex;
      if (currentIndex < otherIndexToShow) {
        uniformityIndex = currentIndex;
        cvIndex = otherIndexToShow;
      } else {
        uniformityIndex = otherIndexToShow;
        cvIndex = currentIndex;
      }
      // serie 0 is the uniformity std
      let uniformitySTD = chartSeries[0].points[this.point.x].y;
      let uniformityRealValue =
        chartSeries[uniformityIndex].points[this.point.x].y;
      // serie 3 is the cv std
      let cvSTD = chartSeries[3].points[this.point.x].y;
      let cvRealValue = chartSeries[cvIndex]?.points[this.point.x]?.y || 0;

      // Uniformity Tooltip
      let percentage =
        uniformitySTD > 0 ? (uniformityRealValue * 100) / uniformitySTD : 0;
      let percentageDifference =
        percentage != 0 ? (100 - percentage) * -1 : 100;

      suffix =
        percentageDifference >= 0
          ? " (+" + currentNumberFormatter(percentageDifference) + ")"
          : " (" + currentNumberFormatter(percentageDifference) + ")";

      let percentageColor = greenColor;
      if (percentageDifference < 0) {
        percentageColor = redColor;
      }

      body +=
        '<span style="color:' +
        chartSeries[uniformityIndex].color +
        '">\u25CF</span> ' +
        chartSeries[uniformityIndex].name +
        ' (%): <span style="color:' +
        percentageColor +
        ';font-weight:bold">' +
        chartSeries[uniformityIndex].chart.numberFormatter(
          uniformityRealValue,
          2
        ) +
        " " +
        chartSeries[uniformityIndex].userOptions.toolTip.valueSuffix +
        suffix +
        "</span><br/>";

      // CV Tooltip
      percentage = cvSTD > 0 ? (cvRealValue * 100) / cvSTD : 0;
      percentageDifference = percentage != 0 ? (100 - percentage) * -1 : 100;

      suffix =
        percentageDifference >= 0
          ? " (+" + currentNumberFormatter(percentageDifference) + ")"
          : " (" + currentNumberFormatter(percentageDifference) + ")";

      percentageColor = redColor;
      if (percentageDifference <= 0) {
        percentageColor = greenColor;
      }

      if (chartSeries[cvIndex]) {
        body +=
          '<span style="color:' +
          chartSeries[cvIndex].color +
          '">\u25CF</span> ' +
          chartSeries[cvIndex].name +
          ' (%): <span style="color:' +
          percentageColor +
          ';font-weight:bold">' +
          chartSeries[cvIndex].chart.numberFormatter(cvRealValue, 2) +
          " " +
          chartSeries[cvIndex].userOptions.toolTip.valueSuffix +
          suffix +
          "</span><br/>";
      }
    }
  }
  // Append body to header and return
  return header + body;
};

const dashboardUniformityChartPlotOptions = {
  spline: {
    dashStyle: "Solid",
    marker: {
      enabled: true,
      symbol: "circle",
    },
  },
  series: {
    dashStyle: "ShortDot",
    lineWidth: 2,
    shadow: true,
    marker: {
      enabled: false,
      symbol: "circle",
      states: {
        hover: {
          enabled: true,
        },
      },
    },
  },
};
