// Tooltip formatter to show percentage current egg type
var dashboardPerformanceChartToolTipFormatter = function () {
    // Tooltip header in bold text showing the XAxis label
    // followed by the exact point(e.g.: "Week of year 19:")
    var header = "<b>" + this.points[0].series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

    // Build body of tooltip. Each line displays the series name followed
    // by the value and units if given in bold text
    var body = "";
    // loop through each series
    for (var i = 0; i < this.points.length; i++) {
        // show points that are not null or undefined
        if (this.points[i].y !== null && this.points[i].y !== undefined) {
            var eggsSuffix = "";
            if ((this.points[i].series.index == 0 || this.points[i].series.index == 1 || this.points[i].series.index == 2) && area == "Laying") {
                const currentNumberFormatter = this.points[i].point.series.chart.numberFormatter;
                const performanceChart = Highcharts.charts.find(obj => obj !== undefined && obj.renderTo.id == "Performance");
                var hatchableEggs = performanceChart.series[0].points[this.points[i].point.x].y;
                var commercialEggs = performanceChart.series[1].points[this.points[i].point.x].y;
                var brokenEggs = performanceChart.series[2].points[this.points[i].point.x].y;
                var percentageCurrentEggType = this.points[i].y / (hatchableEggs + commercialEggs + brokenEggs);

                eggsSuffix = " (" + currentNumberFormatter(Math.round(percentageCurrentEggType * 100)) + "%)";
            }

            // values are rounded to no decimals
            body += '<span style="color:' + this.points[i].color + '">\u25CF</span> ' + this.points[i].series.name + ": <b>" + this.points[i].series.chart.numberFormatter(this.points[i].y, 2) + " " + this.points[i].point.series.userOptions.toolTip.valueSuffix + eggsSuffix + "</b><br/>";
        }
    }

    // Append body to header and return
    return header + body;    
}
