using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.Entities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Excel;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Shaper.Entities.Draft;
using Binit.Shaper.Interfaces.Services;
using DAL.Interfaces;
using Domain.Entities.DTOs;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Entities.Model.Views;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.HenReportDTOs;
using Domain.Logic.BusinessLogic.HenReportBusinessLogic.ShippingNoteSteps;
using Domain.Logic.DTOs.HenBatchDTOs;
using Domain.Logic.DTOs.HenReportDTOs;
using Domain.Logic.Helpers;
using Domain.Logic.Interfaces;
using Domain.Logic.Validations;
using Domain.Logic.Validations.BusinessLogicValidations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Domain.Logic.Helpers.SearaExcelHelper;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.HenReportBusinessLogic;
using DevExpress.CodeParser;
using HenBatchLang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.HenBatchBusinessLogic;
using DevExpress.PivotGrid.DataCalculation;
using Microsoft.Extensions;
using Domain.Entities.Constants.ReportPlanner;

namespace Domain.Logic.BusinessLogic.HenReportBusinessLogic
{
    public class HenReportBusinessLogic : IHenReportBusinessLogic
    {
        #region Properties
        private readonly IUnitOfWork unitOfWork;
        private readonly IHenBatchService henBatchService;
        private readonly IHenReportService henReportService;
        private readonly IHenWarehouseService henWarehouseService;
        private readonly IHenWarehouseBusinessLogic henWarehouseBusinessLogic;
        private readonly IClusterService clusterService;
        private readonly ILineService lineService;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IServiceProvider serviceProvider;
        private readonly IInconsistencyReportService inconsistencyReportService;
        private readonly IExceptionManager exceptionManager;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IExcelExportBusinessLogic excelExportBusinessLogic;
        private readonly IShippingNoteConciliationService shippingNoteConciliationService;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IOperationContext operationContext;
        private readonly IContainerService<Container> containerService;
        private readonly IHenBatchPerformanceBusinessLogic henBatchPerformanceBusinessLogic;
        private readonly IServiceTenantDependent<ReportRectification> reportRectificationService;
        private readonly IDraftExtensionService draftService;
        private readonly IMaterialService materialService;
        private readonly DeathQuantitiesConstants deathQuantitiesConstants;
        private readonly ICasualtyReasonService casualtyReasonService;
        private readonly IDepopulationReasonService depopulationReasonService;
        private readonly IShippingNoteService shippingNoteService;
        private readonly IMaterialReceptionReportBusinessLogic materialReceptionReportBusinessLogic;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;
        private readonly IServiceTenantDependent<HenReportBulkLoadEvent> eventService;
        private readonly IConfiguration configuration;
        private readonly IExternalServiceService externalServiceService;
        private readonly IFileManagerService fileManagerService;
        private readonly IService<IgniteFile> igniteFileService;
        private readonly IServiceTenantDependent<TenantDependentEntityFile> fileService;
        private readonly BusinessValidationManager<HenReport> businessValidationManager;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IUserService<ApplicationUser> userService;
        private readonly IFarmService farmService;
        private readonly IReportPlannerProgramService reportPlannerProgramService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        #endregion

        #region Constructor
        public HenReportBusinessLogic(
            IHenBatchService henBatchService,
            IHenReportService henReportService,
            ILineService lineService,
            IHenWarehouseService henWarehouseService,
            IHenWarehouseBusinessLogic henWarehouseBusinessLogic,
            IClusterService clusterService,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IServiceProvider serviceProvider,
            IInconsistencyReportService inconsistencyReportService,
            IExceptionManager exceptionManager,
            IStringLocalizer<SharedResources> localizer,
            IShippingNoteConciliationService shippingNoteConciliationService,
            IService<TenantConfiguration> tenantConfigurationService,
            IUnitOfWork unitOfWork,
            IOperationContext operationContext,
            IExcelExportBusinessLogic excelExportBusinessLogic,
            IContainerService<Container> containerService,
            IHenBatchPerformanceBusinessLogic henBatchPerformanceBusinessLogic,
            IMaterialService materialService,
            IServiceTenantDependent<ReportRectification> reportRectificationService,
            IDraftExtensionService draftService,
            ICasualtyReasonService casualtyReasonService,
            IDepopulationReasonService depopulationReasonService,
            IShippingNoteService shippingNoteService,
            IMaterialReceptionReportBusinessLogic materialReceptionReportBusinessLogic,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IServiceTenantDependent<HenReportBulkLoadEvent> eventService,
            IConfiguration configuration,
            IExternalServiceService externalServiceService,
            IFileManagerService fileManagerService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IService<IgniteFile> igniteFileService,
            BusinessValidationManager<HenReport> businessValidationManager,
            IGeneticsParameterService geneticsParameterService,
            IUserService<ApplicationUser> userService,
            IFarmService farmService,
            IReportPlannerProgramService reportPlannerProgramService,
            IHenBatchPerformanceService henBatchPerformanceService)
        {
            this.henBatchService = henBatchService;
            this.henReportService = henReportService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.lineService = lineService;
            this.henWarehouseService = henWarehouseService;
            this.henWarehouseBusinessLogic = henWarehouseBusinessLogic;
            this.clusterService = clusterService;
            this.serviceProvider = serviceProvider;
            this.unitOfWork = unitOfWork;
            this.inconsistencyReportService = inconsistencyReportService;
            this.exceptionManager = exceptionManager;
            this.localizer = localizer;
            this.excelExportBusinessLogic = excelExportBusinessLogic;
            this.shippingNoteConciliationService = shippingNoteConciliationService;
            this.tenantConfigurationService = tenantConfigurationService;
            this.operationContext = operationContext;
            this.containerService = containerService;
            this.henBatchPerformanceBusinessLogic = henBatchPerformanceBusinessLogic;
            this.materialService = materialService;
            this.draftService = draftService;
            this.reportRectificationService = reportRectificationService;
            this.casualtyReasonService = casualtyReasonService;
            this.deathQuantitiesConstants = new DeathQuantitiesConstants(this.localizer);
            this.depopulationReasonService = depopulationReasonService;
            this.shippingNoteService = shippingNoteService;
            this.materialReceptionReportBusinessLogic = materialReceptionReportBusinessLogic;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
            this.eventService = eventService;
            this.configuration = configuration;
            this.externalServiceService = externalServiceService;
            this.fileManagerService = fileManagerService;
            this.igniteFileService = igniteFileService;
            this.fileService = fileService;
            this.businessValidationManager = businessValidationManager;
            this.geneticsParameterService = geneticsParameterService;
            this.userService = userService;
            this.farmService = farmService;
            this.reportPlannerProgramService = reportPlannerProgramService;
            this.henBatchPerformanceService = henBatchPerformanceService;
        }
        #endregion

        /// <summary>
        /// GetAll hen batches with relationships and extra data to build data grid rows
        /// </summary>
        public IQueryable<HenReportGridView> GetAllForIndex(Dictionary<string, string> filters)
        {
            filters.TryGetValue("henStage", out string filterValue);
            HenStage henStage = EnumHelper<HenStage>.Parse(filterValue);

            IQueryable<HenReportGridView> henReports = henReportService.GetAllView()
                .Where(hr => hr.HenBatchHenStage == henStage && hr.ReportEnum == ReportEnum.New);

            return henReports;
        }

        /// <summary>
        /// Checks if a henreport has any pending adjustments
        /// </summary>
        public bool HasPendingReports(Guid id)
        {
            IQueryable<ReportRectification> rectificationReport = reportRectificationService.GetAll()
                                                                                      .Where(rr => rr.RectificationHenReportId == id && rr.AuthorizationResponseEnum == AuthorizationResponseEnum.Pending);

            if (rectificationReport.Any())
                return true;
            else
                return false;
        }

        /// <summary>
        /// Creates a hen report, fetch a hen batch performance for the same day and hen batch,
        /// creates it if it does not exist and add the values of the report to it.
        /// </summary>
        public async Task CreateAsync(HenReport henReport, HenReportFeedIntakeDTO feedIntakeDTO)
        {
            //To make sure all changes are saved at the end when all validations are passed.
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                await CreateAndAddToHenBatchPerformanceAsync(henReport, feedIntakeDTO);

                HenBatch henBatch = await henBatchService.GetAll().Include(hb => hb.Line).Where(hb => hb.Id == henReport.HenBatchId).FirstAsync();
                await henWarehouseBusinessLogic.UpdateWarehouseConsumption(henBatch.Line.WarehouseId);
            });
        }

        private void ValidateFeedIntakeOrigins(Guid? feedIntakeOriginId)
        {
            if (containerService.GetAllIgnoringClaims(true)
                        .Where(c => c.Id == feedIntakeOriginId)
                            .Any(c => c.OriginInconsistencyReports.Any(oir => oir.Status == InconsistencyReportStatusEnum.PendingReview) ||
                                      c.DestinationInconsistencyReports.Any(dir => dir.Status == InconsistencyReportStatusEnum.PendingReview)))

                throw new ValidationException(null, new BusinessValidationResult<ShippingNote>(localizer[Lang.PendingInconsistencyReviews]).UnsuccessfulValidations);
        }

        private HenReportBaseStep GetFeedIntakeStep(Guid? feedIntakeOriginId, Guid? materialBatchConsumedId, decimal quantity, string stepName)
        {
            Container feedIntakeOrigin = containerService.GetAllIgnoringClaims(true).FirstOrDefault(c => c.Id == feedIntakeOriginId.Value);

            if (feedIntakeOrigin == null) throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);

            MaterialBatchContainer materialBatch = containerService.GetAvailableMaterialBatches(feedIntakeOrigin.Id).FirstOrDefault(mb => mb.MaterialBatchId == materialBatchConsumedId);

            return GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatch: materialBatch, feedIntake: quantity, ConsumedFoodStepName: stepName);
        }

        private HenReportBaseStep GetFeedIntakeStepDifferentMaterialBatch(Guid? feedIntakeOriginId, Guid? materialBatchFemaleConsumedId, Guid? materialBatchMaleConsumedId, decimal quantityFemale, decimal quantityMale, string stepName)
        {
            Container feedIntakeOrigin = containerService.GetAllIgnoringClaims().FirstOrDefault(c => c.Id == feedIntakeOriginId.Value);

            if (feedIntakeOrigin == null)
                throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);

            MaterialBatchContainer materialBatchFemale = materialBatchFemaleConsumedId.HasValue
                ? containerService.GetAvailableMaterialBatches(feedIntakeOrigin.Id).First(mb => mb.MaterialBatchId == materialBatchFemaleConsumedId.Value)
                : null;

            MaterialBatchContainer materialBatchMale = materialBatchMaleConsumedId.HasValue
                ? containerService.GetAvailableMaterialBatches(feedIntakeOrigin.Id).First(mb => mb.MaterialBatchId == materialBatchMaleConsumedId.Value)
                : null;

            ConsumedFoodStep step = GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatchFemale: materialBatchFemale, materialBatchMale: materialBatchMale, feedIntakeFemale: quantityFemale, feedIntakeMale: quantityMale, ConsumedFoodStepName: stepName);

            return step;
        }

        /// <summary>
        /// Creates a hen report, fetch a hen batch performance for the same day and hen batch,
        /// creates it if it does not exist and add the values of the report to it.
        /// </summary>
        private async Task CreateAndAddToHenBatchPerformanceAsync(HenReport henReport, HenReportFeedIntakeDTO feedIntakeDTO)
        {
            HenBatch henBatch = await henBatchService.GetAll().FirstOrDefaultAsync(hb => hb.Id == henReport.HenBatchId);

            if (henBatch == default) throw new ValidationException(localizer[Lang.HenBatchNotFound]);

            int week = henBatchService.GetCurrentWeekNumberForDate(henBatch.Id, henReport.Date);

            List<HenReportBaseStep> fixture = new List<HenReportBaseStep>();

            if (henBatch.HenStage == HenStage.Laying && henReport.ClassifiedEggs.Any(e => e.Quantity > 0))
                HandleLaying(henReport, fixture);

            if (henReport.DeadFemale != 0 || henReport.DepopulateFemale != 0 || henReport.DeadMale != 0 || henReport.DepopulateMale != 0)
                fixture.Add(GetStep<RemoveHensStep>(totalDeadFemale: henReport.DeadFemale + henReport.DepopulateFemale, totalDeadMale: henReport.DepopulateMale + henReport.DeadMale));

            if (feedIntakeDTO != null)
            {
                // Get feed intake step according to the origins, if feed intake famele and male shared the same
                //origin only add one step, if not add two
                if (feedIntakeDTO.FeedIntakeOriginId.HasValue && (henReport.FeedIntakeFemale + henReport.FeedIntakeMale) > 0 && feedIntakeDTO.MaterialBatchConsumedId.HasValue)
                {
                    // Validates origin has no pending inconsistencies
                    ValidateFeedIntakeOrigins(feedIntakeDTO.FeedIntakeOriginId);
                    fixture.Add(GetFeedIntakeStep(feedIntakeDTO.FeedIntakeOriginId, feedIntakeDTO.MaterialBatchConsumedId, henReport.FeedIntakeFemale + henReport.FeedIntakeMale, HenReportSteps.ConsumedFoodStep));
                }

                else if (feedIntakeDTO.FeedIntakeOriginId.HasValue && (henReport.FeedIntakeMale > 0 || henReport.FeedIntakeFemale > 0) && !feedIntakeDTO.MaterialBatchConsumedId.HasValue)
                {
                    // Validates origin has no pending inconsistencies
                    ValidateFeedIntakeOrigins(feedIntakeDTO.FeedIntakeOriginId);
                    fixture.Add(GetFeedIntakeStepDifferentMaterialBatch(feedIntakeDTO.FeedIntakeOriginId, feedIntakeDTO.MaterialBatchFemaleConsumedId,
                                                                        feedIntakeDTO.MaterialBatchMaleConsumedId, henReport.FeedIntakeFemale,
                                                                        henReport.FeedIntakeMale, HenReportSteps.ConsumedFoodStep));
                }

                else if (feedIntakeDTO.FeedIntakeFemaleOriginId.HasValue || feedIntakeDTO.FeedIntakeMaleOriginId.HasValue)
                {
                    if (feedIntakeDTO.FeedIntakeFemaleOriginId.HasValue && henReport.FeedIntakeFemale > 0)
                    {
                        ValidateFeedIntakeOrigins(feedIntakeDTO.FeedIntakeFemaleOriginId);
                        fixture.Add(GetFeedIntakeStep(feedIntakeDTO.FeedIntakeFemaleOriginId, feedIntakeDTO.MaterialBatchFemaleConsumedId, henReport.FeedIntakeFemale, HenReportSteps.ConsumedFoodFemaleStep));
                    }
                    if (feedIntakeDTO.FeedIntakeMaleOriginId.HasValue && henReport.FeedIntakeMale > 0)
                    {
                        ValidateFeedIntakeOrigins(feedIntakeDTO.FeedIntakeMaleOriginId);
                        fixture.Add(GetFeedIntakeStep(feedIntakeDTO.FeedIntakeMaleOriginId, feedIntakeDTO.MaterialBatchMaleConsumedId, henReport.FeedIntakeMale, HenReportSteps.ConsumedFoodMaleStep));
                    }
                }
            }
            else if ((henReport.FeedIntakeFemale + henReport.FeedIntakeMale) > 0)
            {
                Container feedIntakeOrigin = GetFeedIntakeOrigins(henBatch.Id).FirstOrDefault();

                if (feedIntakeOrigin == null)
                    throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);

                fixture.Add(GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, feedIntake: henReport.FeedIntakeFemale + henReport.FeedIntakeMale));
            }

            henReport.ShippingNotes = new List<ShippingNote>();

            await ExecuteSteps(henReport, henBatch, null, fixture, true);

            henReport.HenBatchPerformanceId = await henBatchPerformanceBusinessLogic.CreateOrUpdateHenBatchPerformance(henReport);

            henReport.ReportEnum = ReportEnum.New;
            henReport.HenAmountFemale = henBatch.HenAmountFemale;
            henReport.HenAmountMale = henBatch.HenAmountMale;

            // set sector, farm and company id for filtering
            henReport.CompanyId = henBatch.CompanyId;
            henReport.FarmId = henBatch.FarmId;
            henReport.SectorId = henBatch.SectorId;

            await henReportService.CreateAsync(henReport);

            // update henbatch values
            henBatch.DeadAccumulatedFemale += henReport.DeadFemale;
            henBatch.DeadAccumulatedMale += henReport.DeadMale;
            henBatch.DepopulateAccumulatedFemale += henReport.DepopulateFemale;
            henBatch.DepopulateAccumulatedMale += henReport.DepopulateMale;

            henBatch.HenReportCreationMinDate = henReport.Date;
            await henBatchService.UpdateAsync(henBatch);

            // Update parent henbatch values.
            if (henBatch.ParentId.HasValue)
            {
                HenBatch parent = await henBatchService.GetAsync(henBatch.ParentId.Value);

                parent.DeadAccumulatedFemale += henReport.DeadFemale;
                parent.DeadAccumulatedMale += henReport.DeadMale;
                parent.DepopulateAccumulatedFemale += henReport.DepopulateFemale;
                parent.DepopulateAccumulatedMale += henReport.DepopulateMale;

                await henBatchService.UpdateAsync(parent);

            }
        }

        private async Task ExecuteSteps(HenReport henReport, HenBatch henBatch, HenReport previousHenReport, List<HenReportBaseStep> fixture, bool createAndApprove)
        {
            foreach (HenReportBaseStep step in fixture)
            {
                (BusinessValidationResult<ShippingNote> validationResult, ShippingNote shippingNote) = await step.ProcessShippingNote(henBatch, henReport, previousHenReport, !createAndApprove);
                if (!validationResult.IsValid) throw new ValidationException(null, validationResult.UnsuccessfulValidations);

                if (shippingNote != null) henReport.ShippingNotes.Add(shippingNote);
            }
        }

        private void HandleLaying(HenReport henReport, List<HenReportBaseStep> fixture)
        {
            // check tenant configuration
            Guid? tenantId = operationContext.GetUserTenantId();
            IQueryable<TenantConfiguration> tenantConfigurations = tenantConfigurationService.GetAll(true).Where(tc => tc.TenantId == tenantId);

            // shipping note conciliation, according to egg type
            List<HenReportEggsDTO> EggsDTO = new List<HenReportEggsDTO>();
            henReport.ShippingNoteConciliations = new List<HenReportConciliation>();
            // it is necessary to set the material type, in order to clasify the eggs
            var materialTypes = materialService.GetAll().Select(m => new
            {
                MaterialId = m.Id,
                MaterialTypePath = m.MaterialType.Path
            });
            foreach (HenReportClassifiedEgg classifiedEgg in henReport.ClassifiedEggs)
            {
                (List<HenReportConciliation> shippingNoteConciliations, int remainingEggs) = ConciliateShippingNotes(henReport.HenBatchId, classifiedEgg.MaterialId, classifiedEgg.Quantity);
                henReport.ShippingNoteConciliations.AddRange(shippingNoteConciliations);
                if (remainingEggs > 0)
                    EggsDTO.Add(new HenReportEggsDTO()
                    {
                        EggId = classifiedEgg.MaterialId.ToString(),
                        Quantity = remainingEggs,
                        MaterialTypePath = materialTypes.FirstOrDefault(mt => mt.MaterialId == classifiedEgg.MaterialId).MaterialTypePath
                    });
            }
            bool groupLayingEggsByMaterialTypeCommercial = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeCommercial && t.Value == "True");
            bool groupLayingEggsByMaterialTypeHatching = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeHatching && t.Value == "True");
            // send eggs from the hen batch to the line
            fixture.Add(GetStep<EggsToLineStep>(EggsDTO: EggsDTO, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));

            // see if its necessary to send the eggs to the storage warehouse
            bool automaticallySentEggs = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs && t.Value == "True");

            if (automaticallySentEggs)
                fixture.Add(GetStep<SendEggsStep>(EggsDTO: EggsDTO, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));
        }

        /// <summary>
        ///  sees if there is any conciliation shipping note remaining for this batch and relates it
        /// </summary>
        private (List<HenReportConciliation> shippingNoteConciliations, int remainingEggs) ConciliateShippingNotes(Guid henBatchId, Guid eggId, int quantity)
        {
            List<HenReportConciliation> shippingNoteConciliations = new List<HenReportConciliation>();
            IQueryable<ShippingNoteConciliation> conciliations = shippingNoteConciliationService.GetWithShippingNotes().Where(s => s.MaterialId == eggId && s.RemainingQuantity > 0 && s.ShippingNote.OriginId == henBatchId);

            foreach (ShippingNoteConciliation sn in conciliations)
            {
                if (quantity > 0)
                {
                    double conciledQuantity = 0;
                    if (quantity >= sn.RemainingQuantity)
                    {
                        conciledQuantity = sn.RemainingQuantity;
                        quantity -= Convert.ToInt32(sn.RemainingQuantity);
                        sn.RemainingQuantity = 0;
                    }
                    else
                    {
                        conciledQuantity = quantity;
                        sn.RemainingQuantity -= quantity;
                        quantity = 0;
                    }
                    shippingNoteConciliations.Add(new HenReportConciliation()
                    {
                        ShippingNoteConciliation = sn,
                        ConciledQuantity = conciledQuantity
                    });
                }
            }
            return (shippingNoteConciliations, quantity);
        }

        public TStep GetStep<TStep>(Container destination = null, Formula formula = null,
            Container feedIntakeOrigin = null, MaterialBatchContainer materialBatch = null,
            List<HenReportEggsDTO> EggsDTO = null, MaterialBatchContainer materialBatchFemale = null,
            MaterialBatchContainer materialBatchMale = null, decimal? feedIntake = 0, decimal? feedIntakeFemale = 0,
            decimal? feedIntakeMale = 0, int totalDeadFemale = 0, int totalDeadMale = 0, bool groupLayingEggsByMaterialTypeHatching = false,
             bool groupLayingEggsByMaterialTypeCommercial = false, string ConsumedFoodStepName = "") where TStep : HenReportBaseStep
        {
            TStep step = serviceProvider.GetService<TStep>();
            if (destination != null)
                step.SetOptionalDestination(destination);
            if (formula != null)
                step.SetFeedIntakeOrigin(formula);
            if (feedIntakeOrigin != null)
                step.SetFeedIntakeOrigin(feedIntakeOrigin);
            if (materialBatch != null)
                step.SetMaterialBatchConsumed(materialBatch);
            if (materialBatchFemale != null)
                step.SetMaterialBatchFemaleConsumed(materialBatchFemale);
            if (materialBatchMale != null)
                step.SetMaterialBatchMaleConsumed(materialBatchMale);
            if (!string.IsNullOrEmpty(ConsumedFoodStepName))
                step.SetFoodConsumedStepName(ConsumedFoodStepName);
            if (EggsDTO != null)
                step.SetEggsQuantity(EggsDTO);
            if (feedIntake.Value != 0)
                step.SetFeedIntake(feedIntake.Value);
            if (feedIntakeFemale.Value != 0)
                step.SetFeedIntakeFemale(feedIntakeFemale.Value);
            if (feedIntakeMale.Value != 0)
                step.SetFeedIntakeMale(feedIntakeMale.Value);
            step.SetDeadHensQuantities(totalDeadFemale, totalDeadMale);
            step.SetEggsBool(groupLayingEggsByMaterialTypeHatching, groupLayingEggsByMaterialTypeCommercial);

            return step;
        }

        /// <summary>
        /// Deletes a hen report, removes it from the HenBatchPerformance,
        /// and updates the values from the HenBatchPerformance
        /// </summary>
        private async Task DeleteAndRemoveFromHenBatchPerformanceAsync(Guid id, bool createAndApprove)
        {
            HenReport henReport = henReportService.GetAll()
                .Include(hr => hr.ShippingNotes)
                .Include(hr => hr.ClassifiedEggs)
                .Include(hr => hr.ShippingNoteConciliations)
                .First(hr => hr.Id == id);

            HenBatch henBatch = henBatchService.Get(henReport.HenBatchId);

            int week = henBatchService.GetCurrentWeekNumberForDate(henBatch.Id, henReport.Date);

            List<HenReportBaseStep> fixture = new List<HenReportBaseStep>();

            if (henBatch.HenStage == HenStage.Laying && henReport.ClassifiedEggs.Any())
                RevertEggsClassification(henReport, fixture);

            // step to return dead hens
            fixture.Add(GetStep<ReturnHensStep>(totalDeadFemale: henReport.DepopulateFemale + henReport.DeadFemale, totalDeadMale: henReport.DeadMale + henReport.DepopulateMale));

            //step to return food
            if (henReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodStep && sn.MaterialsShipped.Count == 1) && (henReport.FeedIntakeFemale + henReport.FeedIntakeMale) > 0)
                fixture.Add(GetStep<ReturnFoodStep>(feedIntake: henReport.FeedIntakeFemale + henReport.FeedIntakeMale, ConsumedFoodStepName: HenReportSteps.ConsumedFoodStep));

            else if (henReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodStep) && (henReport.FeedIntakeFemale > 0 || henReport.FeedIntakeMale > 0))
            {
                if (henReport.FeedIntakeFemale > 0)
                    fixture.Add(GetStep<ReturnFoodStep>(feedIntake: henReport.FeedIntakeFemale, ConsumedFoodStepName: HenReportSteps.ConsumedFoodStep));

                if (henReport.FeedIntakeMale > 0)
                    fixture.Add(GetStep<ReturnFoodStep>(feedIntake: henReport.FeedIntakeMale, ConsumedFoodStepName: HenReportSteps.ConsumedFoodStep));
            }

            else if (henReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodMaleStep || sn.Name == HenReportSteps.ConsumedFoodFemaleStep))
            {
                if (henReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodFemaleStep))
                    fixture.Add(GetStep<ReturnFoodStep>(feedIntake: henReport.FeedIntakeFemale, ConsumedFoodStepName: HenReportSteps.ConsumedFoodFemaleStep));

                if (henReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodMaleStep))
                    fixture.Add(GetStep<ReturnFoodStep>(feedIntake: henReport.FeedIntakeMale, ConsumedFoodStepName: HenReportSteps.ConsumedFoodMaleStep));
            }

            await ExecuteSteps(henReport, henBatch, henReport, fixture, createAndApprove);

            if (createAndApprove)
            {
                if (henReport.InconsistencyReport != null)
                    RevertInconsistencyReport(henReport);

                henBatchPerformanceBusinessLogic.Adjust(henReport);

                RevertHenBatchUpdate(henReport, henBatch);

                if (henBatch.ParentId.HasValue)
                {
                    HenBatch parent = await henBatchService.GetAsync(henBatch.ParentId.Value);
                    RevertHenBatchUpdate(henReport, parent);
                }

                henReport.HenBatchPerformanceId = null;
                henReport.ReportEnum = ReportEnum.Deleted;

                await henReportService.UpdateAsync(henReport);
            }
        }

        private void RevertEggsClassification(HenReport henReport, List<HenReportBaseStep> fixture)
        {
            // check tenant config
            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId().Value);

            Guid? containerId = henReport.ShippingNotes.Where(sn => sn.Name == HenReportSteps.SendEggsStep).Select(c => c.DestinationId).FirstOrDefault();

            Container origin = containerService.GetAll()
                                           .Include(c => c.OriginContainers)
                                           .Include(c => c.AcceptedMaterialType).ThenInclude(cmt => cmt.MaterialType)
                                           .FirstOrDefault(c => c.OriginContainers.Any(oc => oc.OriginId == containerId));

            // revert egg conciliation
            List<HenReportEggsDTO> EggsDTO = new List<HenReportEggsDTO>();
            Guid[] eggMaterialIds = henReport.ClassifiedEggs.Select(ce => ce.MaterialId).ToArray();
            Material[] eggMaterials = materialService.GetAll(true).Where(m => eggMaterialIds.Contains(m.Id)).Include(m => m.MaterialType).ToArray();
            // it is necessary to set the material type, in order to clasify the eggs
            var materialTypes = materialService.GetAll().Select(m => new
            {
                MaterialId = m.Id,
                MaterialTypePath = m.MaterialType.Path
            });
            foreach (HenReportClassifiedEgg egg in henReport.ClassifiedEggs)
            {
                int resultingQuantity = RevertConciliation(henReport, egg.MaterialId);
                if (egg.Quantity - resultingQuantity > 0)
                {
                    Material material = eggMaterials.First(m => m.Id == egg.MaterialId);
                    ContainerMaterialType containerMaterialType = origin.GetAcceptedMaterialType(material);
                    if (containerMaterialType.ActionEnum != ActionsEnum.Consume && containerMaterialType.ActionEnum != ActionsEnum.ConsumeAndProduce)
                        EggsDTO.Add(new HenReportEggsDTO()
                        {
                            EggId = egg.MaterialId.ToString(),
                            Quantity = egg.Quantity - resultingQuantity,
                            MaterialTypePath = materialTypes.FirstOrDefault(mt => mt.MaterialId == egg.MaterialId).MaterialTypePath
                        });
                }
            }
            bool groupLayingEggsByMaterialTypeCommercial = tenantConfig.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeCommercial && t.Value == "True");
            bool groupLayingEggsByMaterialTypeHatching = tenantConfig.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeHatching && t.Value == "True");
            if (!tenantConfig.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs && t.Value == "True") && EggsDTO.Count() != 0)
                fixture.Add(GetStep<RemoveEggsFromLineStep>(EggsDTO: EggsDTO, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));

            else if (EggsDTO.Count() != 0) fixture.Add(GetStep<ReturnEggsStep>(EggsDTO: EggsDTO, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));
        }

        private void RevertInconsistencyReport(HenReport henReport)
        {
            if (henReport.InconsistencyReport.Status == InconsistencyReportStatusEnum.PendingReview)
                inconsistencyReportService.Delete(henReport.InconsistencyReport);
            else
                throw exceptionManager.Handle(new UserException(this.localizer[Lang.CannotRemoveHenReport]));
        }

        private void RevertHenBatchUpdate(HenReport henReport, HenBatch henBatch)
        {
            henBatch.DeadAccumulatedFemale -= henReport.DeadFemale;
            henBatch.DeadAccumulatedMale -= henReport.DeadMale;
            henBatch.DepopulateAccumulatedFemale -= henReport.DepopulateFemale;
            henBatch.DepopulateAccumulatedMale -= henReport.DepopulateMale;
            henBatchService.Update(henBatch);
        }

        /// <summary>
        ///  function to revert egg conciliation
        /// </summary>
        private int RevertConciliation(HenReport henReport, Guid eggId)
        {
            double conciliationQuantity = 0;
            foreach (HenReportConciliation snc in henReport.ShippingNoteConciliations.Where(snc => snc.ShippingNoteConciliation.MaterialId == eggId))
            {
                conciliationQuantity += snc.ConciledQuantity;
                snc.ShippingNoteConciliation.RemainingQuantity += snc.ConciledQuantity;
                shippingNoteConciliationService.Update(snc.ShippingNoteConciliation);
            }
            return (int)conciliationQuantity;
        }

        /// <summary>
        /// Get the values of the values of a hen report to its relatives base values.
        /// </summary>
        public HenReportDTO GetBaseValues(HenReportDTO henReportDTO, HenStage henStage)
        {
            henReportDTO.ReturnedValues = new Dictionary<string, decimal>();

            foreach (Tuple<String, String, decimal> item in henReportDTO.ReceivedValues)
            {
                switch (item.Item1)
                {
                    case "WaterPh":
                        henReportDTO.ReturnedValues.Add(item.Item1, item.Item3);
                        break;
                    case "WaterChlorineConcentration":
                        henReportDTO.ReturnedValues.Add(item.Item1, item.Item3);
                        break;
                    case "WaterPillQuantity":
                        henReportDTO.ReturnedValues.Add(item.Item1, item.Item3);
                        break;
                    case "Humidity":
                        henReportDTO.ReturnedValues.Add(item.Item1, item.Item3);
                        break;
                    case "FeedIntake":
                        if (item.Item2 == "80633ce1-9419-4ae8-a9c8-0c01ea1460d4")
                        {
                            int henAmount = henBatchService.GetAll().Where(hb => hb.Id == Guid.Parse(henReportDTO.HenBatchId)).Select(hb => hb.HenAmountFemale + hb.HenAmountMale).First();
                            henReportDTO.ReturnedValues.Add(item.Item1, capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, item.Item3) * henAmount);
                            break;
                        }
                        goto default;
                    case "FeedIntakeFemale":
                        if (item.Item2 == "80633ce1-9419-4ae8-a9c8-0c01ea1460d4")
                        {
                            int henAmount = henBatchService.GetAll().Where(hb => hb.Id == Guid.Parse(henReportDTO.HenBatchId)).Select(hb => hb.HenAmountFemale).First();
                            henReportDTO.ReturnedValues.Add(item.Item1, capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, item.Item3) * henAmount);
                            break;
                        }
                        goto default;
                    case "FeedIntakeMale":
                        if (item.Item2 == "80633ce1-9419-4ae8-a9c8-0c01ea1460d4")
                        {
                            int henAmount = henBatchService.GetAll().Where(hb => hb.Id == Guid.Parse(henReportDTO.HenBatchId)).Select(hb => hb.HenAmountMale).First();
                            henReportDTO.ReturnedValues.Add(item.Item1, capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, item.Item3) * henAmount);
                            break;
                        }
                        goto default;
                    default:
                        decimal returnedValue = 0;
                        if (item.Item2 != CapacityUnits.GAD.ToString())
                            returnedValue = capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(item.Item2), item.Item3);
                        else
                            returnedValue = CalculateGAD(item.Item3.ToString(), new Guid(item.Item2), new Guid(henReportDTO.HenBatchId));
                        henReportDTO.ReturnedValues.Add(item.Item1, returnedValue);
                        break;
                }
            }

            return henReportDTO;
        }

        /// <summary>
        /// returns the options for farm, cluster and warehouse
        /// </summary>
        public List<List<SelectListItem>> GetContainers(HenBatchFilterDTO d)
        {
            List<SelectListItem> lines = new List<SelectListItem>();
            List<SelectListItem> warehouses = new List<SelectListItem>();
            List<SelectListItem> clusters = new List<SelectListItem>();
            List<SelectListItem> farms = new List<SelectListItem>();
            List<SelectListItem> feedIntakeOrigins = new List<SelectListItem>();
            IQueryable<Line> lineDDBB;
            IQueryable<HenWarehouse> WHddbb;
            IQueryable<Cluster> clusterDDBB;
            IQueryable<Farm> farmDDBB;
            IQueryable<ContainerContainer> feedIntakeOriginsDDBB;

            if (d.CurrentBatchId.HasValue)
            {
                HenBatch batch = henBatchService.GetFull(d.CurrentBatchId.Value);

                lineDDBB = lineService.GetAllFull(batch.HenStage).Where(l => l.WarehouseId == batch.Line.WarehouseId);

                WHddbb = henWarehouseService.GetAllFull(henStage: batch.HenStage).Where(w => w.ClusterId == batch.Line.Warehouse.ClusterId);

                clusterDDBB = clusterService.GetAllFull(henStage: batch.HenStage).Where(c => c.FarmId.Value == batch.Line.Warehouse.Cluster.FarmId.Value);

                farmDDBB = henBatchService.GetAllFull(henStage: batch.HenStage).Where(hb => !hb.DateEnd.HasValue).Select(x => x.Farm).Distinct();

                IEnumerable<Guid> formulasConsumed = batch.FormulasConsumed.Select(f => f.FormulaId);

                feedIntakeOriginsDDBB = henBatchService.GetAll().Where(hb => hb.Id == batch.Id)
                    .SelectMany(hb => hb.OriginContainers.Where(o => o.Origin.MaterialContainers.Any(mc => mc.MaterialBatches.Any(mb => formulasConsumed.Contains(mb.MaterialBatch.MaterialId) && mb.MaterialBatch.Quantity > 0))))
                    .AsQueryable();

                lines = lineDDBB.OrderBy(l => l.Name).Select(l => new SelectListItem(l.Name, l.Id.ToString(), l.Id == batch.LineId)).ToList();

                warehouses = WHddbb.OrderBy(w => w.Name).Select(w => new SelectListItem(w.Name, w.Id.ToString(), w.Id == batch.Line.WarehouseId)).ToList();

                clusters = clusterDDBB.OrderBy(w => w.Name).Select(c => new SelectListItem(c.Name, c.Id.ToString(), c.Id == batch.Line.Warehouse.ClusterId)).ToList();

                farms = farmDDBB.OrderBy(w => w.Name).Select(f => new SelectListItem(f.Name, f.Id.ToString(), f.Id == batch.Line.Warehouse.Cluster.FarmId.Value || farmDDBB.Count() == 1)).ToList();

                feedIntakeOrigins = feedIntakeOriginsDDBB.OrderBy(o => o.Origin.DetailedName).Select(o => new SelectListItem(o.Origin.DetailedName, o.OriginId.ToString())).ToList();
            }
            else
            {
                //Get lines by selected warehouse and henstage. Get warehouses, clusters and farms through this list.
                lineDDBB = lineService.GetWithOpenBatchAndBirds(henStage: d.HenStage);

                //Select the farms before filtering the lines by henwarehouse so that only that of the selected henwarehouse is not filtered.
                farmDDBB = lineDDBB.Select(l => l.Farm).Distinct().OrderBy(f => f.Name);
                farms.AddRange(farmDDBB.Select(f => new SelectListItem(f.Name, f.Id.ToString(), f.Id == d.SelectedFarm || farmDDBB.Count() == 1)));

                if (d.SelectedWarehouse.HasValue)
                    lineDDBB = lineDDBB.Where(l => l.WarehouseId == d.SelectedWarehouse);

                WHddbb = henWarehouseService.GetAll().Where(hw => hw.Lines.Any(l => lineDDBB.Any(lDB => lDB.Id == l.Id))).Select(w => new HenWarehouse { Id = w.Id, Name = w.Name, ClusterId = w.ClusterId });
                clusterDDBB = clusterService.GetAllFull().Where(c => WHddbb.Any(wh => wh.ClusterId == c.Id)).Select(c => new Cluster { Id = c.Id, Name = c.Name, FarmId = c.FarmId.Value });

                IQueryable<Guid> lineIds = lineDDBB.Select(l => l.Id);

                feedIntakeOriginsDDBB = henBatchService.GetAll()
                    .Where(hb => lineIds.Contains(hb.LineId.Value))
                    .Include(hb => hb.OriginContainers).ThenInclude(oc => oc.Origin)
                    .SelectMany(b => b.OriginContainers
                        .Where(o => o.Origin.AcceptedMaterialType.Any(amt => amt.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaAlimentacionFormula && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.Store))))
                    .Distinct().AsQueryable();

                if (d.SelectedFarm != Guid.Empty && d.SelectedFarm != null)
                    clusterDDBB = clusterDDBB.Where(c => c.FarmId.Value == d.SelectedFarm);

                if (d.SelectedCluster != Guid.Empty && d.SelectedCluster != null)
                    WHddbb = WHddbb.Where(wh => wh.ClusterId == d.SelectedCluster);

                if (d.SelectedWarehouse != Guid.Empty && d.SelectedWarehouse != null)
                    lineDDBB = lineDDBB.Where(l => l.WarehouseId == d.SelectedWarehouse);

                if (d.SelectedFeedIntakeOrigin != Guid.Empty && d.SelectedFeedIntakeOrigin != null)
                    feedIntakeOriginsDDBB = feedIntakeOriginsDDBB.Where(o => o.OriginId == d.SelectedFeedIntakeOrigin);

                foreach (Line line in lineDDBB)
                    lines.Add(new SelectListItem(line.Name, line.Id.ToString(), d.SelectedLine == line.Id));

                foreach (Warehouse wh in WHddbb)
                    warehouses.Add(new SelectListItem(wh.Name, wh.Id.ToString(), d.SelectedWarehouse == wh.Id));

                foreach (Cluster cluster in clusterDDBB)
                    clusters.Add(new SelectListItem(cluster.Name, cluster.Id.ToString(), cluster.Id == d.SelectedCluster));

                foreach (ContainerContainer container in feedIntakeOriginsDDBB)
                    feedIntakeOrigins.Add(new SelectListItem(container.Origin.DetailedName, container.OriginId.ToString(), container.OriginId == d.SelectedFeedIntakeOrigin));

                clusters = clusters.OrderBy(c => c.Text).ToList();
                warehouses = warehouses.OrderBy(wh => wh.Text).ToList();
                lines = lines.OrderBy(l => l.Text).ToList();
                feedIntakeOrigins = feedIntakeOrigins.OrderBy(o => o.Text).ToList();
            }

            return new List<List<SelectListItem>>() { farms, clusters, warehouses, lines, feedIntakeOrigins };
        }

        public List<SelectListItem> GetWareHouses(HenBatchFilterDTO data)
        {
            IQueryable<Line> lines = lineService.GetWithOpenBatchAndBirds(henStage: data.HenStage);

            IQueryable<HenWarehouse> warehouses = henWarehouseService.GetAll()
                .Where(hw => hw.Lines.Any(l => lines.Any(lDB => lDB.Id == l.Id)))
                .Select(w => new HenWarehouse { Id = w.Id, Name = w.Name, ClusterId = w.ClusterId });

            if (data.SelectedCluster != Guid.Empty && data.SelectedCluster != null)
                warehouses = warehouses.Where(wh => wh.ClusterId == data.SelectedCluster);

            return warehouses.OrderBy(w => w.Name).Select(w => new SelectListItem(w.Name, w.Id.ToString())).ToList();
        }

        public List<SelectListItem> GetClusters(HenBatchFilterDTO data)
        {
            IQueryable<Line> lines = lineService.GetWithOpenBatchAndBirds(henStage: data.HenStage);

            IQueryable<HenWarehouse> warehouses = henWarehouseService.GetAll()
                .Where(hw => hw.Lines.Any(l => lines.Any(lDB => lDB.Id == l.Id)))
                .Select(w => new HenWarehouse { Id = w.Id, Name = w.Name, ClusterId = w.ClusterId });

            IQueryable<Cluster> clusters = clusterService.GetAllFull()
                .Where(c => warehouses.Any(wh => wh.ClusterId == c.Id))
                .Select(c => new Cluster { Id = c.Id, Name = c.Name, FarmId = c.FarmId.Value });

            if (data.SelectedFarm != Guid.Empty && data.SelectedFarm != null)
                clusters = clusters.Where(c => c.FarmId.Value == data.SelectedFarm);

            return clusters.OrderBy(c => c.Name).Select(c => new SelectListItem(c.Name, c.Id.ToString())).ToList();
        }

        public List<HenReportHenBatchDTO> GetActiveHenBatchesByFarm(Guid farmId, HenStage henStage)
        {
            return henBatchService.GetAll()
                .Where(hb => hb.FarmId == farmId && hb.HenStage == henStage)
                .Where(hb => hb.DateEnd == null && (hb.InitialHenAmountFemale > 0 || hb.InitialHenAmountMale > 0)) // only active
                .Select(hb => new HenReportHenBatchDTO { 
                    Id = hb.Parent != null ? hb.Parent.Id : hb.Id, 
                    Code = hb.Parent != null ? hb.Parent.Code : hb.Code, 
                    BatchWeekNumber = hb.Parent != null ? hb.Parent.BatchWeekNumber : hb.BatchWeekNumber, 
                    DateStart = hb.Parent != null ? hb.Parent.DateStart : hb.DateStart, 
                    DayOfWeek = hb.Farm.DayOfWeek 
                })
                .Distinct()
                .ToList();
        }

        public List<PlannedGADDTO> GetPlannedGADByBatch(Guid parentBatchId, DateTime? date, bool shouldUseBatchWeekNumber = false)
        {
            // get week number
            var batchDates = henBatchService.GetAll()
                .Where(hb => hb.Id == parentBatchId)
                .Select(hb => new { hb.BatchWeekNumber, hb.DateStart, hb.Farm.DayOfWeek })
                .First();

            int weekNumber = shouldUseBatchWeekNumber ? batchDates.BatchWeekNumber : batchDates.BatchWeekNumber + (date.Value - batchDates.DateStart.Value.GetPastSelectedDay(batchDates.DayOfWeek)).Days / 7;
            // get child batches
            var childBatches = henBatchService.GetAll()
                .Where(hb => hb.ParentId == parentBatchId)
                .Select(hb => hb.Id);

            // get planned gad
            var plannedGad = reportPlannerProgramService.GetAll()
                .Where(p => childBatches.Contains(p.HenbatchId) && p.Week == weekNumber)
                .ToList()
                .GroupBy(p => p.HenbatchId)
                .Select(p => new PlannedGADDTO
                {
                    HenbatchId = p.Key,
                    Week = p.FirstOrDefault().Week,
                    ProgramFemale = p.FirstOrDefault(gad => gad.PlannerName == GADReportPlannerConst.ReportFemale)?.Program,
                    ProgramMale = p.FirstOrDefault(gad => gad.PlannerName == GADReportPlannerConst.ReportMale)?.Program,
                })
                .ToList();

            return plannedGad;
        }

        public List<HenReportWarehouseDTO> GetWarehousesWithLinesByBatch(Guid henBatchId)
        {
            // get the parent batch ID for the given hen batch
            var parentBatchId = henBatchService.GetAll()
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => hb.ParentId ?? hb.Id)
                .FirstOrDefault();

            // get all lines from breeding warehouses for the parent batch
            var lines = lineService.GetAll()
                .Where(l => l.HenBatches.Any(hb => hb.Id == parentBatchId || hb.ParentId == parentBatchId))
                .Select(l => new Line
                {
                    Id = l.Id,
                    Name = l.Name,
                    Code = l.Code,
                    Warehouse = new HenWarehouse
                    {
                        Id = l.Warehouse.Id,
                        Name = l.Warehouse.Name,
                        Code = l.Warehouse.Code,
                        HenStage = l.Warehouse.HenStage,
                        FarmId = l.Warehouse.FarmId,
                    },
                    HenBatches = l.HenBatches
                        .Where(hb => hb.Id == parentBatchId || hb.ParentId == parentBatchId)
                        .Where(hb => hb.DateEnd == null && (hb.HenAmountFemale > 0 || hb.HenAmountMale > 0)) // only active
                        .Select(hb => new HenBatch
                        {
                            Id = hb.Id,
                            Code = hb.Code,
                            HenAmountFemale = hb.HenAmountFemale,
                            HenAmountMale = hb.HenAmountMale,
                            BatchWeekNumber = hb.BatchWeekNumber,
                            OriginContainers = hb.OriginContainers
                                .Where(o => o.Origin.Active && (
                                    o.Origin.MaterialContainers.Any(mc => hb.FormulasConsumed.Select(fc => fc.FormulaId).Contains(mc.MaterialId) && mc.Quantity > 0) ||
                                    o.Origin.AcceptedMaterialType.Any(amt => MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula.Contains(amt.MaterialType.Path) &&
                                        (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce || amt.ActionEnum == ActionsEnum.Store))))
                                .Select(o => new ContainerContainer
                                {
                                    OriginId = o.OriginId,
                                    Origin = new Silo
                                    {
                                        Id = o.OriginId,
                                        Name = o.Origin.Name,
                                        Code = o.Origin.Code,
                                    },
                                })
                                .ToList()
                        })
                        .ToList()
                })
                .ToList();

            // group lines by warehouse
            var warehouses = lines
                   .GroupBy(l => l.Warehouse.Id)
                   .Select(g => new HenReportWarehouseDTO
                   {
                       Id = g.First().Warehouse.Id,
                       Name = g.First().Warehouse.Name,
                       Code = g.First().Warehouse.Code,
                       Lines = g.ToList().Select(l => new HenReportLineDTO
                       {
                           Id = l.Id,
                           Name = l.Name,
                           Code = l.Code,
                           HenBatches = l.HenBatches.Select(hb => new HenReportHenBatchDTO
                           {
                               Id = hb.Id,
                               Code = hb.Code,
                               BatchWeekNumber = hb.BatchWeekNumber,
                               HenAmountFemale = hb.HenAmountFemale,
                               HenAmountMale = hb.HenAmountMale,
                               FeedIntakeOrigins = hb.OriginContainers.Select(o => new HenReportFeedIntakeOriginDTO
                               {
                                   Id = o.OriginId,
                                   Name = o.Origin.Name,
                                   Code = o.Origin.Code,
                               }).ToList()
                           }).ToList()
                       })
                       .OrderBy(l => int.TryParse(l.Code, out int number) ? number : 0).ThenBy(w => w.Name)
                       .ToList()
                   })
                   .OrderBy(w => int.TryParse(w.Code, out int number) ? number : 0).ThenBy(w => w.Name)
                   .ToList();

            return warehouses;
        }

        /// <summary>
        /// Returns containers that have material stock of the formulas associated with the hen batch
        /// </summary>
        public IQueryable<Container> GetFeedIntakeOrigins(Guid henBatchId)
        {
            IEnumerable<Guid> formulasConsumed = henBatchService.GetAll().Where(hb => hb.Id == henBatchId).SelectMany(hb => hb.FormulasConsumed).Select(fc => fc.FormulaId).ToArray();

            return henBatchService.GetAll().Where(hb => hb.Id == henBatchId)
                .SelectMany(hb => hb.OriginContainers
                .Where(o => o.Origin.Active && (
                    o.Origin.MaterialContainers.Any(mc => formulasConsumed.Contains(mc.MaterialId) && mc.Quantity > 0) ||
                    o.Origin.AcceptedMaterialType.Any(amt => MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula.Contains(amt.MaterialType.Path) && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce || amt.ActionEnum == ActionsEnum.Store)))))
                .Select(c => c.Origin);
        }

        /// <summary>
        /// Returns containers that have material stock of the formulas associated with the hen batch
        /// </summary>
        public IQueryable<ContainerContainer> GetFeedIntakeOrigins(params Guid[] henBatches)
        {
            var formulasConsumed = henBatchService.GetAll()
                .Where(hb => henBatches.Contains(hb.Id))
                .Select(hb => new
                {
                    henBatch = hb.Id,
                    formulas = hb.FormulasConsumed.Select(fc => fc.FormulaId)
                }).ToList();

            DbContext context = unitOfWork.GetModelDbContext();

            return context.Set<ContainerContainer>()
                .Where(cc => formulasConsumed.Any(fc =>
                    cc.ContainerId == fc.henBatch
                    && (cc.Origin.MaterialContainers.Any(mc => fc.formulas.Contains(mc.MaterialId) && mc.Quantity > 0)
                        || cc.Origin.AcceptedMaterialType.Any(amt => MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula.Contains(amt.MaterialType.Path) && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce || amt.ActionEnum == ActionsEnum.Store)))));
        }

        /// <summary>
        /// Returns material batches from selected container with material of the formulas associated with the hen batch
        /// </summary>
        public IQueryable<MaterialBatchContainer> GetAvailableMaterialBatches(Guid henBatchId, Guid originId)
        {
            IEnumerable<Guid> formulasConsumed = henBatchService.GetAll().Where(hb => hb.Id == henBatchId).SelectMany(hb => hb.FormulasConsumed).Select(fc => fc.FormulaId).ToArray();

            return containerService.GetAll()
                .Where(c => c.Id == originId)
                .SelectMany(c => c.MaterialContainers)
                .SelectMany(mc => mc.MaterialBatches).Where(mc => formulasConsumed.Contains(mc.MaterialBatch.MaterialId) && mc.Quantity > 0);
        }

        public bool CheckIfProduces(Guid originId)
        {
            return containerService.GetAll().Where(c => c.Active)
                .Any(c => c.AcceptedMaterialType
                    .Any(amt => MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula.Contains(amt.MaterialType.Path) &&
                    (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce)));
        }

        /// <summary>
        /// Searches hen reports and exports them as an excel file.
        /// If no search term is provided, returns all hen reports.
        /// </summary>
        public async Task<ExportResult> ExportExcel(Dictionary<string, string> data, string searchTerm = null, HenStage? henStage = null)
        {

            bool nullData = data == null;
            if (!nullData)
                nullData |= data.All(d => string.IsNullOrEmpty(d.Value));

            IQueryable<HenReport> query = nullData
                ? this.henReportService.GetAllFull(henStage).Where(hr => hr.ReportEnum == ReportEnum.New)
                : this.henReportService.GetFullFiltered(data).Where(hr => hr.ReportEnum == ReportEnum.New);


            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(hr =>
                    hr.Name.Contains(searchTerm) ||
                    hr.HenBatch.Line.Warehouse.Cluster.Name.Contains(searchTerm) ||
                    hr.HenBatch.Line.Warehouse.Name.Contains(searchTerm) ||
                    hr.HenBatch.Line.Name.Contains(searchTerm) ||
                    hr.HenBatch.Genetic.Name.Contains(searchTerm) ||
                    hr.HenBatch.Name.Contains(searchTerm) ||
                    hr.DeadFemale.ToString().Contains(searchTerm) ||
                    hr.DeadMale.ToString().Contains(searchTerm) ||
                    hr.FeedIntakeFemale.ToString().Contains(searchTerm) ||
                    hr.FeedIntakeMale.ToString().Contains(searchTerm) ||
                    hr.WaterConsumption.ToString().Contains(searchTerm) ||
                    hr.MinTemp.ToString().Contains(searchTerm) ||
                    hr.MaxTemp.ToString().Contains(searchTerm)
                );
            }

            IQueryable<ExcelExportDTO> dtos = query.Select(e => new ExcelExportDTO(e));

            string fileDownloadName = string.Format(localizer[Lang.ExcelExportFilename], DateTime.Now.ToString("yyyyMMdd"));

            return await excelExportBusinessLogic.ExportExcel(dtos, fileDownloadName);
        }

        /// <summary>
        /// Validate HenReportDTO and create the entity
        /// </summary>
        public async Task<HenReport> ValidateDTOAndCreateEntity(HenReportAPI dto)
        {
            HenBatch henBatch = dto.HenBatchId.HasValue
                ? await henBatchService.GetAsync(dto.HenBatchId.Value)
                : await henBatchService.GetByFarmHenWarehouseAndLineCode(dto.Farm, dto.HenWarehouse, dto.Line);

            IQueryable<Guid> formulasConsumed = henBatchService.GetAll().Where(hb => hb.Id == henBatch.Id)
                .SelectMany(hb => hb.FormulasConsumed)
                .Select(fc => fc.FormulaId);

            // Filter formulas to get feed origins and select the ids
            IQueryable<Guid> feedOriginIds = henBatchService.GetAll()
                .SelectMany(hb => hb.OriginContainers
                .Where(o =>
                    o.Origin.MaterialContainers.Any(mc => formulasConsumed.Contains(mc.MaterialId) && mc.Quantity > 0) ||
                    o.Origin.AcceptedMaterialType.Any(amt => amt.MaterialTypeId == MaterialTypes.InsumoMateriaPrimaAlimentacionFormula && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce || amt.ActionEnum == ActionsEnum.Store))))
                .Select(o => o.OriginId);


            if (dto.MinTemp > dto.MaxTemp)
                throw new ValidationException(localizer[Lang.MaxTempEx]);

            if (dto.DepopulateFemale > 0 || dto.DeadFemale > 0)
            {
                //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                int depopulationDDBB = henReportService.GetAll().Where(hr => hr.HenBatchId == henBatch.Id).Select(hr => hr.DepopulateFemale).Sum();
                if (henBatch.HenAmountFemale - dto.DeadFemale - dto.DepopulateFemale - dto.ToFloorFemale - depopulationDDBB < 0)
                    throw new ValidationException(localizer[Lang.NoFemaleHenEx]);
            }

            if (dto.DepopulateMale > 0 || dto.DeadMale > 0)
            {
                //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                int depopulationDDBB = henReportService.GetAll().Where(hr => hr.HenBatchId == henBatch.Id).Select(hr => hr.DepopulateMale).Sum();
                if (henBatch.HenAmountMale - dto.DeadMale - dto.DepopulateMale - dto.ToFloorMale - depopulationDDBB < 0)
                    throw new ValidationException(localizer[Lang.NoMaleHenEx]);
            }

            if (dto.Date > DateTime.Now)
                throw new ValidationException(localizer[Lang.GreaterThanToday]);

            HenReport henReport = dto.ToEntity();
            henReport.HenBatchId = henBatch.Id;

            return henReport;
        }

        ///<inheritdoc/>
        public void ValidateDTO(List<CreateFromWarehouseAPI> warehouseReport)
        {
            DateTime minDate = warehouseReport.Select(r => r.GeneralReportDTO.Date).Min();
            string generalValidationsKey = minDate.ToString();
            Dictionary<string, List<string>> errors = new Dictionary<string, List<string>> { { generalValidationsKey, new List<string>() } };
            errors.TryGetValue(generalValidationsKey, out List<string> generalErrors);

            if (!warehouseReport.Any(d => d.GeneralReportDTO.WaterConsumption.HasValue))
                generalErrors.Add(localizer[Lang.WaterConsumptionRequired]);

            if (!warehouseReport.Any(d => d.GeneralReportDTO.MinTemp.HasValue))
                generalErrors.Add(localizer[Lang.MinTempRequired]);

            if (!warehouseReport.Any(d => d.GeneralReportDTO.MaxTemp.HasValue))
                generalErrors.Add(localizer[Lang.MaxTempRequired]);

            if (!warehouseReport.Any(d => d.GeneralReportDTO.Humidity.HasValue))
                generalErrors.Add(localizer[Lang.HumidityRequired]);

            if (!warehouseReport.Any(d => d.GeneralReportDTO.WaterPh.HasValue))
                generalErrors.Add(localizer[Lang.WaterPhRequired]);

            if (!warehouseReport.Any(d => d.GeneralReportDTO.WaterChlorineConcentration.HasValue))
                generalErrors.Add(localizer[Lang.WaterChlorineConcentrationRequired]);

            HenWarehouse warehouse = henWarehouseService.Get(warehouseReport.First().HenWarehouseId);

            IEnumerable<FeedIntakeDTO> allFeedIntakes = warehouseReport.SelectMany(wr => wr.FeedIntakeDTO);
            bool feedIntakeIsDeclared = feedIntakeIsDeclared = allFeedIntakes != null && allFeedIntakes.Any()
                && (allFeedIntakes.Sum(fi => fi.FemaleQuantity) > 0 || allFeedIntakes.Sum(fi => fi.MaleQuantity) > 0);

            // For laying, feed intake is required. Verify if there is any feed intake declared; otherwise, return validation.
            // If feed intake was declared, further validations will be made below for each particular henbatch.
            if (warehouse.HenStage == HenStage.Laying && !feedIntakeIsDeclared)
                generalErrors.Add(localizer[Lang.FeedIntakeRequired]);

            List<Guid> henBatchesIds = henBatchService.GetAllFromWarehouse(warehouseReport.First().HenWarehouseId)
                .Where(hb => hb.HenAmountFemale > 0 || hb.HenAmountMale > 0)
                .Select(hb => hb.Id).ToList();

            // For laying batches, if no eggs are declared, validate first production date
            if (warehouse.HenStage == HenStage.Laying && (!warehouseReport.Any(r => r.EggQuantityDTO != null && r.EggQuantityDTO.Any()) || warehouseReport.Sum(r => r.EggQuantityDTO.Sum(eq => eq.Quantity)) == 0))
            {
                try
                {
                    ValidateFirstProductionDate(minDate, null, henBatchesIds);
                }
                catch (ValidationException ex)
                {
                    bool keyIsDefined = errors.TryGetValue(generalValidationsKey, out List<string> errorsByReport);

                    if (!keyIsDefined)
                        errors.Add(generalValidationsKey, new List<string> { ex.Message });
                    else
                        errorsByReport.Add(ex.Message);
                }
            }

            foreach (Guid id in henBatchesIds)
            {
                HenBatch henBatch = henBatchService.Get(id);

                try
                {
                    ValidateDate(minDate, id);
                }
                catch (ValidationException ex)
                {
                    bool keyIsDefined = errors.TryGetValue(generalValidationsKey, out List<string> errorsByReport);

                    if (!keyIsDefined)
                        errors.Add(generalValidationsKey, new List<string> { ex.Message });
                    else
                        errorsByReport.Add(ex.Message);
                }

                if (!GetFeedIntakeOrigins(id).Any())
                    generalErrors.Add(localizer[Lang.FeedIntakeOriginNotFound]);

                // In Laying, feed intake is required for both females and males in all henbatches where their amounts are >0.
                if (feedIntakeIsDeclared && warehouse.HenStage == HenStage.Laying)
                {
                    IEnumerable<FeedIntakeDTO> feedIntakes = warehouseReport.SelectMany(r => r.FeedIntakeDTO.Where(fi => fi.HenBatchId == id));

                    if (henBatch.HenStage == HenStage.Laying && henBatch.HenAmountFemale > 0 && feedIntakes.Sum(fi => fi.FemaleQuantity) <= 0)
                        generalErrors.Add(localizer[Lang.FemaleFeedIntakeRequired] + henBatch.DetailedName);

                    if (henBatch.HenStage == HenStage.Laying && henBatch.HenAmountMale > 0 && feedIntakes.Sum(fi => fi.MaleQuantity) <= 0)
                        generalErrors.Add(localizer[Lang.MaleFeedIntakeRequired] + henBatch.DetailedName);
                }

                IEnumerable<BirdsDTO> birds = warehouseReport.SelectMany(r => r.BirdsDTO.Where(b => b.HenBatchId == id));

                int totalDepopulateFemale = birds.Sum(b => b.DepopulateFemale);
                int totalDeadFemale = birds.Sum(b => b.DeadFemale);
                if (totalDepopulateFemale > 0 || totalDeadFemale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = henReportService.GetAll().Where(hr => hr.HenBatchId == id).Select(hr => hr.DepopulateFemale).Sum();
                    if (henBatch.HenAmountFemale - totalDeadFemale - totalDepopulateFemale - depopulationDDBB < 0)
                        generalErrors.Add(localizer[Lang.NoFemaleHenEx]);
                }
                int totalDepopulateMale = birds.Sum(b => b.DepopulateMale);
                int totalDeadMale = birds.Sum(b => b.DeadMale);
                if (totalDepopulateMale > 0 || totalDeadMale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = henReportService.GetAll().Where(hr => hr.HenBatchId == henBatch.Id).Select(hr => hr.DepopulateMale).Sum();
                    if (henBatch.HenAmountMale - totalDeadMale - totalDepopulateMale - depopulationDDBB < 0)
                        generalErrors.Add(localizer[Lang.NoMaleHenEx]);
                }
            }
            if (errors.Any(e => e.Value != null && e.Value.Any()))
                throw new ValidationException("", errors.ToDictionary(e => e.Key, e => string.Join(",", e.Value.Distinct())));
        }

        ///<inheritdoc/>
        public IEnumerable<HenBatchHenReportDTO> ValidateDTOAndCreateEntity(CreateFromWarehouseAPI dto)
        {
            ValidateSameHenBatches(dto);

            Guid[] henBatches = dto.FeedIntakeDTO
                .Select(r => r.HenBatchId)
                .ToArray();

            Guid warehouseId = henBatchService.GetAll().Where(hb => hb.Id == henBatches.First())
                .Select(hb => hb.Line.WarehouseId).First();

            HenStage area = henBatchService.GetAll().Where(hb => hb.Id == henBatches.First())
                .Select(hb => hb.HenStage).First();

            if (area == HenStage.Laying && (dto.EggQuantityDTO == null || !dto.EggQuantityDTO.Any() || dto.EggQuantityDTO.Sum(eqd => eqd.Quantity) == 0))
                ValidateFirstProductionDate(dto.GeneralReportDTO.Date, warehouseId);

            foreach (Guid id in henBatches)
            {
                HenBatch henBatch = henBatchService.Get(id);

                ValidateDate(dto.GeneralReportDTO.Date, id);

                Guid[] formulasConsumed = ValidateFormulasConsumed(id);

                if (!GetFeedIntakeOrigins(id).Any())
                    throw new ValidationException(localizer[Lang.FeedIntakeOriginNotFound]);

                BirdsDTO birds = dto.BirdsDTO.First(b => b.HenBatchId == id);

                if (birds.DepopulateFemale > 0 || birds.DeadFemale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = henReportService.GetAll().Where(hr => hr.HenBatchId == id).Select(hr => hr.DepopulateFemale).Sum();
                    if (henBatch.HenAmountFemale - birds.DeadFemale - birds.DepopulateFemale - depopulationDDBB < 0)
                        throw new ValidationException(localizer[Lang.NoFemaleHenEx]);
                }

                if (birds.DepopulateMale > 0 || birds.DeadMale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = henReportService.GetAll().Where(hr => hr.HenBatchId == henBatch.Id).Select(hr => hr.DepopulateMale).Sum();
                    if (henBatch.HenAmountMale - birds.DeadMale - birds.DepopulateMale - depopulationDDBB < 0)
                        throw new ValidationException(localizer[Lang.NoMaleHenEx]);
                }

                CasualtiesAndDepopulations casualties = dto.CasualtiesDTO != null && dto.CasualtiesDTO.Any(c => c.HenBatchId == id) ? dto.CasualtiesDTO.FirstOrDefault(c => c.HenBatchId == id) : null;
                HenBatchHenReportDTO report = HenBatchHenReportDTO(dto.FeedIntakeDTO.FirstOrDefault(fi => fi.HenBatchId == id), birds, casualties);
                report.HenAmountFemale = henBatch.HenAmountFemale;
                report.HenAmountMale = henBatch.HenAmountMale;
                yield return report;
            }
        }
        public IEnumerable<HenBatchHenReportDTO> CreateEntity(CreateFromWarehouseAPI dto)
        {
            List<Guid> henBatches = henBatchService.GetAllFromWarehouse(dto.HenWarehouseId)
                .Where(hb => hb.HenAmountFemale > 0 || hb.HenAmountMale > 0)
                .Select(hb => hb.Id).ToList();

            foreach (Guid id in henBatches)
            {
                HenBatch henBatch = henBatchService.Get(id);
                BirdsDTO birds = dto.BirdsDTO.Any(b => b.HenBatchId == id) ? dto.BirdsDTO.First(b => b.HenBatchId == id) : new BirdsDTO() { HenBatchId = id };
                CasualtiesAndDepopulations casualties = dto.CasualtiesDTO != null && dto.CasualtiesDTO.Any(c => c.HenBatchId == id) ? dto.CasualtiesDTO.FirstOrDefault(c => c.HenBatchId == id) : null;
                FeedIntakeDTO feedIntake = dto.FeedIntakeDTO.Any(b => b.HenBatchId == id) ? dto.FeedIntakeDTO.First(b => b.HenBatchId == id) : new FeedIntakeDTO() { HenBatchId = id };
                HenBatchHenReportDTO report = HenBatchHenReportDTO(dto.FeedIntakeDTO.FirstOrDefault(fi => fi.HenBatchId == id), birds, casualties);
                report.HenAmountFemale = henBatch.HenAmountFemale;
                report.HenAmountMale = henBatch.HenAmountMale;
                yield return report;
            }
        }

        /// <summary>
        /// Creates hen report draft and ReportRectification
        /// </summary>
        public async Task CreateHenReportDraft(Draft draft, Guid henReportId, HenReport newHenReport, HenReportRectificationDTO differences)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                // get needed entities
                HenReport previousHenReport = henReportService.GetAll()
                .Include(hr => hr.ShippingNotes).ThenInclude(sn => sn.MaterialBatchesShipped).ThenInclude(mb => mb.MaterialBatch)
                .Include(hr => hr.ShippingNotes).ThenInclude(sn => sn.MaterialsShipped)
                .FirstOrDefault(p => p.Id == henReportId);

                // check hen batch
                HenBatch henBatch = null;
                try
                {
                    henBatch = henBatchService.GetFull(previousHenReport.HenBatchId);
                }
                catch (NotFoundException)
                {
                    throw new ValidationException(null, new BusinessValidationResult<ShippingNote>(localizer[Lang.HenBatchNotFound]).UnsuccessfulValidations);
                }

                List<HenReportBaseStep> fixture = await BuildFixture(newHenReport, previousHenReport, differences, henBatch, true);
                // process shipping notes
                newHenReport.ShippingNotes = new List<ShippingNote>();

                foreach (HenReportBaseStep step in fixture)
                {
                    (BusinessValidationResult<ShippingNote> validationResult, ShippingNote shippingNote) = await step.ProcessShippingNote(henBatch, newHenReport, previousHenReport, true);
                    if (!validationResult.IsValid)
                        foreach (ValidationResponse<ShippingNote> v in validationResult.Validations)
                            throw new ValidationException(v.Validation);
                    if (shippingNote != null)
                        newHenReport.ShippingNotes.Add(shippingNote);
                }

                ReportRectification rectification = new ReportRectification()
                {
                    AuthorizationResponseEnum = AuthorizationResponseEnum.Pending,
                    Draft = draft,
                    DraftId = draft.Id,
                    CompanyId = previousHenReport.CompanyId,
                    FarmId = previousHenReport.FarmId,
                    SectorId = previousHenReport.SectorId,
                    RectificationHenReport = previousHenReport,
                    RectificationHenReportId = previousHenReport.Id,
                    Action = RectificationActionEnum.Modify
                };

                await this.draftService.CreateOrUpdate(draft);
                await this.reportRectificationService.CreateAsync(rectification);

            });

        }

        /// <summary>
        ///  returns a dto with the diference between the report and the rectification
        /// </summary>
        public HenReportRectificationDTO GetDifferences(HenReport report, Guid DDBBreportId, HenStage? henStage = null)
        {
            HenReport DBreport = henReportService.GetWithEggs(DDBBreportId);
            HenReportRectificationDTO differences = new HenReportRectificationDTO()
            {
                HenAmountFemale = DBreport.HenAmountFemale,
                HenAmountMale = DBreport.HenAmountMale,
                DeadFemale = report.DeadFemale - DBreport.DeadFemale,
                DeadMale = report.DeadMale - DBreport.DeadMale,
                FeedIntakeFemale = report.FeedIntakeFemale - DBreport.FeedIntakeFemale,
                FeedIntakeMale = report.FeedIntakeMale - DBreport.FeedIntakeMale,
                TotalEggs = (int)(report.TotalEggs - DBreport.TotalEggs),
                HatchableEggs = (int)(report.HatchableEggs - DBreport.HatchableEggs),
                CommercialEggs = (int)(report.CommercialEggs - DBreport.CommercialEggs),
                BrokenEggs = (int)report.BrokenEggs - (int)DBreport.BrokenEggs,
                DepopulateFemale = report.DepopulateFemale - DBreport.DepopulateFemale,
                DepopulateMale = report.DepopulateMale - DBreport.DepopulateMale,
                ToFloorFemale = report.ToFloorFemale - DBreport.ToFloorFemale,
                ToFloorMale = report.ToFloorMale - DBreport.ToFloorMale,
                ToCageFemale = report.ToCageFemale - DBreport.ToCageFemale,
                ToCageMale = report.ToCageMale - DBreport.ToCageMale,
                WaterChlorineConcentrationChange = report.WaterChlorineConcentration != DBreport.WaterChlorineConcentration,
                WaterConsumptionChange = report.WaterConsumption != DBreport.WaterConsumption,
                WaterPhChange = report.WaterPh != DBreport.WaterPh,
                WaterPillQuantityChange = report.WaterPillQuantity != DBreport.WaterPillQuantity
            };

            // it is necessary to set the material type, in order to clasify the eggs
            var materialTypes = materialService.GetAll().Select(m => new
            {
                MaterialId = m.Id,
                MaterialTypePath = m.MaterialType.Path
            });

            // add common eggs
            foreach (HenReportClassifiedEgg egg in report.ClassifiedEggs.Where(ce => DBreport.ClassifiedEggs.Any(db => db.MaterialId == ce.MaterialId)))
                differences.EggsDifferences.Add(new HenReportEggsDTO()
                {
                    EggId = egg.MaterialId.ToString(),
                    Quantity = egg.Quantity - DBreport.ClassifiedEggs.FirstOrDefault(db => db.MaterialId == egg.MaterialId).Quantity,
                    MaterialTypePath = materialTypes.FirstOrDefault(mt => mt.MaterialId == egg.MaterialId).MaterialTypePath
                });

            // add new eggs
            foreach (HenReportClassifiedEgg egg in report.ClassifiedEggs.Where(ce => !DBreport.ClassifiedEggs.Any(db => db.MaterialId == ce.MaterialId)))
                differences.EggsDifferences.Add(new HenReportEggsDTO()
                {
                    EggId = egg.MaterialId.ToString(),
                    Quantity = egg.Quantity,
                    MaterialTypePath = materialTypes.FirstOrDefault(mt => mt.MaterialId == egg.MaterialId).MaterialTypePath
                });

            // add eggs that are no longer used
            foreach (HenReportClassifiedEgg egg in DBreport.ClassifiedEggs.Where(db => !report.ClassifiedEggs.Any(ce => db.MaterialId == ce.MaterialId)))
                differences.EggsDifferences.Add(new HenReportEggsDTO()
                {
                    EggId = egg.MaterialId.ToString(),
                    Quantity = egg.Quantity * (-1),
                    MaterialTypePath = materialTypes.FirstOrDefault(mt => mt.MaterialId == egg.MaterialId).MaterialTypePath
                });

            if (henStage.HasValue && henStage.Value == HenStage.Breeding)
                differences.FeedIntake = (report.FeedIntakeFemale + report.FeedIntakeMale) - (DBreport.FeedIntakeFemale + DBreport.FeedIntakeMale);

            return differences;
        }

        /// <summary>
        /// Gets all the casualty reasons by area with it's quantities for male and female
        /// </summary>
        public (List<string> headers, List<Dictionary<string, string>> table) GetDeathQuantitiesTable(Guid henReportId, HenStage henStage)
        {
            List<string> headers = new List<string>(deathQuantitiesConstants.TableHeaders);
            List<Dictionary<string, string>> table = new List<Dictionary<string, string>>();

            HenReport henReport = henReportService.GetAllWithCasualityReason(henReportId).FirstOrDefault();
            IQueryable<CasualtyReason> casualtyReasons = casualtyReasonService.GetAll()
                .Where(cr => cr.Area == (AreaEnum)henStage && (cr.Active || henReport.Casualties.Select(c => c.CasualtyReasonId).Distinct().Contains(cr.Id)))
                .OrderBy(cr => cr.Name);

            foreach (CasualtyReason casualtyReason in casualtyReasons)
            {
                Dictionary<string, string> row = NewRowDictionary();

                row["reason"] = casualtyReason.ToString();
                row["reasonId"] = casualtyReason.Id.ToString();
                row["female"] = henReport.CasualtiesFemale.Where(c => c.CasualtyReasonId == casualtyReason.Id).Sum(c => c.DeadCount).ToString();
                row["male"] = henReport.CasualtiesMale.Where(c => c.CasualtyReasonId == casualtyReason.Id).Sum(c => c.DeadCount).ToString();

                table.Add(row);
            }

            return (headers, table);
        }

        /// <inheritdoc/>
        public (List<string> headers, List<Dictionary<string, string>> table) GetDepopulateTable(Guid henReportId, HenStage henStage)
        {
            IQueryable<HenReport> henReport = henReportService.GetAll(asNoTracking: true).Where(hr => hr.Id == henReportId);
            List<string> headers = new List<string>(deathQuantitiesConstants.TableHeaders);
            List<Dictionary<string, string>> table = new List<Dictionary<string, string>>();

            Guid[] reportDepopulationReasons = henReport.SelectMany(hr => hr.Depopulations.Select(c => c.DepopulationReasonId)).Distinct().ToArray();
            DepopulationReason[] depopulationReasons = depopulationReasonService.GetAll(asNoTracking: true)
                .Where(cr => cr.Area == (AreaEnum)henStage && (cr.Active || reportDepopulationReasons.Contains(cr.Id)))
                .OrderBy(cr => cr.Name)
                .ToArray();

            foreach (DepopulationReason depopulationReason in depopulationReasons)
            {
                Dictionary<string, string> row = NewRowDictionary();

                row["reason"] = depopulationReason.ToString();
                row["reasonId"] = depopulationReason.Id.ToString();
                row["female"] = henReport.SelectMany(hr => hr.Depopulations).Where(d => d.IsFemale && d.DepopulationReasonId == depopulationReason.Id).Sum(df => df.DepopulationCount).ToString();
                row["male"] = henReport.SelectMany(hr => hr.Depopulations).Where(d => !d.IsFemale && d.DepopulationReasonId == depopulationReason.Id).Sum(df => df.DepopulationCount).ToString();

                table.Add(row);
            }

            return (headers, table);
        }

        /// <summary>
        /// Update or create henreport's casualties from DeathQuantities table.
        /// </summary>
        public async Task EditDeathAndDepopulationQuantities(DeathAndDepopulationQuantitiesDTO deathAndDepopulationQuantitiesDTO)
        {
            HenReport henReport = await henReportService.GetAll()
                .Where(hr => hr.Id == deathAndDepopulationQuantitiesDTO.HenReports.First())
                .Include(hr => hr.Casualties)
                .Include(hr => hr.Depopulations)
                .FirstAsync();

            //To make sure all changes are saved at the end when all validations are passed.
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                henReport.Casualties = GetCasualties(deathAndDepopulationQuantitiesDTO.Dead).ToList();
                henReport.Depopulations = deathAndDepopulationQuantitiesDTO.Depopulate != null
                                            ? GetDepopulations(deathAndDepopulationQuantitiesDTO.Depopulate).ToList()
                                            : null;
                await henReportService.UpdateAsync(henReport);
            });
        }

        /// <summary>
        /// Update or create henreports casualties and depopulations
        /// </summary>
        public async Task EditDeathAndDepopulationQuantitiesByWarehouse(IEnumerable<CasualtiesAndDepopulations> casualtiesAndDepopulations)
        {
            IQueryable<HenReport> henReports = henReportService.GetAll()
                   .Where(hr => casualtiesAndDepopulations.Select(cd => cd.HenReportId).Contains(hr.Id))
                   .Include(hr => hr.Casualties)
                   .Include(hr => hr.Depopulations);

            //To make sure all changes are saved at the end when all validations are passed.
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                foreach (CasualtiesAndDepopulations dto in casualtiesAndDepopulations)
                {
                    HenReport henReport = await henReports.FirstAsync(hr => hr.Id == dto.HenReportId);

                    await henReportService.UpdateAsync(dto.FillCasualtiesAndDepopulations(henReport));
                }
            });

        }

        private IEnumerable<Casualty> GetCasualties(List<Dictionary<string, string>> quantities)
        {
            foreach (Dictionary<string, string> row in quantities)
            {
                if (row.ContainsKey("reasonId"))
                {
                    Guid reasonId = new Guid(row["reasonId"]);

                    int femaleDead = int.Parse(row["female"]);
                    int maleDead = int.Parse(row["male"]);

                    Casualty femaleCasualty = new Casualty
                    {
                        CasualtyReasonId = reasonId,
                        DeadCount = femaleDead,
                        IsFemale = true
                    };

                    yield return femaleCasualty;

                    Casualty maleCasualty = new Casualty
                    {
                        CasualtyReasonId = reasonId,
                        DeadCount = maleDead,
                        IsFemale = false
                    };

                    yield return maleCasualty;
                }
            }
        }

        private IEnumerable<Depopulation> GetDepopulations(List<Dictionary<string, string>> quantities)
        {
            foreach (Dictionary<string, string> row in quantities)
            {
                if (row.ContainsKey("reasonId"))
                {
                    Guid reasonId = new Guid(row["reasonId"]);

                    int femaleDepopulation = int.Parse(row["female"]);
                    int maleDepopulation = int.Parse(row["male"]);

                    Depopulation femaleCasualty = new Depopulation
                    {
                        DepopulationReasonId = reasonId,
                        DepopulationCount = femaleDepopulation,
                        IsFemale = true
                    };

                    yield return femaleCasualty;

                    Depopulation maleCasualty = new Depopulation
                    {
                        DepopulationReasonId = reasonId,
                        DepopulationCount = maleDepopulation,
                        IsFemale = false
                    };

                    yield return maleCasualty;

                }
            }
        }
        public class DeathQuantitiesConstants
        {
            public readonly List<string> TableHeaders;
            public readonly List<string> DictionaryKeys;

            public DeathQuantitiesConstants(IStringLocalizer<SharedResources> localizer)
            {
                TableHeaders = new List<string>() {
                    localizer[Lang.Reason],
                    localizer[Lang.Reason],
                    localizer[Lang.Female],
                    localizer[Lang.Male]
            };

                DictionaryKeys = new List<string>() {
                "reason",
                "reasonId",
                "female",
                "male"
            };
            }
        }

        /// <summary>
        /// Initialize row dictionary for Death quantities table
        /// </summary>
        private Dictionary<string, string> NewRowDictionary()
        {
            Dictionary<string, string> dict = new Dictionary<string, string>();

            List<string> keys = new List<string>(deathQuantitiesConstants.DictionaryKeys);

            foreach (string key in keys)
                dict.Add(key, "");

            return dict;
        }

        /// <summary>
        /// Removes the old hen report form the hen performnce, creates the new report and updates report rectification.
        /// </summary>
        public async Task ApproveRectificationAsync(HenReport henReport, Guid ReportRectificationId, Guid previousHenReportId, HenReportRectificationDTO differences)
        {
            //To make sure all changes are saved at the end when all validations are passed.
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                // get needed entities
                ReportRectification reportRectification = reportRectificationService.Get(ReportRectificationId);
                HenReport previousHenReport = henReportService.GetAll()
                .Include(hr => hr.ShippingNotes).ThenInclude(sn => sn.MaterialBatchesShipped).ThenInclude(mb => mb.MaterialBatch)
                .Include(hr => hr.ShippingNotes).ThenInclude(sn => sn.MaterialsShipped)
                .FirstOrDefault(p => p.Id == previousHenReportId);

                // check hen batch
                HenBatch henBatch = null;
                try
                {
                    henBatch = henBatchService.GetFull(henReport.HenBatchId);
                }
                catch (NotFoundException)
                {
                    throw new ValidationException(null, new BusinessValidationResult<ShippingNote>(localizer[Lang.HenBatchNotFound]).UnsuccessfulValidations);
                }

                List<HenReportBaseStep> fixture = await BuildFixture(henReport, previousHenReport, differences, henBatch);
                // process shipping notes
                henReport.ShippingNotes = new List<ShippingNote>();

                foreach (HenReportBaseStep step in fixture)
                {
                    (BusinessValidationResult<ShippingNote> validationResult, ShippingNote shippingNote) = await step.ProcessShippingNote(henBatch, henReport, previousHenReport);
                    if (!validationResult.IsValid)
                        foreach (ValidationResponse<ShippingNote> v in validationResult.Validations)
                            throw new ValidationException(v.Validation);
                    if (shippingNote != null)
                        henReport.ShippingNotes.Add(shippingNote);
                }
                // creates new hen report
                henReport.HenBatchPerformanceId = previousHenReport.HenBatchPerformanceId;
                henReport.HenBatchPerformanceId = henBatchPerformanceBusinessLogic.RectifyHenBatchPerformance(henReport, differences);
                henReport.ReportEnum = ReportEnum.New;
                henReport.HenAmountFemale = henBatch.HenAmountFemale;
                henReport.HenAmountMale = henBatch.HenAmountMale;

                // set sector, farm and company id for filtering
                henReport.CompanyId = henBatch.CompanyId;
                henReport.FarmId = henBatch.FarmId;
                henReport.SectorId = henBatch.SectorId;

                await henReportService.CreateAsync(henReport);

                /// update henbatch values
                henBatch.DeadAccumulatedFemale += differences.DeadFemale;
                henBatch.DeadAccumulatedMale += differences.DeadMale;
                henBatch.DepopulateAccumulatedFemale += differences.DepopulateFemale;
                henBatch.DepopulateAccumulatedMale += differences.DepopulateMale;

                await henBatchService.UpdateAsync(henBatch);

                // rectified old hen report
                previousHenReport.ReportEnum = ReportEnum.Rectified;
                previousHenReport.RectificationHenReportId = henReport.Id;
                await henReportService.UpdateAsync(previousHenReport);

                // update report rectification
                reportRectification.ApplicationUserId = operationContext.GetUserId();
                reportRectification.AuthorizationResponseEnum = AuthorizationResponseEnum.Approved;
                await reportRectificationService.UpdateAsync(reportRectification);

            });
        }

        private HenReportBaseStep GetConsumedOrReturnStep(Container feedIntakeOrigin, string stepName, decimal quantity)
        {
            if (quantity > 0)
                return GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, feedIntake: quantity, ConsumedFoodStepName: stepName);
            else
                return GetStep<ReturnFoodStep>(feedIntakeOrigin: feedIntakeOrigin, feedIntake: Math.Abs(quantity), ConsumedFoodStepName: stepName);
        }
        private HenReportBaseStep GetConsumedOrReturnStepDifferentMaterialShipped(Container feedIntakeOrigin, Guid? materialBatchFemaleConsumedId, Guid? materialBatchMaleConsumedId, string stepName, decimal quantityFemale, decimal quantityMale)
        {
            MaterialBatchContainer materialBatchFemale = materialBatchFemaleConsumedId.HasValue
                ? containerService.GetAvailableMaterialBatches(feedIntakeOrigin.Id).First(mb => mb.MaterialBatchId == materialBatchFemaleConsumedId.Value)
                : null;

            MaterialBatchContainer materialBatchMale = materialBatchMaleConsumedId.HasValue
                ? containerService.GetAvailableMaterialBatches(feedIntakeOrigin.Id).First(mb => mb.MaterialBatchId == materialBatchMaleConsumedId.Value)
                : null;

            if (quantityFemale > 0 && quantityMale > 0)
                return GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatchFemale: materialBatchFemale, materialBatchMale: materialBatchMale, feedIntakeFemale: quantityFemale, feedIntakeMale: quantityMale, ConsumedFoodStepName: stepName);
            else
                return GetStep<ReturnFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatchFemale: materialBatchFemale, materialBatchMale: materialBatchMale, feedIntakeFemale: Math.Abs(quantityFemale), feedIntakeMale: Math.Abs(quantityMale), ConsumedFoodStepName: stepName);
        }
        private HenReportBaseStep GetConsumedOrReturnStepWithMaterialShipped(Container feedIntakeOrigin, Guid? materialBatchConsumedId, string stepName, decimal quantity)
        {
            MaterialBatchContainer materialBatch = containerService.GetAvailableMaterialBatches(feedIntakeOrigin.Id).FirstOrDefault(mb => mb.MaterialBatchId == materialBatchConsumedId);

            if (quantity > 0)
                return GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatch: materialBatch, feedIntake: quantity, ConsumedFoodStepName: stepName);
            else
                return GetStep<ReturnFoodStep>(feedIntakeOrigin: feedIntakeOrigin, materialBatch: materialBatch, feedIntake: Math.Abs(quantity), ConsumedFoodStepName: stepName);
        }

        private async Task<List<HenReportBaseStep>> BuildFixture(HenReport henReport, HenReport previousHenReport, HenReportRectificationDTO differences, HenBatch henBatch, bool justValidate = false)
        {
            int week = henBatchService.GetCurrentWeekNumberForDate(henBatch.Id, henReport.Date);

            List<HenReportBaseStep> fixture = new List<HenReportBaseStep>();

            bool hasNoOriginFemalePrevious = !previousHenReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodFemaleStep) && previousHenReport.FeedIntakeFemale != 0;
            bool hasNoOriginMalePrevious = !previousHenReport.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodMaleStep) && previousHenReport.FeedIntakeMale != 0;
            List<ShippingNote> shippingNotesBefore = new List<ShippingNote>();
            if (hasNoOriginFemalePrevious || hasNoOriginMalePrevious)
            {

                HenReport henReportBefore = henReportService.GetAllFullReportsBefore(previousHenReport.Id);
                if (henReportBefore != null)
                    shippingNotesBefore = shippingNoteService.GetAll(true).Where(sn => sn.HenReportId == henReportBefore.Id).ToList();
            }

            shippingNotesBefore.AddRange(shippingNoteService.GetAll(true)
               .Where(sn => sn.HenReportId == previousHenReport.Id).Distinct().ToList());

            ILookup<string, Container> containers = shippingNotesBefore
                .ToLookup(sn => sn.Name, sn => sn.Origin);

            // egg movements
            if (henBatch.HenStage == HenStage.Laying && differences.EggsDifferences.Any(ed => ed.Quantity != 0))
            {
                //if the number is positive, the eggs are produced normally, if the number is negative we have to adjust the number of eggs
                // check tenent configuration
                Guid tenantId = operationContext.GetUserTenantId().Value;
                IQueryable<TenantConfiguration> tenantConfigurations = tenantConfigurationService.GetAll().Where(tc => tc.TenantId == tenantId);
                // see if its necessary to send the eggs to the storage warehouse
                bool automaticallySentEggs = await tenantConfigurations.AnyAsync(t => t.TenantId == operationContext.GetUserTenantId() && t.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs && t.Value == "True");
                bool groupLayingEggsByMaterialTypeHatching = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeHatching && t.Value == "True");
                bool groupLayingEggsByMaterialTypeCommercial = tenantConfigurations.Any(t => t.TenantConfigurationEnum == TenantConfigurationEnum.GroupLayingEggsByMaterialTypeCommercial && t.Value == "True");
                henReport.ShippingNoteConciliations = new List<HenReportConciliation>();

                // send needed eggs
                if (differences.EggsDifferences.Any(ed => ed.Quantity > 0))
                {
                    List<HenReportEggsDTO> positiveEggs = new List<HenReportEggsDTO>();

                    // shipping note conciliation, according to egg type
                    foreach (HenReportEggsDTO egg in differences.EggsDifferences.Where(ed => ed.Quantity > 0))
                    {
                        (List<HenReportConciliation> shippingNoteConciliations, int remainingEggs) = ConciliateShippingNotes(henReport.HenBatchId, Guid.Parse(egg.EggId), egg.Quantity);
                        henReport.ShippingNoteConciliations.AddRange(shippingNoteConciliations);
                        if (remainingEggs > 0)
                            positiveEggs.Add(new HenReportEggsDTO()
                            {
                                EggId = egg.EggId,
                                Quantity = remainingEggs,
                                MaterialTypePath = egg.MaterialTypePath
                            });
                    }

                    // send eggs from the hen batch to the line
                    fixture.Add(GetStep<EggsToLineStep>(EggsDTO: positiveEggs, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));

                    if (automaticallySentEggs && !justValidate)
                        fixture.Add(GetStep<SendEggsStep>(EggsDTO: positiveEggs, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));
                }

                // if the number is negative remove eggs
                if (differences.EggsDifferences.Any(ed => ed.Quantity < 0))
                {
                    List<HenReportEggsDTO> negativeEggs = new List<HenReportEggsDTO>();
                    // revert conciliation
                    foreach (HenReportEggsDTO egg in differences.EggsDifferences.Where(ed => ed.Quantity < 0))
                    {
                        int remainingEggs = await AdjustConciliation(previousHenReport, Guid.Parse(egg.EggId), Math.Abs(egg.Quantity));
                        if (remainingEggs > 0)
                            negativeEggs.Add(new HenReportEggsDTO()
                            {
                                EggId = egg.EggId,
                                Quantity = remainingEggs,
                                MaterialTypePath = egg.MaterialTypePath
                            });
                    }

                    if (automaticallySentEggs)
                        fixture.Add(GetStep<ReturnEggsStep>(EggsDTO: negativeEggs, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));
                    else
                        fixture.Add(GetStep<RemoveEggsFromLineStep>(EggsDTO: negativeEggs, groupLayingEggsByMaterialTypeCommercial: groupLayingEggsByMaterialTypeCommercial, groupLayingEggsByMaterialTypeHatching: groupLayingEggsByMaterialTypeHatching));
                }
            }

            // hens movements
            int totalDeadFemale = differences.DepopulateFemale + differences.DeadFemale;
            int totalDeadMale = differences.DepopulateMale + differences.DeadMale;

            if (totalDeadFemale > 0 || totalDeadMale > 0)
                fixture.Add(GetStep<RemoveHensStep>(totalDeadMale: totalDeadMale, totalDeadFemale: totalDeadFemale));
            if (totalDeadFemale < 0 || totalDeadMale < 0)
            {
                int absTotalDeadMale = totalDeadMale > 0 ? 0 : Math.Abs(totalDeadMale);
                int absTotalDeadFemale = totalDeadFemale > 0 ? 0 : Math.Abs(totalDeadFemale);
                fixture.Add(GetStep<ReturnHensStep>(totalDeadMale: absTotalDeadMale, totalDeadFemale: absTotalDeadFemale));
            }

            // food movements
            // if there was a consumed food shipping note and same materialshipped, return or send food from the same origin

            IQueryable<ShippingNote> shippingNotes = shippingNoteService.GetAll(true).Where(sn => sn.HenReportId == previousHenReport.Id);

            if (await shippingNotes.AnyAsync(sn => sn.Name == HenReportSteps.ConsumedFoodStep && sn.MaterialsShipped.Count == 1) && differences.FeedIntakeFemale + differences.FeedIntakeMale != 0)
                fixture.Add(GetConsumedOrReturnStep(containers[HenReportSteps.ConsumedFoodStep].First(), HenReportSteps.ConsumedFoodStep, differences.FeedIntakeFemale + differences.FeedIntakeMale));

            else if (await shippingNotes.AnyAsync(sn => sn.Name == HenReportSteps.ConsumedFoodStep) && (differences.FeedIntakeFemale != 0 || differences.FeedIntakeMale != 0))
            {
                if ((differences.FeedIntakeFemale > 0 && differences.FeedIntakeMale > 0) || (differences.FeedIntakeFemale < 0 && differences.FeedIntakeMale < 0))
                    fixture.Add(GetConsumedOrReturnStepDifferentMaterialShipped(containers[HenReportSteps.ConsumedFoodStep].First(), differences.MaterialBatchFemaleConsumedId, differences.MaterialBatchMaleConsumedId, HenReportSteps.ConsumedFoodStep, differences.FeedIntakeFemale, differences.FeedIntakeMale));
                else
                {
                    if (differences.FeedIntakeFemale != 0)
                        fixture.Add(GetConsumedOrReturnStepWithMaterialShipped(containers[HenReportSteps.ConsumedFoodStep].First(), differences.MaterialBatchFemaleConsumedId, HenReportSteps.ConsumedFoodStep, differences.FeedIntakeFemale));
                    if (differences.FeedIntakeMale != 0)
                        fixture.Add(GetConsumedOrReturnStepWithMaterialShipped(containers[HenReportSteps.ConsumedFoodStep].First(), differences.MaterialBatchMaleConsumedId, HenReportSteps.ConsumedFoodStep, differences.FeedIntakeMale));
                }
            }
            // if there was two consumed shipping notes only return or send when the difference is bigger than cero
            else if (await shippingNotes.AnyAsync(sn => sn.Name == HenReportSteps.ConsumedFoodFemaleStep || sn.Name == HenReportSteps.ConsumedFoodMaleStep))
            {
                if (differences.FeedIntakeFemale != 0)
                    fixture.Add(GetConsumedOrReturnStep(containers[HenReportSteps.ConsumedFoodFemaleStep].First(), HenReportSteps.ConsumedFoodFemaleStep, differences.FeedIntakeFemale));
                if (differences.FeedIntakeMale != 0)
                    fixture.Add(GetConsumedOrReturnStep(containers[HenReportSteps.ConsumedFoodMaleStep].First(), HenReportSteps.ConsumedFoodMaleStep, differences.FeedIntakeMale));
            }
            else if (differences.FeedIntakeFemale + differences.FeedIntakeMale > 0)
            {
                // if we need to send food for the first time find the origin
                Container feedIntakeOrigin = GetFeedIntakeOrigins(henBatch.Id).FirstOrDefault();

                if (feedIntakeOrigin == null)
                    throw new ValidationException(null, new BusinessValidationResult<ShippingNote>(localizer[Lang.FeedIntakeOriginNotFound]).UnsuccessfulValidations);

                fixture.Add(GetStep<ConsumedFoodStep>(feedIntakeOrigin: feedIntakeOrigin, feedIntake: differences.FeedIntakeFemale + differences.FeedIntakeMale, ConsumedFoodStepName: HenReportSteps.ConsumedFoodStep));
            }
            else if (differences.FeedIntakeFemale + differences.FeedIntakeMale < 0) // can not return food if there wasent food sent
                throw new ValidationException(null, new BusinessValidationResult<ShippingNote>(localizer[Lang.FeedIntakeNotFound]).UnsuccessfulValidations);

            return fixture;
        }

        /// <summary>
        ///  function to adjust egg conciliation
        /// </summary>
        private async Task<int> AdjustConciliation(HenReport henReport, Guid eggId, int quantity)
        {
            HenReportConciliation[] conciliations = await shippingNoteConciliationService.GetAll(true)
                .Where(snc => snc.MaterialId == eggId && snc.HenReports.Any(hr => hr.HenReportId == henReport.Id))
                .SelectMany(snc => snc.HenReports)
                .ToArrayAsync();

            foreach (HenReportConciliation sn in conciliations)
                if (quantity > 0)
                {
                    if (quantity >= sn.ConciledQuantity)
                    {
                        quantity -= Convert.ToInt32(sn.ConciledQuantity);
                        sn.ShippingNoteConciliation.RemainingQuantity += sn.ConciledQuantity;
                        await shippingNoteConciliationService.UpdateAsync(sn.ShippingNoteConciliation);
                    }
                    else
                    {
                        sn.ShippingNoteConciliation.RemainingQuantity += quantity;
                        await shippingNoteConciliationService.UpdateAsync(sn.ShippingNoteConciliation);
                        quantity = 0;
                    }
                }
            return quantity;
        }

        /// <inheritdoc/>
        public async Task Import(IFormFile file)
        {
            string henBatchCode = file.FileName.Split(' ').Last().Split('.').First();
            HenReportBulkLoadEvent @event = await CreateOrUpdateEvent(henBatchCode);

            IgniteFile uploadedFile = await this.fileManagerService.Upload(file);
            await CreateOrUpdateFile(@event.Id, uploadedFile);
        }

        /// <inheritdoc/>
        public async Task ProcessFiles()
        {
            SearaExcelHelper excelHelper = new SearaExcelHelper(localizer, configuration, externalServiceService, henBatchService, materialService, fileManagerService);
            HenReportBulkLoadEvent[] events = await eventService.GetAll().Where(e => e.Status == HenReportBulkLoadStatusEnum.Pending).ToArrayAsync();

            foreach (HenReportBulkLoadEvent @event in events)
            {
                string message = string.Empty;

                try
                {
                    @event.Status = HenReportBulkLoadStatusEnum.InProgress;
                    await eventService.UpdateAsync(@event);

                    IgniteFile file = await fileService.GetAll().Where(f => f.TenantDependentEntityId == @event.Id).Select(f => f.File).FirstAsync();

                    IDictionary<DateTime, (List<SearaExcelHelper.WarehouseHenReportsDTO> warehouseReports, List<BirdMovementDTO> birdMovements)> reportsByDate =
                        await excelHelper.Import(file);

                    foreach (KeyValuePair<DateTime, (List<WarehouseHenReportsDTO> warehouseReports, List<BirdMovementDTO> birdMovements)> date in reportsByDate.Where(r => @event.LastSuccessfulDate == default || r.Key > @event.LastSuccessfulDate))
                    {
                        await unitOfWork.ExecuteAsTransactionAsync(async () =>
                        {
                            foreach (BirdMovementDTO movement in date.Value.birdMovements)
                            {
                                await henBatchBusinessLogic.MoveBirds(movement);
                            }

                            foreach (WarehouseHenReportsDTO warehouseReport in date.Value.warehouseReports)
                            {
                                foreach (SpikingReceptionrReportDTO report in warehouseReport.SpikingReceptionReports)
                                {
                                    await materialReceptionReportBusinessLogic.CreateMaterialReceptionReport(report.SpikingReceptionReport, report.Destination, AreaEnum.Laying, report.ShippingNoteDataDTO);
                                }

                                await CreateWarehouseHenReports(warehouseReport.HenReportDTOs, warehouseReport.GeneralReport, warehouseReport.EggsDTO);
                            }

                            @event.LastSuccessfulDate = date.Key;
                            await eventService.UpdateAsync(@event);
                        });
                    }

                    @event.Status = HenReportBulkLoadStatusEnum.Successful;
                    message = localizer[Lang.SuccessfulMessage];
                }
                catch (Exception e)
                {
                    @event.Status = HenReportBulkLoadStatusEnum.WithErrors;
                    message = e.Message;
                }

                if (string.IsNullOrEmpty(@event.Description)) @event.Description = $"{DateTime.Now} {message}";
                else @event.Description += $"{Environment.NewLine}{DateTime.Now} {message}";

                await eventService.UpdateAsync(@event);

            }
        }

        private async Task CreateOrUpdateFile(Guid eventId, IgniteFile file)
        {
            IgniteFile igniteFile = await igniteFileService.GetAll().FirstOrDefaultAsync(f => f.FileId == file.FileId);

            if (igniteFile == default)
            {
                await igniteFileService.CreateAsync(file);
                igniteFile = file;
            }

            TenantDependentEntityFile tenantDependentEntityFile = await fileService.GetAll()
                .FirstOrDefaultAsync(f => f.TenantDependentEntityId == eventId);

            if (tenantDependentEntityFile != default)
            {
                tenantDependentEntityFile.FileId = igniteFile.Id;
                await fileService.UpdateAsync(tenantDependentEntityFile);
            }


            else
            {
                tenantDependentEntityFile = new TenantDependentEntityFile() { Id = Guid.NewGuid(), TenantDependentEntityId = eventId, FileId = igniteFile.Id };
                await fileService.CreateAsync(tenantDependentEntityFile);
            }
        }

        private async Task<HenReportBulkLoadEvent> CreateOrUpdateEvent(string henBatchCode)
        {
            HenReportBulkLoadEvent @event = await eventService.GetAll().FirstOrDefaultAsync(e => e.HenBatch.Code == henBatchCode);

            if (@event == default)
            {
                Guid henBatch = await henBatchService.GetAll().Where(hb => hb.Code == henBatchCode && !hb.ParentId.HasValue).Select(hb => hb.Id).FirstOrDefaultAsync();

                if (henBatch == default) throw new ValidationException(localizer.GetString(Lang.FileNameFormatEx, henBatchCode));

                @event = new HenReportBulkLoadEvent()
                {
                    UserId = operationContext.GetUserId(),
                    EntityType = EntityTypeEnum.HenReport,
                    HenBatchId = henBatch,
                    Action = EventActionEnum.Create,
                    Status = HenReportBulkLoadStatusEnum.Pending,
                };

                await eventService.CreateAsync(@event);
            }
            else
            {
                @event.Status = HenReportBulkLoadStatusEnum.Pending;
                await eventService.UpdateAsync(@event);
            }

            return @event;
        }

        /// <summary>
        /// Create warehouse reports async
        /// </summary>
        public async Task<IEnumerable<Guid>> CreateWarehouseHenReportsAsync(IEnumerable<Guid> reports, List<CreateFromWarehouseAPI> createFromWarehouseAPI)
        {
            //To make sure all changes are saved at the end when all validations are passed.
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                foreach (CreateFromWarehouseAPI dto in createFromWarehouseAPI)
                {
                    try
                    {
                        HenReport generalReport = dto.GeneralReportDTO.ToEntity();

                        List<HenBatchHenReportDTO> henReportDTOs = CreateEntity(dto).ToList();

                        List<HenReportEggsDTO> henReportEggsDTO = dto.EggQuantityDTO != null && dto.EggQuantityDTO.Any() ?
                                    dto.EggQuantityDTO.Select(eq => new HenReportEggsDTO(eq)).ToList() :
                                    new List<HenReportEggsDTO>();

                        reports = reports.Concat(await CreateWarehouseHenReports(henReportDTOs, generalReport, henReportEggsDTO));
                    }
                    catch (ValidationException ex)
                    {
                        if (ex.Errors.Any())
                        {
                            IDictionary<string, string> errors = new Dictionary<string, string>();
                            foreach (KeyValuePair<string, string> error in ex.Errors)
                                errors.Add(dto.GeneralReportDTO.Date.ToString(), error.Value);
                            throw new ValidationException("", errors);

                        }
                        throw new ValidationException(dto.GeneralReportDTO.Date.ToString(), ex.Message);
                    }
                }
            });
            return reports;
        }

        /// <summary>
        /// Creates all the hen reports from a warehouse and returns the Ids of the ones that have dead amounts declared
        /// </summary>
        public async Task<IEnumerable<Guid>> CreateWarehouseHenReports(List<HenBatchHenReportDTO> henReportDTOs, HenReport generalReport, List<HenReportEggsDTO> eggsDTO)
        {
            List<HenReport> reports = new List<HenReport>();
            Guid? henBatchId = default;
            DateTime? henReportDate = default;

            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            return reports
                .Select(hr => hr.Id);

            async Task AsyncLogic()
            {
                // build hen reports
                int totalFemaleHens = henReportDTOs.Sum(dto => dto.HenAmountFemale);

                Material[] eggMaterials = new Material[eggsDTO.Count()];
                if (eggsDTO.Any())
                {
                    IEnumerable<Guid> eggMaterialsIds = eggsDTO.Select(e => Guid.Parse(e.EggId));
                    eggMaterials = await materialService.GetAll().Include(m => m.MaterialType).Where(m => eggMaterialsIds.Contains(m.Id)).ToArrayAsync();
                }

                foreach (HenBatchHenReportDTO dto in henReportDTOs)
                {
                    HenReport currentReport = new HenReport()
                    {
                        Date = generalReport.Date,
                        DeadFemale = dto.DeadFemale ?? 0,
                        DeadMale = dto.DeadMale ?? 0,
                        DepopulateFemale = dto.DepopulateFemale ?? 0,
                        DepopulateMale = dto.DepopulateMale ?? 0,
                        FeedIntakeFemale = dto.FeedIntakeDTO != null ? (dto.FeedIntakeDTO.FeedIntakeFemale != null && dto.FeedIntakeDTO.FeedIntakeFemale != 0 ?
                                 dto.FeedIntakeDTO.FeedIntakeFemale.Value : (dto.FeedIntakeDTO.FeedIntake.HasValue
                                ? dto.FeedIntakeDTO.FeedIntake.Value * dto.HenAmountFemale / (dto.HenAmountFemale + dto.HenAmountMale)
                                : 0)) : 0,
                        FeedIntakeMale = dto.FeedIntakeDTO != null ? (dto.FeedIntakeDTO.FeedIntakeMale != null && dto.FeedIntakeDTO.FeedIntakeMale != 0 ?
                                 dto.FeedIntakeDTO.FeedIntakeMale.Value : (dto.FeedIntakeDTO.FeedIntake.HasValue
                                ? dto.FeedIntakeDTO.FeedIntake.Value * dto.HenAmountMale / (dto.HenAmountFemale + dto.HenAmountMale)
                                : 0)) : 0,
                        HenBatchId = dto.HenBatchId,
                        Humidity = generalReport.Humidity,
                        MaxTemp = generalReport.MaxTemp,
                        MinTemp = generalReport.MinTemp,
                        ToCageFemale = dto.ToCageFemale ?? 0,
                        ToCageMale = dto.ToCageMale ?? 0,
                        ToFloorFemale = dto.ToFloorFemale ?? 0,
                        ToFloorMale = dto.ToFloorMale ?? 0,
                        WaterChlorineConcentration = generalReport.WaterChlorineConcentration,
                        WaterConsumption = generalReport.WaterConsumption,
                        WaterPh = generalReport.WaterPh,
                        WaterPillQuantity = generalReport.WaterPillQuantity,
                        HenAmountFemale = dto.HenAmountFemale,
                        HenAmountMale = dto.HenAmountMale,
                        ClassifiedEggs = new List<HenReportClassifiedEgg>(),
                        BrokenEggs = totalFemaleHens != 0 ? (uint)(generalReport.BrokenEggs * dto.HenAmountFemale / totalFemaleHens) : 0,
                        UploadOrigin = generalReport.UploadOrigin
                    };
                    // add egg quantity according to female hen amount
                    if (currentReport.HenAmountFemale > 0 && eggsDTO.Any())
                        currentReport.ClassifiedEggs.AddRange(eggsDTO.Select(e =>
                            new HenReportClassifiedEgg()
                            {
                                MaterialId = Guid.Parse(e.EggId),
                                Material = eggMaterials.First(m => m.Id == Guid.Parse(e.EggId)),
                                Quantity = e.Quantity * currentReport.HenAmountFemale / totalFemaleHens
                            }
                        ));
                    if (dto.CasualitiesAndDepopulationsDTO != null)
                        currentReport = dto.CasualitiesAndDepopulationsDTO.FillCasualtiesAndDepopulations(currentReport);

                    reports.Add(currentReport);
                }

                if (totalFemaleHens > 0)
                {
                    // if there are missing eggs add them to a random report
                    int numberOfDistributions = reports.Where(r => r.HenAmountFemale != 0).Count();
                    int randomDistribution = new Random().Next(0, numberOfDistributions - 1);
                    foreach (HenReportEggsDTO egg in eggsDTO)
                    {
                        randomDistribution = new Random().Next(0, numberOfDistributions - 1);
                        reports.Where(r => r.HenAmountFemale != 0).ToList()[randomDistribution].ClassifiedEggs
                                .FirstOrDefault(ce => ce.MaterialId == Guid.Parse(egg.EggId)).Quantity
                        += GetEggsRest(egg.Quantity,
                            reports.SelectMany(r => r.ClassifiedEggs)
                                .Where(ce => ce.MaterialId == Guid.Parse(egg.EggId)).Sum(r => r.Quantity));
                    }
                    // add missing broken eggs
                    reports.Where(r => r.HenAmountFemale != 0).ToList()[randomDistribution].BrokenEggs
                        += (uint)GetEggsRest((int)generalReport.BrokenEggs, (int)reports.Sum(r => r.BrokenEggs));
                }

                List<HenBatch> henBatches = await henBatchService.GetAll().Include(hb => hb.Line).Where(hb => reports.Select(r => r.HenBatchId).Contains(hb.Id)).ToListAsync();

                foreach (HenReport hr in reports)
                {
                    henBatchId = hr.HenBatchId;
                    henReportDate = hr.Date;

                    uint totalEggs = hr.BrokenEggs + hr.CommercialEggs + hr.HatchableEggs + (uint)hr.ClassifiedEggs.Sum(ce => ce.Quantity);
                    if (totalEggs > 0)
                    {
                        HenBatch henBatch = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == henBatchId.Value);
                        DateTime? firstProductionDate = henBatch.FirstProductionDate;
                        if (!firstProductionDate.HasValue)
                        {
                            henBatch.FirstProductionDate = hr.Date;
                            await henBatchService.UpdateAsync(henBatch);
                        }
                    }

                    // set total eggs
                    hr.CommercialEggs = (uint)hr.ClassifiedEggs.Where(e => e.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado)).Sum(e => e.Quantity);
                    hr.HatchableEggs = (uint)hr.ClassifiedEggs.Where(e => e.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado)).Sum(e => e.Quantity);

                    // create hen reports
                    HenBatchHenReportDTO currentDTO = henReportDTOs.FirstOrDefault(dto => dto.HenBatchId == hr.HenBatchId);

                    await CreateAndAddToHenBatchPerformanceAsync(hr, currentDTO.FeedIntakeDTO);
                }

                await henWarehouseBusinessLogic.UpdateWarehouseConsumption(henBatches.First().Line.WarehouseId);
            }
        }

        public async Task<HenReportByCategoryPage> GetPageByCategory(DateTime from, DateTime? to, string farmCode, string categoryCode, int page = 0, int pageSize = 20)
        {
            Farm farm = farmService.GetAll(true).Where(f => f.Code == farmCode).FirstOrDefault();
            HenReportByCategoryPage responseDTO = new HenReportByCategoryPage();
            if (farm is null)
                return responseDTO;
            IQueryable<HenBatch> henBatches = henBatchService.GetAll(true, false).Where(hb => hb.Active && hb.FarmId == farm.Id);

            // prioritize children lotes
            if (henBatches.Any())
                henBatches = henBatches.Where(hb => hb.ParentId.HasValue);

            // if not any data by children then prioritize parent lotes
            if (!henBatches.Any())
                henBatches = henBatchService.GetAll(true, false).Where(hb => !hb.ParentId.HasValue);

            if (!henBatches.Any())
                return responseDTO;

            List<Guid> henBatchIds = await henBatches.Select(a => a.Id).ToListAsync();

            IQueryable<HenReport> henReports = henReportService.GetAll()
                .Include(hr => hr.ClassifiedEggs).ThenInclude(hr => hr.Material)
                .Include(hr => hr.ClassifiedEggs).ThenInclude(hr => hr.HenReport).ThenInclude(hr => hr.HenBatch)
                .Include(hr => hr.RectificationHenReport)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Farm)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse)
                .Where(hr => hr.Date.Date >= from.Date && hr.FarmId == farm.Id && hr.ReportEnum == ReportEnum.New);

            if (to.HasValue)
                henReports = henReports.Where(hr => hr.Date.Date <= to.Value.Date);
            if (henBatchIds.Any())
                henReports = henReports.Where(hr => henBatchIds.Contains(hr.HenBatchId));

            var henReportsByCategory = new List<HenReportByCategoryItem>();
            int total = 0;
            int eggsTotal = 0;
            if (categoryCode == "09")
            {
                foreach (HenReport henreport in henReports)
                {
                    if (henreport.BrokenEggs > 0)
                    {
                        var eggs = (int?)henreport.TotalEggs;
                        eggsTotal += eggs ?? 0;
                        HenReportByCategoryItem broken = new HenReportByCategoryItem();
                        broken.ProductionDate = henreport.Date;
                        broken.HenbatchName = $"{henreport.HenBatch.Farm.Code + "-" + henreport.HenBatch.Code + "-" + henreport.HenBatch.Line.Warehouse.Code + "-" + henreport.HenBatch.Line.Code}";
                        broken.CategoryCode = "09";
                        broken.CategoryName = "Eliminado/Quebrado";
                        broken.Quantity = Convert.ToInt32(henreport.BrokenEggs);
                        henReportsByCategory.Add(broken);
                    }
                }
                total = henReportsByCategory.Count();
            }
            else
            {
                EggCategoryCodes = EggCategoryCodes.Where(x => x != "09").ToList();
                IQueryable<HenReportClassifiedEgg> henReportClassifiedEggs = henReports
                    .SelectMany(hr => hr.ClassifiedEggs)
                    .Include(hrce => hrce.HenReport).ThenInclude(hr => hr.HenBatch).ThenInclude(hb => hb.Farm)
                    .Include(hrce => hrce.HenReport).ThenInclude(hr => hr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse)
                    .Where(hrce => EggCategoryCodes.Contains(hrce.Material.InternalId) && hrce.Quantity > 0);

                total = henReportClassifiedEggs.Count();
                eggsTotal = henReportClassifiedEggs.Sum(x => x.Quantity);
                henReportsByCategory = await henReportClassifiedEggs.Skip((page - 1) * pageSize).Take(pageSize).Select(hrce => new HenReportByCategoryItem(hrce)).ToListAsync();
            }


            return new HenReportByCategoryPage()
            {
                PageSize = pageSize,
                Total = total,
                Items = henReportsByCategory,
                EggsTotal = eggsTotal,
            };
        }

        private static List<string> EggCategoryCodes = new List<string>()
        {
            "01", // Ninho Limpo
            "02", // Ninho Sujo
            "03", // Cama
            "04", // 2 Gemas
            "05", // Pequeno Grj
            "06", // Defeituoso/Deformado
            "07", // Sujo/Rolados
            "08", // Trincado Grj
            "09", // Eliminado Quebrado
            "11", // Casca Fina
        };

        private HenBatchHenReportDTO HenBatchHenReportDTO(FeedIntakeDTO feedIntakeDTO, BirdsDTO birdsDTO, CasualtiesAndDepopulations casualtiesAndDepopulationsDTO)
        {
            HenBatch henBatch = henBatchService.GetAll().Where(hb => hb.Id == birdsDTO.HenBatchId).First();
            int henAmount = henBatch.HenAmountFemale + henBatch.HenAmountMale;
            int henAmountFemale = henBatch.HenAmountFemale;
            int henAmountMale = henBatch.HenAmountMale;
            HenBatchHenReportDTO henBatchHenReportDTO = new HenBatchHenReportDTO();
            if (feedIntakeDTO != null)
            {
                henBatchHenReportDTO.FeedIntakeDTO = new HenReportFeedIntakeDTO()
                {
                    HenBatchId = feedIntakeDTO.HenBatchId,
                    FeedIntakeOriginId = string.IsNullOrEmpty(feedIntakeDTO.Silo)
                                   ? (string.Equals(feedIntakeDTO.FemaleSilo, feedIntakeDTO.MaleSilo) && !String.IsNullOrEmpty(feedIntakeDTO.FemaleSilo)
                                       ? TransformToGuid(feedIntakeDTO.FemaleSilo)
                                       : null)
                                    : TransformToGuid(feedIntakeDTO.Silo),
                    FeedIntakeFemaleOriginId = !string.IsNullOrEmpty(feedIntakeDTO.FemaleSilo) ? TransformToGuid(feedIntakeDTO.FemaleSilo) : null,
                    FeedIntakeMaleOriginId = !string.IsNullOrEmpty(feedIntakeDTO.MaleSilo) ? TransformToGuid(feedIntakeDTO.MaleSilo) : null,
                    MaterialBatchConsumedId = null,
                    MaterialBatchFemaleConsumedId = null,
                    MaterialBatchMaleConsumedId = null,
                    FeedIntake = !string.IsNullOrEmpty(feedIntakeDTO.Unit) && feedIntakeDTO.Unit == CapacityUnits.GAD.ToString()
                                                        ? capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, feedIntakeDTO.Quantity) * henAmount
                                                        : feedIntakeDTO.Quantity,
                    FeedIntakeFemale = !string.IsNullOrEmpty(feedIntakeDTO.FemaleUnit) && feedIntakeDTO.FemaleUnit == CapacityUnits.GAD.ToString()
                                                        ? capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, feedIntakeDTO.FemaleQuantity) * henAmountFemale
                                                        : feedIntakeDTO.FemaleQuantity,
                    FeedIntakeMale = !string.IsNullOrEmpty(feedIntakeDTO.MaleUnit) && feedIntakeDTO.MaleUnit == CapacityUnits.GAD.ToString()
                                                        ? capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, feedIntakeDTO.MaleQuantity) * henAmountMale
                                                        : feedIntakeDTO.MaleQuantity
                };
            }

            if (casualtiesAndDepopulationsDTO != null)
                henBatchHenReportDTO.CasualitiesAndDepopulationsDTO = casualtiesAndDepopulationsDTO;

            henBatchHenReportDTO.HenBatchId = birdsDTO != null ? birdsDTO.HenBatchId : feedIntakeDTO.HenBatchId;
            henBatchHenReportDTO.DeadFemale = birdsDTO != null ? birdsDTO.DeadFemale : 0;
            henBatchHenReportDTO.DeadMale = birdsDTO != null ? birdsDTO.DeadMale : 0;
            henBatchHenReportDTO.DepopulateFemale = birdsDTO != null ? birdsDTO.DepopulateFemale : 0;
            henBatchHenReportDTO.DepopulateMale = birdsDTO != null ? birdsDTO.DepopulateMale : 0;

            return henBatchHenReportDTO;
        }

        private Guid? TransformToGuid(string id)
        {
            return id != null ? new Guid(id) : (Guid?)null;
        }

        private int GetEggsRest(int total, int sum)
        {
            if (total - sum > 0)
                return total - sum;
            return 0;
        }

        /// <summary>
        /// Function to create and approve an adjustment report when the user has the approver role
        /// or when the original report is the latest
        /// </summary>
        public async Task CreateAndApproveRectificationAsync(Guid henReportId, HenReport newHenReport, HenReportRectificationDTO differences, Draft draft)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                // get needed entities
                HenReport previousHenReport = await henReportService.GetAll()
            .Include(hr => hr.ShippingNotes).ThenInclude(sn => sn.MaterialBatchesShipped).ThenInclude(mbs => mbs.MaterialBatch)
            .FirstAsync(hr => hr.Id == henReportId);

                // check hen batch
                HenBatch henBatch = henBatchService.GetFull(previousHenReport.HenBatchId);

                List<HenReportBaseStep> fixture = await BuildFixture(newHenReport, previousHenReport, differences, henBatch);
                // process shipping notes
                newHenReport.ShippingNotes = new List<ShippingNote>();

                foreach (HenReportBaseStep step in fixture)
                {
                    (BusinessValidationResult<ShippingNote> validationResult, ShippingNote shippingNote) = await step.ProcessShippingNote(henBatch, newHenReport, previousHenReport, false);
                    if (!validationResult.IsValid)
                        foreach (ValidationResponse<ShippingNote> v in validationResult.Validations)
                            throw new ValidationException(v.Validation);
                    if (shippingNote != null)
                        newHenReport.ShippingNotes.Add(shippingNote);
                }

                ReportRectification rectification = new ReportRectification()
                {
                    AuthorizationResponseEnum = AuthorizationResponseEnum.Approved,
                    Draft = draft,
                    DraftId = draft.Id,
                    CompanyId = previousHenReport.CompanyId,
                    FarmId = previousHenReport.FarmId,
                    SectorId = previousHenReport.SectorId,
                    RectificationHenReport = previousHenReport,
                    RectificationHenReportId = previousHenReport.Id,
                    ApplicationUserId = operationContext.GetUserId(),
                    Action = RectificationActionEnum.Modify
                };

                await this.draftService.CreateOrUpdate(draft);
                await this.reportRectificationService.CreateAsync(rectification);

                // creates new hen report
                newHenReport.HenBatchPerformanceId = previousHenReport.HenBatchPerformanceId;
                newHenReport.HenBatchPerformanceId = henBatchPerformanceBusinessLogic.RectifyHenBatchPerformance(newHenReport, differences);
                newHenReport.ReportEnum = ReportEnum.New;
                newHenReport.HenAmountFemale = henBatch.HenAmountFemale;
                newHenReport.HenAmountMale = henBatch.HenAmountMale;

                // set sector, farm and company id for filtering
                newHenReport.CompanyId = henBatch.CompanyId;
                newHenReport.FarmId = henBatch.FarmId;
                newHenReport.SectorId = henBatch.SectorId;

                await henReportService.CreateAsync(newHenReport);

                // if the dead or depopulated amount was modified, we need to change the accum values of child and parent henbatch
                HenBatch parent = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == henBatch.ParentId);
                if (differences.DeadFemale != 0)
                {
                    henBatch.DeadAccumulatedFemale += differences.DeadFemale;
                    if (parent != null)
                        parent.DeadAccumulatedFemale += differences.DeadFemale;
                }
                if (differences.DepopulateFemale != 0)
                {
                    henBatch.DepopulateAccumulatedFemale += differences.DepopulateFemale;
                    if (parent != null)
                        parent.DepopulateAccumulatedFemale += differences.DepopulateFemale;
                }
                if (differences.DeadMale != 0)
                {
                    henBatch.DeadAccumulatedMale += differences.DeadMale;
                    if (parent != null)
                        parent.DeadAccumulatedMale += differences.DeadMale;
                }
                if (differences.DepopulateMale != 0)
                {
                    henBatch.DepopulateAccumulatedMale += differences.DepopulateMale;
                    if (parent != null)
                        parent.DepopulateAccumulatedMale += differences.DepopulateMale;
                }
                // updates hen batch
                await henBatchService.UpdateAsync(henBatch);
                if (parent != null)
                    await henBatchService.UpdateAsync(parent);

                // rectified old hen report
                previousHenReport.ReportEnum = ReportEnum.Rectified;
                previousHenReport.RectificationHenReportId = newHenReport.Id;
                await henReportService.UpdateAsync(previousHenReport);
            });
        }

        /// <summary>
        /// gets the last hen report for the corresponding hen batch and see if it is the same
        /// report that is being rectified
        /// </summary>
        public bool LastReport(Guid henReportId, Guid henBatchId)
        {
            IQueryable<HenReport> reports = henReportService.GetAll()
                .Where(hr => hr.HenBatchId == henBatchId);

            HenReport currentReport = reports.FirstOrDefault(hr => hr.Id == henReportId);
            HenReport lastReport = reports
                                    .OrderByDescending(hr => hr.Date)
                                    .FirstOrDefault();

            if (currentReport == null)
                return false;
            else
                return currentReport.Equals(lastReport);
        }

        /// <summary>
        /// Creates a Report rectification to delete te hen report.
        /// If the user has the corresponding roles the function deletes the hen report
        /// </summary>
        public async Task CreateRequestToDelete(HenReport henReport, bool createAndApprove)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                // validate
                businessValidationManager
              .BuildValidation<MovementsBusinessLogicValidations>()
              .Validate(henReport);

                if (!businessValidationManager.Succeeded)
                {
                    BusinessValidationResult<HenReport> result = new BusinessValidationResult<HenReport>(businessValidationManager);
                    throw new ValidationException(null, result.UnsuccessfulValidations);
                }

                ReportRectification rectification = new ReportRectification()
                {
                    Action = RectificationActionEnum.Delete,
                    ApplicationUserId = createAndApprove
                    ? operationContext.GetUserId()
                    : (Guid?)null,
                    AuthorizationResponseEnum = createAndApprove
                    ? AuthorizationResponseEnum.Approved
                    : AuthorizationResponseEnum.Pending,
                    CompanyId = henReport.CompanyId,
                    FarmId = henReport.FarmId,
                    RectificationHenReportId = henReport.Id,
                    SectorId = henReport.SectorId,
                };
                await reportRectificationService.CreateAsync(rectification);

                await DeleteAndRemoveFromHenBatchPerformanceAsync(henReport.Id, createAndApprove);
            });
        }

        /// <summary>
        /// Approves the delete request and deletes the hen report
        /// </summary>
        public async Task ApproveDeleteRequest(Guid rectificationId, Guid henReportId)
        {
            await unitOfWork.ExecuteAsTransactionAsync(async () =>
            {
                // validate
                businessValidationManager
              .BuildValidation<MovementsBusinessLogicValidations>()
              .Validate(henReportService.Get(henReportId));

                if (!businessValidationManager.Succeeded)
                {
                    BusinessValidationResult<HenReport> result = new BusinessValidationResult<HenReport>(businessValidationManager);
                    throw new ValidationException(null, result.UnsuccessfulValidations);
                }

                ReportRectification rectification = reportRectificationService.Get(rectificationId);
                rectification.ApplicationUserId = operationContext.GetUserId();
                rectification.AuthorizationResponseEnum = AuthorizationResponseEnum.Approved;

                await reportRectificationService.UpdateAsync(rectification);
                await DeleteAndRemoveFromHenBatchPerformanceAsync(henReportId, true);
            });
        }

        /// <summary>
        ///  Convert GAD to kilograms
        /// </summary>
        public decimal CalculateGAD(string value, Guid measure, Guid henBatchId, bool? female = null)
        {
            decimal.TryParse(value, out decimal number);

            int henAmount = henBatchService.GetAll()
                .Where(hb => hb.Id == henBatchId)
                .Select(hb => female.HasValue
                    ? female.Value ? hb.HenAmountFemale : hb.HenAmountMale
                    : hb.HenAmountFemale + hb.HenAmountMale)
                .First();

            decimal calculatedValue = measure == CapacityUnits.GAD
                ? capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, number) * henAmount
                : capacityUnitBusinessLogic.ConvertValue(measure, CapacityUnits.Grams, number) / henAmount;

            return decimal.Round(calculatedValue, 4);
        }

        /// <summary>
        /// Returns data to notify for exceeding threshold parameters
        /// </summary>
        public async Task<ParametersThatExceedThresholdDTO> GetDataThatExceedsThresholdParametersAsync(DateTime lastSuccessfulJobDate)
        {
            DateTime now = DateTime.Now;
            DateTime yesterday = now.AddDays(-1);
            // It is observed that this parameter launches future hours, in this way we manage to manipulate it
            DateTime deadline = now < lastSuccessfulJobDate ? lastSuccessfulJobDate.AddHours(-3) : lastSuccessfulJobDate;

            IQueryable<HenReport> lastTwoDaysHenReports = henReportService.GetAll()
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Farm)
                .Where(hr => yesterday.Date <= hr.CreatedDate.Date && now >= hr.CreatedDate &&
                    (hr.HenBatch.Farm.SupervisorId.HasValue || hr.HenBatch.Farm.SanitarianId.HasValue || hr.HenBatch.Farm.TechnicianId.HasValue));

            IQueryable<HenReport> lastPeriodHenReports = lastTwoDaysHenReports.Where(hr => hr.CreatedDate > deadline);

            // build data for mortality
            IQueryable<Guid> geneticsFilter = lastTwoDaysHenReports.Select(hr => hr.HenBatch.GeneticId).Distinct();
            List<GeneticsParametersReference> geneticParameters = await geneticsParameterService.GetAll(asNoTracking: true)
                .Where(gpr => geneticsFilter.Any(g => g == gpr.GeneticsId)).ToListAsync();

            IEnumerable<ExceedsMoratalityThresholdParametersDTO> dataForMortality = lastPeriodHenReports.ToList()
                .GroupBy(hr => hr.HenBatch.Line.WarehouseId)
                .Select(hrw => new ExceedsMoratalityThresholdParametersDTO(hrw, geneticParameters))
                .Where(dm => dm.HenBatchData != null && dm.HenBatchData.RealMortality - dm.HenBatchData.ExpectedMortality >= 20);

            // build data for laying
            IEnumerable<ExceedsLayingThresholdParametersDTO> dataForLaying = lastTwoDaysHenReports.ToList()
                .GroupBy(hr => hr.HenBatch.Line.WarehouseId)
                .Select(whr => new ExceedsLayingThresholdParametersDTO(whr, now, deadline));

            // only stay with those who exceed the 4%
            dataForLaying = dataForLaying.Where(hr => hr.PreviousTotalEggs > 0 && ThresholdExceeded(hr.PreviousTotalEggs, hr.LastTotalEggs, 4));

            // filter to bring only users who should receive mail
            HashSet<Guid> receiversId = new HashSet<Guid>();
            foreach (ExceedsLayingThresholdParametersDTO data in dataForLaying) data.LookUpAndSetReceiversTo(receiversId);
            foreach (ExceedsMoratalityThresholdParametersDTO data in dataForMortality) data.LookUpAndSetReceiversTo(receiversId);

            List<ApplicationUser> users = await userService.GetAll(asNoTracking: true).Where(u => receiversId.Any(ri => ri == u.Id)).ToListAsync();
            return new ParametersThatExceedThresholdDTO(dataForLaying, dataForMortality, users);
        }

        private bool ThresholdExceeded(long previous, long last, decimal threshold)
        {
            long difference = previous - last;
            decimal differencePercentage = difference * 100 / previous;

            return differencePercentage > threshold;
        }

        public void ValidateDate(DateTime reportDate, Guid henBatchId)
        {
            DateTime yesterday = reportDate.Date.AddDays(-1);

            DateTime? henBatchStart = henBatchService.GetBirdsFirstLoadingDate(henBatchId);

            if (!henBatchStart.HasValue)
                throw new ValidationException(localizer[Lang.DoesntHaveHenAmount]);

            // Report date should be greater than hen batch start
            else if (henBatchStart > reportDate)
                throw new ValidationException(localizer.GetString(Lang.WrongReportDate, henBatchStart.Value.ToShortDateString()));

            else if (henBatchStart.Value.Date != yesterday.Date && henBatchStart.Value.Date != reportDate.Date && !operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeDailyReportWithoutDateValidation))
            {
                IQueryable<HenReport> reports = henReportService.GetAll()
                    .Where(hr => hr.HenBatchId == henBatchId && hr.ReportEnum == ReportEnum.New);

                DateTime lastReportDate = henReportService.GetAll()
                       .Where(hr => hr.HenBatchId == henBatchId && hr.ReportEnum == ReportEnum.New)
                       .OrderByDescending(hr => hr.Date)
                       .Select(hr => hr.Date)
                       .FirstOrDefault();

                int reportCount = reports.Count();

                // Check AllowBeginReportsOnAnyDate property
                if (reportCount == 0)
                {
                    var henBatch = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == henBatchId);
                    if (henBatch?.AllowBeginReportsOnAnyDate == true)
                    {
                        // Skip validation for fresh batches with AllowBeginReportsOnAnyDate enabled
                        return;
                    }
                    throw new ValidationException(localizer.GetString(Lang.NoReports, henBatchStart.Value.ToShortDateString()));
                }

                else if (lastReportDate.AddDays(1).Date < reportDate.Date)
                    throw new ValidationException(localizer.GetString(Lang.MissingReports, lastReportDate.Date.AddDays(1).ToShortDateString()));

                else if (lastReportDate != default && lastReportDate.Date > reportDate.Date)
                    throw new ValidationException(localizer.GetString(Lang.LastReportDateError, lastReportDate.ToShortDateString()));
            }
        }

        public void ValidateFirstProductionDate(DateTime reportDate, Guid? warehouseId = null, List<Guid> henbatches = null)
        {
            DateTime? firstProductionDate = henbatches != null && henbatches.Any()
                ? henBatchService.GetFirstProductionDate(henbatches)
                : henBatchService.GetFirstProductionDate(warehouseId.Value);

            if (reportDate >= firstProductionDate)
                throw new ValidationException(localizer[Lang.FirstProductionDateValidation]);
        }

        public Guid[] ValidateFormulasConsumed(Guid henBatchId)
        {
            Guid[] formulasConsumed = henBatchService.GetAll().Where(hb => hb.Id == henBatchId)
                .SelectMany(hb => hb.FormulasConsumed)
                .Select(fc => fc.FormulaId)
                .ToArray();

            if (!formulasConsumed.Any())
            {
                string code = henBatchService.GetAll().Where(hb => hb.Id == henBatchId).Select(hb => hb.Code).First();
                throw new ValidationException(localizer.GetString(Lang.FormulasConsumedValidation, code));
            }

            return formulasConsumed;
        }

        private void ValidateSameHenBatches(CreateFromWarehouseAPI dto)
        {
            if (!dto.FeedIntakeDTO.Select(fi => fi.HenBatchId).SequenceEqual(dto.BirdsDTO.Select(b => b.HenBatchId)))
                throw new ValidationException(localizer[Lang.SameHenBatchesValidation]);
        }

        /// <summary>
        /// Verified that the henBatchParent has a date of first production and a date of capitalization
        /// </summary>
        public bool[] AlertPruductionDate(Guid henBatchId)
        {
            IQueryable<HenBatch> allHenBatches = henBatchService.GetAll(asNoTracking: true);

            HenBatch defaultHenBatch = allHenBatches.Where(hb => hb.Id == henBatchId).FirstOrDefault();

            if (defaultHenBatch == null)
                return new bool[] { true, true };

            if (!defaultHenBatch.ParentId.HasValue)
                return new bool[] { !defaultHenBatch.FirstProductionDate.HasValue, !defaultHenBatch.CapitalizationDate.HasValue };
            else
            {
                HenBatch parentHenBatch = allHenBatches.Where(hb => hb.Id == defaultHenBatch.ParentId).FirstOrDefault();

                if (parentHenBatch == null)
                    return new bool[] { true, true };

                return new bool[] { !parentHenBatch.FirstProductionDate.HasValue, !parentHenBatch.CapitalizationDate.HasValue };
            }
        }

        public async Task<HenReportMortalityByHenbatchPage> GetPageByHenbatch(string farmCode, DateTime from, DateTime? to, int page = 0, int pageSize = 20)
        {
            Farm farm = farmService.GetAll(true).Where(f => f.Code == farmCode).FirstOrDefault();
            HenReportMortalityByHenbatchPage response = new HenReportMortalityByHenbatchPage();

            if (farm is null)
                return response;

            IQueryable<HenBatch> henBatches = henBatchService.GetAll(true, false).Where(hb => hb.Active && hb.FarmId == farm.Id);

            to = to.HasValue ? to : DateTime.Now;

            // prioritize children lotes
            if (henBatches.Any())
                henBatches = henBatches.Where(hb => hb.ParentId.HasValue);

            // if not any data by children then prioritize parent lotes
            if (!henBatches.Any())
                henBatches = henBatchService.GetAll(true, false).Where(hb => !hb.ParentId.HasValue);

            if (!henBatches.Any())
                return response;

            IQueryable<Guid> henBatchIds = henBatches.Select(hb => hb.Id);

            IQueryable<HenReport> henReports = henReportService.GetAll(true)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Farm)
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(hb => hb.Warehouse)
                .Include(hr => hr.Casualties).ThenInclude(c => c.CasualtyReason)
                .Where(a => a.FarmId == farm.Id && henBatchIds.Contains(a.HenBatchId) && a.Date.Date >= from.Date && a.Date.Date <= to.Value.Date && a.ReportEnum == ReportEnum.New);

            List<HenReportMortalityByHenbatchItem> items = new List<HenReportMortalityByHenbatchItem>();

            foreach (var hr in henReports)
            {
                if (hr.Dead > 0)
                {
                    var casualtiesGroupedByReason = hr.Casualties
                        .Where(s => s.DeadCount > 0)
                        .GroupBy(c => c.CasualtyReason.Id)
                        .Select(g => new
                        {
                            CasualtyReasonId = g.Key,
                            QuantityFemale = g.Sum(x => x.IsFemale ? x.DeadCount : 0),
                            QuantityMale = g.Sum(x => !x.IsFemale ? x.DeadCount : 0),
                            Reason = g.First().CasualtyReason.Description,
                            Quantity = g.Sum(x => x.DeadCount)
                        });

                    foreach (var group in casualtiesGroupedByReason)
                    {
                        items.Add(new HenReportMortalityByHenbatchItem
                        {
                            HenBatch = hr.HenBatch.Farm.Code + "-" + hr.HenBatch.Code + "-" +
                                       hr.HenBatch.Line.Warehouse.Code + "-" + hr.HenBatch.Line.Code,
                            ProductionDate = hr.Date,
                            Reason = group.Reason,
                            QuantityFemale = group.QuantityFemale,
                            QuantityMale = group.QuantityMale,
                        });
                    }
                }
            }

            int total = items.Count();
            var hateoasPage = new HenReportMortalityByHenbatchPage();
            hateoasPage.QuantityFemaleFarm = items.Sum(x => x.QuantityFemale);
            hateoasPage.QuantityMaleFarm = items.Sum(x => x.QuantityMale);
            hateoasPage.QuantityTotalFarm = hateoasPage.QuantityFemaleFarm + hateoasPage.QuantityMaleFarm;
            items = items.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            hateoasPage.PageSize = pageSize;
            hateoasPage.Total = total;
            hateoasPage.Items = items.OrderBy(i => i.ProductionDate).ToList();
            return hateoasPage;
        }

        /// <summary>
        /// Gets GAD by farm code
        /// </summary>
        public async Task<HenbatchReportGADPage> GetGAD(HenReportGADDTO henReportGADDTO)
        {
            Farm farm = await farmService.GetAll(true)
                .Where(f => f.Code == henReportGADDTO.FarmCode)
                .FirstOrDefaultAsync();

            HenbatchReportGADPage response = new HenbatchReportGADPage();

            if (farm is null)
                throw new Exception(localizer[HenBatchLang.FarmNotFoundEx]);

            var henBatchesToCalculate = henBatchService
                .GetAll(true, false)
                .Where(hb => hb.FarmId == farm.Id && (hb.ParentId.HasValue || (!hb.ParentId.HasValue && hb.LineId.HasValue)))
                .Select(hb => new
                {
                    hb.Id,
                    FarmCode = hb.Farm.Code,
                    LineCode = hb.Line.Code,
                    WarehouseCode = hb.Line.Warehouse.Code,
                    hb.Code,
                });

            henReportGADDTO.To ??= DateTime.Now;

            if (!henBatchesToCalculate.Any())
                return response;

            IQueryable<Guid> henBatchIds = henBatchesToCalculate.Select(hb => hb.Id);

            Dictionary<Guid, string> henBatchLabels = await henBatchesToCalculate
                .ToDictionaryAsync(hb => hb.Id, hb => $"{hb.FarmCode} - {hb.Code} - {hb.LineCode} - {hb.WarehouseCode}");

            var henBatchGroups = henBatchPerformanceService.GetAll()
                .Where(hbp => henBatchIds.Any(hb => hb == hbp.HenBatchId) && henReportGADDTO.From <= hbp.Date && henReportGADDTO.To >= hbp.Date)
                .Select(hbp => new
                {
                    hbp.HenBatchId,
                    hbp.Date,
                    GADFemale = hbp.HenAmountFemale > 0 ? hbp.FeedIntakeFemale * 1000 / hbp.HenAmountFemale / 7 : 0,
                    GADMale = hbp.HenAmountMale > 0 ? hbp.FeedIntakeMale * 1000 / hbp.HenAmountMale / 7 : 0,
                })
                .ToList()
                .GroupBy(hbp => hbp.HenBatchId);

            foreach (var aGroup in henBatchGroups)
                foreach (var aData in aGroup)
                {
                    var item = new HenbatchReportGADResDTO()
                    {
                        Date = aData.Date,
                        HenBatchName = henBatchLabels[aData.HenBatchId],
                    };
                    item.GAD.Add(new GADDTO(aData.GADFemale, aData.GADMale));

                    response.Items.Add(item);
                }

            response.Items = response.Items.Skip((henReportGADDTO.Page - 1) * henReportGADDTO.PageSize).Take(henReportGADDTO.PageSize).ToList();
            response.PageSize = response.Items.Count();
            response.Page = henReportGADDTO.Page;

            return response;
        }
    }
}