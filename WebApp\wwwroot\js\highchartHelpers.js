/*
 * functions to draw different types of charts with HighCharts.
 */

/* ----------------------------------------
---- Generic line/bar/column chart DTO ----
Use it like this:
    $('#DivId').DrawGenericHighChart(chartDTO)
Where DivId is the id of the html div where the
chart is rendered, and chartDTO is an object
containing basic chart configurations.
---------------------------------------- */
$.fn.DrawGenericHighChart = function (userOptions, tooltipFormatter, plotOptions) {
    var chartId = $(this).attr('id');

    // Check if colors where set for every category for every series globally
    if (userOptions.categoryColors && userOptions.categoryColors.length > 0) {
        for (let i = 0; i < userOptions.series.length; i++) {
            for (let j = 0; j < userOptions.categoryColors.length; j++) {
                userOptions.series[i].data[j] = { y: userOptions.series[i].data[j], color: userOptions.categoryColors[j] }
            }
        }
    }

    // Check if objects where passed as data
    for (let i = 0; i < userOptions.series.length; i++) {
        if (userOptions.series[i].dataObjects && userOptions.series[i].dataObjects.length > 0) {
            userOptions.series[i].data = userOptions.series[i].dataObjects    
        }
    }

    // Fill template object with userOptions
    var chartConfiguration = {
        title: { text: userOptions.title },
        credits: { enabled: false },
        lang: { noData: userOptions.noDataAvilableMessage },
        tooltip: {
            shared: userOptions.toolTipShared,
            headerFormat: '<span style="font-size: 10px">' + userOptions.xAxisLabel + ': {point.key}</span><br/>',
        },
        xAxis: [{ categories: userOptions.categories, crosshair: true, title: { text: userOptions.xAxisLabel }, labels: { style: { textOverflow: 'none' } }, gridLineWidth: userOptions.xAxisGridLineWidth }],
        yAxis: userOptions.yAxis,
        series: userOptions.series,
        exporting: {
            menuItemDefinitions: {
                XLSX: {
                    onclick: function () {
                        exportExcel(this);
                    },
                    text: 'XLSX'
                },
                PNG: {
                    onclick: function () {
                        exportPNG(this);
                    },
                    text: 'PNG'
                }
            },
            fallbackToExportServer: true,
            buttons: {
                contextButton: {
                    menuItems: ["PNG", "XLSX" ]
                }
            }
        }
    };

    // check if height was given
    chartConfiguration.chart = { height: userOptions.chart?.height ?? 500 }

    // Check custom formatters
    // Tooltips
    if (tooltipFormatter) {
        chartConfiguration.tooltip.formatter = tooltipFormatter;
    }
    else {
        chartConfiguration.tooltip.formatter = defaultToolTipFormatter;
    }
    // Apply Y axis unit formatter when units are provided
    for (var i = 0; i < chartConfiguration.yAxis.length; i++) {
        if (chartConfiguration.yAxis[i].unitSymbol !== "") {
            chartConfiguration.yAxis[i].labels = { formatter: defaultYAxisFormatter };
        }
    }

    // Check if plot options where provided
    if (plotOptions) {
        chartConfiguration.plotOptions = plotOptions;
    }

    // Draw chart and return object if needed.
    // All charts are available at Highcharts.charts list
    // To look for a specific one, filter it like this:
    // Highcharts.charts.find(obj => obj.renderTo.id == "DivId")
    // Where DivId is the id of the html div where the chart
    // is rendered
    return Highcharts.chart(chartId, chartConfiguration);
}

/* --------------------------------------------
------------------- Helpers -------------------
-------------------------------------------- */
// Default Y Axis formatter for adding units to labels when provided
var defaultYAxisFormatter = function () {
    // Get Highcharts default formatter output which formats
    // numbers given the decimal and thousand separators
    // preconfigured and handles big numbers if needed with
    // suffixes such as "K" for thousands
    var result = this.axis.defaultLabelFormatter.call(this);

    // If the user provided a unit symbol asociated to the series,
    // then, append it to the default highcharts formatter output
    if (this.axis.userOptions.unitSymbol !== "") {
        result += " " + this.axis.userOptions.unitSymbol;
    }

    return result;
}

// Default tooltip formatter to show units next to values
var defaultToolTipFormatter = function () {
    // Tooltip header in bold text showing the XAxis label
    // followed by the exact point(e.g.: "Week of year 19:")
    var header = "<b>" + this.points[0].series.xAxis.axisTitle.textStr + " " + this.x + ": </b><br/>";

    // Build body of tooltip. Each line displays the series name followed
    // by the value and units if given in bold text
    var body = "";
    // loop through each series
    for (var i = 0; i < this.points.length; i++) {
        // show points that are not null or undefined
        if (this.points[i].y !== null && this.points[i].y !== undefined) {
            // values are rounded to no decimals
            body += '<span style="color:' + this.points[i].color + '">\u25CF</span> ' + this.points[i].series.name + ": <b>" + this.points[i].series.chart.numberFormatter(this.points[i].y, 2) + " " + this.points[i].point.series.userOptions.toolTip.valueSuffix + "</b><br/>";
        }
    }

    // Append body to header and return
    return header + body;
}

const exportExcel = function (chart) {
    let name,
        xlsxRows = [],
        rows;

    // Get data as table rows with highcharts data-export module
    rows = chart.getDataRows(true);

    // Parse values to cell objects with data type and value
    xlsxRows = Highcharts.map(rows.slice(1), function (row) {
        return Highcharts.map(row, function (column) {
            return column;
        });
    });

    // Add units to headers if provided in series.
    // (skip first item in row because it is the x axis label)
    for (var i = 0; i < xlsxRows[0].length - 1; i++) {
        if (chart.userOptions.series[i].toolTip && chart.userOptions.series[i].toolTip.valueSuffix) {
            xlsxRows[0][i + 1].value += (" [" + chart.userOptions.series[i].toolTip.valueSuffix) + "]";
        }
    }

    // Get the filename, copied from Highchart.Chart.fileDownload function
    if (chart.options.exporting.filename) {
        name = chart.options.exporting.filename;
    } else if (chart.title && chart.title.textStr) {
        name = chart.title.textStr.replace(/ /g, '-').toLowerCase();
    } else {
        name = 'chart';
    }

    const form = document.createElement("form");
    document.body.appendChild(form);
    form.action = "/Dashboard/ExportXLSX";
    form.method = "POST";

    const chartName = document.createElement("input");
    chartName.setAttribute("type", "text");
    chartName.name = 'name';
    chartName.value = chart.userOptions.title.text;
    form.appendChild(chartName);

    const rowsInput = document.createElement("input");
    rowsInput.setAttribute("type", "text");
    rowsInput.name = 'rows';
    rowsInput.value = JSON.stringify(xlsxRows);
    form.appendChild(rowsInput);

    form.submit();
};

const exportPNG = function (chart) {
    const form = document.createElement("form");
    document.body.appendChild(form);
    form.action = "/Dashboard/ExportPNG";
    form.method = "POST";

    const chartName = document.createElement("input");
    chartName.setAttribute("type", "text");
    chartName.name = 'name';
    chartName.value = chart.userOptions.title.text;
    form.appendChild(chartName);

    const svgInput = document.createElement("input");
    svgInput.setAttribute("type", "text");
    svgInput.name = 'svg';
    svgInput.value = chart.getSVG();
    form.appendChild(svgInput);

    form.submit();
};