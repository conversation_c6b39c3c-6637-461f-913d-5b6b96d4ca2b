using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApp.Attributes;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.BreedingController;

namespace WebApp.Controllers
{
    public class BreedingController : DashboardController
    {
        private readonly ICompanyService companyService;
        private readonly IFarmService farmService;
        private readonly IHenReportBusinessLogic henReportBusinessLogic;
        private readonly ILineService lineService;
        private readonly ISearaChartsBusinessLogic searaChartsBusinessLogic;
        private readonly IOperationContext operationContext;
        private readonly IMaterialTypeService materialTypeService;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly IService<GeneticsParametersReference> geneticsParametersReferenceService;
        private readonly RazorViewRender razorViewRender;

        public BreedingController(
            IExceptionManager exceptionManager,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IHenBatchService henBatchService,
            IStringLocalizer<SharedResources> localizer,
            IMessageBusinessLogic messageBusinessLogic,
            IStatisticsBusinessLogic statisticsBusinessLogic,
            IHolidayService holidayService,
            IContainerService<Container> containerService,
            ITaskEntityService taskEntityService,
            IUserService<ApplicationUser> userService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IHappeningBusinessLogic happeningBusinessLogic,
            IMaterialTypeService materialTypeService,
            IOperationContext operationContext,
            ILogger logger,
            ICompanyService companyService,
            IFarmService farmService,
            IHenReportBusinessLogic henReportBusinessLogic,
            ILineService lineService,
            IHenBatchPerformanceService henBatchPerformanceService,
            IService<GeneticsParametersReference> geneticsParametersReferenceService,
            RazorViewRender razorViewRender,
            ISearaChartsBusinessLogic searaChartsBusinessLogic)
            : base(
                  exceptionManager,
                  henBatchBusinessLogic,
                  henBatchService,
                  farmService,
                  localizer,
                  messageBusinessLogic,
                  statisticsBusinessLogic,
                  holidayService,
                  containerService,
                  taskEntityService,
                  userService,
                  fileService,
                  happeningBusinessLogic,
                  materialTypeService,
                  operationContext,
                  logger)
        {
            this.companyService = companyService;
            this.farmService = farmService;
            this.operationContext = operationContext;
            this.henReportBusinessLogic = henReportBusinessLogic;
            this.lineService = lineService;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.geneticsParametersReferenceService = geneticsParametersReferenceService;
            this.razorViewRender = razorViewRender;
            this.searaChartsBusinessLogic = searaChartsBusinessLogic;
        }

        public IActionResult Index()
        {
            ViewData["Title"] = localizer[Lang.IndexTitle];
            return View();
        }

        [AuthorizeAnyRoles(Roles.BackofficeSuperAdministrator, Roles.BackofficeBreedingAdministrator, Roles.BackofficeBreedingDashboard, Roles.SearaDashboardChartsBreeding)]
        public IActionResult Dashboard()
        {
            AreaEnum area = AreaEnum.Breeding;
            List<AreaEnum> areas = new List<AreaEnum>
            {
                area
            };
            ViewData["Area"] = area;
            ViewData["UserIsAdmin"] = this.operationContext.UserIsInAnyRole(Roles.BackofficeTaskAdministrator,
                                                                            Roles.BackofficeTaskUser,
                                                                            Roles.BackofficeSuperAdministrator,
                                                                            Roles.BackofficeBreedingAdministrator).ToString();
            ViewData["DashboardsResources"] = JsLocalizer.GetLocalizedResources(JsLang.BreedingDashboard, this.localizer);
            ViewData["Title"] = localizer[Lang.DashboardTitle];
            //Calendar
            InitCalendar(areas);

            // Performance chart filter initialization
            InitLists();
            DateTime today = DateTime.Today;
            ViewData["MinDayDefault"] = today.AddMonths(-1).Date.ToShortDateString();
            ViewData["Today"] = today.Date.ToShortDateString();

            // Histogram filter initialization
            ViewData["DefaultFilterParameters"] = new FilterDataDTO
            {
                HenBatchHistogramWeekNumberBinLimit1 = HistogramDefaultLimits.BreedingLimit1,
                HenBatchHistogramWeekNumberBinLimit2 = HistogramDefaultLimits.BreedingLimit2,
                HenBatchHistogramWeekNumberBinLimit3 = HistogramDefaultLimits.BreedingLimit3,
                HenBatchHistogramWeekNumberBinLimit4 = HistogramDefaultLimits.BreedingLimit4
            };

            Guid userId = this.operationContext.GetUserId();
            var user = userService.GetAll(filterByTenant: true).Where(u => u.Id == userId).FirstOrDefault();
            ViewData["UserLanguage"] = user.Language;

            return View();
        }

        [AuthorizeAnyRoles(Roles.BackofficeSuperAdministrator, Roles.SearaDashboardCardsBreeding, Roles.SearaDashboardChartsBreeding)]
        public async Task<IActionResult> GetSearaCards(string from, string to, Guid? henBatchId, Guid? companyId, Guid? userFilterId)
        {
            List<(string, int)> viewList = new List<(string, int)>();

            if (!henBatchId.HasValue && !companyId.HasValue && !userFilterId.HasValue)
            {
                return Ok(new
                {
                    viewList
                });
            }

            List<SimpleDashboardInformativeCardDTO> simpleCardDTO = new List<SimpleDashboardInformativeCardDTO>();
            List<InformativeCardDTO> listCardDTO = new List<InformativeCardDTO>();
            List<WidthCardDTO> widthCardDTO = new List<WidthCardDTO>();
            DateTime dateFrom = DateTime.Parse(from);
            DateTime dateTo = DateTime.Parse(to);

            if (henBatchId.HasValue)
            {
                IQueryable<HenBatchPerformance> henBatchPerformaces = GetHenBatchPerformace(dateFrom, dateTo, henBatchId.Value);
                IQueryable<HenBatch> henBatch = GetHenBatch(henBatchId.Value);
                IQueryable<GeneticsParametersReference> genetics = GetGenetics(henBatchId.Value);

                HenStage henStage = henBatch.Select(hb => hb.HenStage).FirstOrDefault();
                DashboardInformativeCardsDTO informativeDTO = new DashboardInformativeCardsDTO();
                informativeDTO.HenBatchIds.Add(henBatchId.Value);
                informativeDTO = this.statisticsBusinessLogic.GetCardsHenBatchData(informativeDTO, henBatchPerformaces, genetics, henStage);
                simpleCardDTO = informativeDTO.SimpleCardDTOs;
                listCardDTO = informativeDTO.ListCardDTOs;
                widthCardDTO = informativeDTO.WidthCardDTOs;
            }

            if (companyId.HasValue)
            {
                List<Guid> henBatchIds = GetHenBatchesByCompany(companyId.Value);

                List<HenBatchPerformance> allHenBatchPerformances = new List<HenBatchPerformance>();
                List<GeneticsParametersReference> allGenetics = new List<GeneticsParametersReference>();

                foreach (var id in henBatchIds)
                {
                    allHenBatchPerformances.AddRange(GetHenBatchPerformace(dateFrom, dateTo, id).ToList()); // Convertir IQueryable a List
                    allGenetics.AddRange(GetGenetics(id).ToList()
                        .Where(g => !allGenetics.Any(existing => existing.Id == g.Id))); // Evita duplicados por ID
                }

                DashboardInformativeCardsDTO informativeDTO = new DashboardInformativeCardsDTO();
                informativeDTO.HenBatchIds.AddRange(henBatchIds); // Agregar todos los IDs

                // Hacer una única llamada con todos los datos acumulados
                informativeDTO = this.statisticsBusinessLogic.GetCardsHenBatchData(informativeDTO,
                    allHenBatchPerformances.AsQueryable(),
                    allGenetics.AsQueryable(),
                    HenStage.Breeding);

                simpleCardDTO.AddRange(informativeDTO.SimpleCardDTOs);
                listCardDTO.AddRange(informativeDTO.ListCardDTOs);
                widthCardDTO.AddRange(informativeDTO.WidthCardDTOs);
            }


            if (userFilterId.HasValue)
            {
                List<Guid> henBatchIds = GetHenBatchesByUser(userFilterId.Value);

                List<HenBatchPerformance> allHenBatchPerformances = new List<HenBatchPerformance>();
                List<GeneticsParametersReference> allGenetics = new List<GeneticsParametersReference>();

                foreach (var id in henBatchIds)
                {
                    allHenBatchPerformances.AddRange(GetHenBatchPerformace(dateFrom, dateTo, id).ToList());
                    allGenetics.AddRange(GetGenetics(id).ToList()
                        .Where(g => !allGenetics.Any(existing => existing.Id == g.Id))); // Evita duplicados por ID
                }

                DashboardInformativeCardsDTO informativeDTO = new DashboardInformativeCardsDTO();
                informativeDTO.HenBatchIds.AddRange(henBatchIds); // Agregar todos los IDs

                // Hacer una única llamada con todos los datos acumulados
                informativeDTO = this.statisticsBusinessLogic.GetCardsHenBatchData(informativeDTO,
                    allHenBatchPerformances.AsQueryable(),
                    allGenetics.AsQueryable(),
                    HenStage.Breeding);

                simpleCardDTO.AddRange(informativeDTO.SimpleCardDTOs);
                listCardDTO.AddRange(informativeDTO.ListCardDTOs);
                widthCardDTO.AddRange(informativeDTO.WidthCardDTOs);
            }


            if (userFilterId.HasValue || companyId.HasValue)
            {
                simpleCardDTO = simpleCardDTO
                    .GroupBy(s => s.Title)
                    .Select(sg => new SimpleDashboardInformativeCardDTO
                    {
                        Title = sg.Key,
                        IsSubtitle = sg.First().IsSubtitle,
                        Text = sg.First().Text,
                        SubtitleBold = sg.First().SubtitleBold,
                        SubtitleSimple = sg.First().SubtitleSimple,
                        Value = sg.Sum(s => s.NumberValue) != 0 ? sg.Average(s => s.NumberValue).ToString() : "", //calculo de valor
                        ValueColor = sg.First().ValueColor,
                        Icon = sg.First().Icon,
                        IconColor = sg.First().IconColor,
                        SubtitleBoldColor = sg.First().SubtitleBoldColor,
                        SubtitleSimpleColor = sg.First().SubtitleSimpleColor,
                        IsSimple = sg.First().IsSimple
                    })
                    .ToList();

                listCardDTO = listCardDTO
                    .GroupBy(l => l.Title)
                    .Select(lg => new InformativeCardDTO
                    {
                        Title = lg.Key,
                        InformativeCards = lg.SelectMany(l => l.InformativeCards)
                            .GroupBy(item => new { item.Text })
                            .Select(itemGroup => new InformativeCardListItemDTO
                            {
                                IsSubtitle = itemGroup.First().IsSubtitle,
                                Text = itemGroup.Key.Text,
                                TextColor = itemGroup.First().TextColor,
                                Value = itemGroup.Sum(i => i.NumberValue) > 0 ? (itemGroup.Average(i => i.NumberValue)).ToString("F2") : "", //calculo de valor
                                ValueColor = itemGroup.First().ValueColor,
                                ValueTooltipText = itemGroup.First().ValueTooltipText
                            })
                            .ToList()
                    })
                    .ToList();

                widthCardDTO = widthCardDTO
                    .GroupBy(w => w.Title)
                    .Select(wg => new WidthCardDTO
                    {
                        Title = wg.Key,
                        WidthCards = wg.SelectMany(w => w.WidthCards)
                            .GroupBy(item => item.Text)
                            .Select(itemGroup => new WidthCardListItemDTO
                            {
                                Text = itemGroup.Key,
                                Value = itemGroup.Sum(i => i.NumberValue) > 0 ? (itemGroup.Average(i => i.NumberValue)).ToString("F2") : "", //calculo
                                ValueColor = itemGroup.First().ValueColor,
                                ValueTooltipText = itemGroup.First().ValueTooltipText
                            })
                            .ToList()
                    })
                    .ToList();
            }

            foreach (var dtos in simpleCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/SimpleDashboardInformativeCard/Default", dtos), 0));

            foreach (var dtos in listCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/ListDashboardInformativeCard/Default", dtos), 1));

            foreach (var dtos in widthCardDTO)
                viewList.Add((await razorViewRender.RenderToStringAsync("Shared/Components/WidthDashboardInformativeCard/Default", dtos), 2));

            return Ok(new
            {
                viewList
            });
        }


        #region Charts
        [HttpPost]
        public IActionResult GetDataForBreedingMaleFemaleDistributionChart(FilterDataDTO filters)
        {

            try
            {
                base.ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                return new JsonResult(this.statisticsBusinessLogic.GetDataForMaleFemaleDistributionChart(filters, HenStage.Breeding));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForBreedingDashboardMortalityChart(FilterDataDTO filters)
        {
            try
            {
                base.ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                return new JsonResult(this.statisticsBusinessLogic.GetDataForMortalityChart(filters, HenStage.Breeding));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpGet]
        public IActionResult GetDataForBreedingDashboardCapacityChart()
        {
            return new JsonResult(this.statisticsBusinessLogic.GetDataForCapacityChart(HenStage.Breeding));
        }

        [HttpGet]
        public IActionResult GetDataForBreedingDashboardGeneticsChart()
        {
            return new JsonResult(this.statisticsBusinessLogic.GetDataForGeneticsChart(HenStage.Breeding));
        }

        [HttpPost]
        public IActionResult GetDataForBreedingDashboardHenBatchAgeChart(FilterDataDTO data)
        {
            try
            {
                data.HenStage = HenStage.Breeding;
                return new JsonResult(this.statisticsBusinessLogic.GetDataForHenBatchAgeChart(data));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpGet]
        public IActionResult GetDataForBreedingDashboardHenAmountByGroupChart()
        {
            return new JsonResult(this.statisticsBusinessLogic.GetDataForColorAndGeneticHenBatchChart(HenStage.Breeding));
        }

        [HttpPost]
        public IActionResult GetDataForBreedingDashboardPerformanceChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Breeding;

                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, false);

                List<List<HenBatchStatusDTO>> data = new List<List<HenBatchStatusDTO>>();
                DashboardLineOrBarChartDTO chartDTO = new DashboardLineOrBarChartDTO();

                (data, chartDTO) = this.statisticsBusinessLogic.GetDataForPerformanceChart(henBatches, filters.MinDate.Value, filters.MaxDate.Value, filters.HenStage);

                return new JsonResult(new { data, chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForBreedingDashboardHenBatchPerformanceChart(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Breeding;

                ParseDatesFromFilters(filters, ChartTimeSpan.WeekSpan, weeks: true);
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, true);

                return new JsonResult(this.statisticsBusinessLogic.GetDataForBatchPerformanceChart(HenStage.Breeding, filters.MinDate.Value, filters.MaxDate.Value, henBatches));
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForBreedingDashboardSampleCageCharts(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Breeding;
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);

                SampleCageChartsDTO chartDTO = this.statisticsBusinessLogic.GetDataForSampleCageCharts(filters);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }

        [HttpPost]
        public IActionResult GetDataForBreedingDashboardSearaPerformanceCharts(FilterDataDTO filters)
        {
            try
            {
                filters.HenStage = HenStage.Breeding;
                ParseDatesFromFilters(filters, ChartTimeSpan.DaySpan);
                bool parentHenBatches = filters.HenBatchId.HasValue && !filters.WarehouseId.HasValue && !filters.LineId.HasValue;
                List<Guid> henBatches = GetHenBatchIdsFromFilters(filters, parents: parentHenBatches);

                PerformanceSearaChartsDTO chartDTO = this.searaChartsBusinessLogic.GetDataForSearaPerformanceChart(HenStage.Breeding, henBatches);

                return new JsonResult(new { chartDTO });
            }
            catch (ValidationException ex)
            {
                return ChartFilterError(ex);
            }
            catch (Exception ex)
            {
                return ChartLoadingError(ex);
            }
        }
        #endregion

        #region Filter lists initialization
        private void InitLists()
        {
            HenStage henStage = HenStage.Breeding;

            ContainerFilterDTO items = henBatchBusinessLogic.GetAllContainerFilter(henStage, null, activeBatches: true, orderByNew: true);

            ViewData["Companies"] = GetCompanies();
            var companyIds = companyService.GetAll().Select(c => c.Id).ToList();
            var farmIds = farmService.GetAll().Select(c => c.Id).ToList();
            ViewData["Supervisors"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.SupervisorId, farmIds);
            ViewData["Extensionists"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.TechnicianId, farmIds);
            ViewData["Productors"] = GetUsersByCompanyWithProfile(companyIds, SecurityProfiles.ProductorBreedingId, farmIds);

            ViewData["Lines"] = items.Line;
            ViewData["HenBatchesOrderByNew"] = items.ParentHenBatch;
            ViewData["Warehouses"] = items.Warehouse;
            ViewData["Cages"] = items.Cages;
        }

        private List<SelectListItem> GetCompanies()
        {
            return this.companyService.GetAll().OrderBy(c => c.BusinessName)
                .Select(c => new SelectListItem(c.BusinessName, c.Id.ToString())).ToList();
        }

        public List<SelectListItem> GetUsersByCompanyWithProfile(List<Guid> companyIds, Guid? profile, List<Guid> farmIds)
        {
            IQueryable<ApplicationUser> companyUsers = userService.GetAllFull();

            // Filtrar usuarios por compañías
            companyUsers = companyUsers.Where(au => au.Companies == null || !au.Companies.Any() ||
                                                    au.Companies.Any(c => companyIds.Contains(c.CompanyId)));

            // Obtener farmsActiveIds solo si el perfil requiere este filtro
            List<Guid?> farmsActiveIds = new List<Guid?>();
            Guid productorProfileId = Guid.Parse("155F3BD0-E1A9-667B-BB06-3A045F9214FB");
            if (profile == productorProfileId)
            {
                farmsActiveIds = henBatchService.GetAll()
                    .Where(hb => hb.HenStage == HenStage.Breeding && hb.HenAmountFemale > 0 && hb.HenAmountMale > 0 && hb.Active)
                    .Where(hb => farmIds.Contains(hb.FarmId.Value))
                    .Select(hb => hb.FarmId).ToList();


                // Filtrar usuarios por farmsActiveIds
                companyUsers = companyUsers.Where(au => au.Sites.Any(c => farmsActiveIds.Contains(c.SiteId)));
            }

            // Filtrar usuarios por perfil de seguridad
            companyUsers = companyUsers.Where(au => au.SecurityProfiles.Any(sp => sp.SecurityProfileId == profile));

            // Excluir usuario de integración
            Guid tenantId = this.operationContext.GetUserTenantId().Value;
            Guid excludedUserId = Guid.Parse("daae7110-08a2-50d7-8149-39fb048e5702"); // Usuario de integración

            return companyUsers.Where(u => u.TenantId == tenantId && u.Id != excludedUserId)
                .Select(u => new SelectListItem(u.Name + " " + u.LastName, u.Id.ToString()))
                .ToList();
        }
        #endregion

        #region refresh inputs
        /// <summary>
        /// Gets farms from a henStage to use in henreport index farm filter.
        /// </summary>
        public List<SelectListItem> GetFarmsByHenStage()
        {
            return henBatchBusinessLogic.GetFarms(HenStage.Breeding);
        }

        /// <summary>
        /// Gets clusters from a farm to use in henreport index cluster filter.
        /// </summary>
        public List<SelectListItem> GetClustersByFarm(Guid? farmId)
        {
            return henBatchBusinessLogic.GetClusters(HenStage.Breeding, farmId);
        }

        /// <summary>
        /// Gets hen warehouses from a cluster to use in henreport index warehouses filter.
        /// </summary>
        public List<SelectListItem> GetWarehousesByCluster(Guid? clusterId)
        {
            return henBatchBusinessLogic.GetWarehouses(HenStage.Breeding, clusterId);
        }

        /// <summary>
        /// Gets lines from a hen warehouse to use in henreport index line filter.
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouse(Guid? warehouseId, Guid? henBatchId)
        {
            return henBatchBusinessLogic.GetLinesFiltered(HenStage.Breeding, warehouseId, henBatchId);
        }
        /// <summary>
        /// Gets cages from a line.
        /// </summary>
        public List<SelectListItem> GetCagesByLine(Guid? lineId)
        {
            return henBatchBusinessLogic.GetCages(HenStage.Breeding, lineId);
        }

        /// <summary>
        /// Gets all henbatches to use in hen report index henbatch filter
        /// </summary>
        public List<SelectListItem> GetHenBatchesByLine(Guid? lineId)
        {
            return henBatchBusinessLogic.GetHenBatches(HenStage.Breeding, lineId, true);
        }

        /// <summary>
        /// Gets all warehouses by selected hen batch
        /// </summary>
        public List<SelectListItem> GetWarehousesByHenBatch(Guid selectedHenBatch)
        {
            return henBatchBusinessLogic.GetWarehousesByHenBatch(selectedHenBatch, HenStage.Breeding);
        }

        /// <summary>
        /// Gets lines by selected hen batch and selected warehouse.
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouseAndHenBatch(Guid selectedWarehouse, Guid selectedHenBatch)
        {
            return henBatchBusinessLogic.GetLinesByWarehouseAndHenBatch(selectedWarehouse, selectedHenBatch, HenStage.Breeding);
        }
        #endregion refresh inputs

        #region Create Lists
        /// <summary>
        /// Gets the henbatch and its genetic from a line.
        /// </summary>
        public async Task<IActionResult> GetHenbatch(Guid lineId)
        {
            Line line = await this.lineService.GetFullAsync(lineId);
            HenBatch henBatch = line.HenBatches.FirstOrDefault(hb => hb.DateEnd == null);
            return Json(new
            {
                henbatchId = henBatch.Id,
                geneticName = henBatch.Genetic.Name,
                henAmount = henBatch.HenAmountFemale + henBatch.HenAmountMale,
                henbatchName = henBatch.DetailedName
            });
        }

        public IQueryable<HenBatchPerformance> GetHenBatchPerformace(DateTime from, DateTime to, Guid henBatchId)
        {
            IQueryable<HenBatchPerformance> henBatchPerformances = henBatchPerformanceService.GetAll()
                .Where(hbp => hbp.Date >= from && hbp.Date <= to)
                .Where(hbp => hbp.HenBatchId == henBatchId || hbp.HenBatch.ParentId == henBatchId);
            return henBatchPerformances;
        }

        public List<Guid> GetHenBatchesByCompany(Guid companyId)
        {
            List<Guid> henBatches = henBatchService.GetAll()
                .Where(hb => hb.HenStage == HenStage.Breeding && hb.CompanyId == companyId && hb.HenAmountFemale > 0 && hb.Active && hb.ParentId == null)
                .Select(hb => hb.Id)
                .ToList();

            return henBatches;
        }

        public List<Guid> GetHenBatchesByUser(Guid userFilterId)
        {
            ApplicationUser user = userService.GetAllFull().FirstOrDefault(u => u.Id == userFilterId);
            List<Guid> farms = user.Sites.Select(s => s.SiteId).ToList();

            List<Guid> henBatches = henBatchService.GetAll()
               .Where(hb => hb.HenStage == HenStage.Breeding && hb.HenAmountFemale > 0 && hb.Active && hb.ParentId == null)
               .Where(hb => farms.Contains(hb.FarmId.Value))
               .Select(hb => hb.Id).ToList();

            return henBatches;
        }

        public IQueryable<HenBatch> GetHenBatch(Guid henBatchId)
        {
            IQueryable<HenBatch> henBatch = henBatchService.GetAll().Where(hb => hb.Id == henBatchId);
            return henBatch;
        }

        public IQueryable<GeneticsParametersReference> GetGenetics(Guid henBatchId)
        {
            IQueryable<GeneticsParametersReference> genetics = geneticsParametersReferenceService.GetAll().Include(g => g.Genetics).Where(g => g.Genetics.HenBatch.Any(hb => hb.Id == henBatchId));
            return genetics;
        }

        public IQueryable<GeneticsParametersReference> GetGeneticsByHenBatchIds(List<Guid> henBatchIds)
        {
            IQueryable<GeneticsParametersReference> genetics = geneticsParametersReferenceService.GetAll().Include(g => g.Genetics).Where(g => g.Genetics.HenBatch.Any(hb => henBatchIds.Contains(hb.Id)));
            return genetics;
        }

        #endregion

    }
}