﻿#nullable enable
using Domain.Entities.Model;
using System;

namespace Domain.Logic.BusinessLogic.DTOs
{
    public class FilterDataDTO
    {
        public string FilterIdentifier { get; set; }
        public DateTime? MinDate { get; set; }
        public DateTime? MaxDate { get; set; }
        public int? MinWeek { get; set; }
        public int? MaxWeek { get; set; }
        public Guid? UserId { get; set; }
        public Guid? PersonId { get; set; }
        public Guid? FarmId { get; set; }
        public Guid? ClusterId { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid? WarehouseId { get; set; }
        public Guid? LineId { get; set; }
        public Guid? CageId { get; set; }
        public Guid? HenBatchId { get; set; }
        public HenStage? HenStage { get; set; }
        // Hen batch week number histogram bin limits
        public int HenBatchHistogramWeekNumberBinLimit1 { get; set; }
        public int HenBatchHistogramWeekNumberBinLimit2 { get; set; }
        public int HenBatchHistogramWeekNumberBinLimit3 { get; set; }
        public int HenBatchHistogramWeekNumberBinLimit4 { get; set; }
        public Guid? CapacityUnitId { get; set; }
        public Guid? ReportId { get; set; }
        public string ReportType { get; set; }

        public bool HasAuthorizationSearaCharts { get; set; } = false;

        public string ProducerName { get; set; }
        public Guid? ProductorId { get; set; }
        public Guid? BatchId { get; set; }
        public Guid? AviaryId { get; set; }
        public Guid? SampleCageId { get; set; }
        public Guid? RegionalId { get; set; }
        public Guid? UnitId { get; set; }
        public Guid? SupervisorId { get; set; }
        public Guid? ExtensionistId { get; set; }
        public Guid? GeneticsId { get; set; }
        public string Gender { get; set; }
        public string ConsolidatedOrOpen { get; set; }

        public bool UseMonthYearRange { get; set; }
        public int? StartMonth { get; set; }
        public int? StartYear { get; set; }
        public int? EndMonth { get; set; }
        public int? EndYear { get; set; }

        public bool UseWeekRange { get; set; }
        public int? StartWeek { get; set; }
        public int? EndWeek { get; set; }
        public string HenBatchStatus { get; set; }
        public FilterDataDTO() { }
        
        public int TotalWeeks => (EndWeek ?? 0) - (StartWeek ?? 0) + 1;
        
        public void SetDefaultWeekRange(int defaultEnd, int defaultStart = 0)
        {
            StartWeek ??= defaultStart;
            EndWeek ??= defaultEnd;
        }

        public static FilterDataDTO OfDefaultFilter(FilterDataDTO? filter, int defaultEnd, int defaultStart = 0)
        {
            filter ??= new FilterDataDTO();
            filter.SetDefaultWeekRange(defaultEnd, defaultStart);

            return filter;
        }
        
        public bool IsConsolidatedReportType => ReportType == "1";
    }
}
