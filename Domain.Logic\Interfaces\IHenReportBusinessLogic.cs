using Binit.Framework.Helpers.Excel;
using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.HenReportDTOs;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Binit.Shaper.Entities.Draft;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Domain.Entities.Model.Views;
using Domain.Logic.DTOs.HenReportDTOs;
using Domain.Entities.DTOs;

namespace Domain.Logic.Interfaces
{
    public interface IHenReportBusinessLogic
    {
        /// <summary>
        /// GetAll hen batches with relationships and extra data to build data grid rows
        /// </summary>
        IQueryable<HenReportGridView> GetAllForIndex(Dictionary<string, string> filters);

        /// <summary>
        /// Creates a hen report, fetch a hen batch performance for the same day and hen batch,
        /// creates it if it does not exist and add the values of the report to it.
        /// </summary>
        Task CreateAsync(HenReport henReport, HenReportFeedIntakeDTO feedIntakeDTO);

        /// <summary>
        /// Get the values of the values of a hen report to its relatives base values.
        /// </summary>
        HenReportDTO GetBaseValues(HenReportDTO henReportDTO, HenStage henStage);

        /// <summary>
        /// returns the options for farm, cluster and warehouse 
        /// </summary>
        List<List<SelectListItem>> GetContainers(HenBatchFilterDTO d);

        List<SelectListItem> GetWareHouses(HenBatchFilterDTO data);
        List<SelectListItem> GetClusters(HenBatchFilterDTO data);

        List<HenReportHenBatchDTO> GetActiveHenBatchesByFarm(Guid farmId, HenStage henStage);
        List<HenReportWarehouseDTO> GetWarehousesWithLinesByBatch(Guid farmId);
        List<PlannedGADDTO> GetPlannedGADByBatch(Guid parentBatchId, DateTime? date, bool shouldUseBatchWeekNumber = false);

        /// <summary>
        /// Searches hen reports and exports them as an excel file.
        /// If no search term is provided, returns all hen reports.
        /// </summary>
        Task<ExportResult> ExportExcel(Dictionary<string, string> data, string searchTerm = null, HenStage? henStage = null);

        /// <summary>
        /// Returns containers that have material stock of the formulas associated with the hen batch
        /// </summary>
        IQueryable<Container> GetFeedIntakeOrigins(Guid henBatchId);

        /// <summary>
        /// Returns material batches from selected container with material of the formulas associated with the hen batch
        /// </summary>
        IQueryable<MaterialBatchContainer> GetAvailableMaterialBatches(Guid henBatchId, Guid originId);

        bool CheckIfProduces(Guid originId);

        /// <summary>
        /// Validate HenReportDTO and create the entity
        /// </summary>
        Task<HenReport> ValidateDTOAndCreateEntity(HenReportAPI dto);

        /// <summary>
        /// Creates hen report draft and ReportRectification
        /// </summary>
        Task CreateHenReportDraft(Draft draft, Guid henReportId, HenReport newHenReport, HenReportRectificationDTO differences);

        /// <summary>
        ///  returns a dto with the diference between the report and the rectification 
        /// </summary>
        HenReportRectificationDTO GetDifferences(HenReport report, Guid DDBBreportId, HenStage? henStage = null);

        /// <summary>
        /// Gets all the casualty reasons by area with it's quantities for male and female
        /// </summary>
        (List<string> headers, List<Dictionary<string, string>> table) GetDeathQuantitiesTable(Guid henReportId, HenStage henStage);

        /// <summary>
        /// Gets all the depopulation reasons by area with it's quantities for male and female
        /// </summary>
        (List<string> headers, List<Dictionary<string, string>> table) GetDepopulateTable(Guid henReportId, HenStage henStage);

        /// <summary>
        /// Update or create henreport's casualties from DeathQuantities table.
        /// </summary>
        Task EditDeathAndDepopulationQuantities(DeathAndDepopulationQuantitiesDTO deathAndDepopulationQuantitiesDTO);

        /// <summary>
        /// Update or create henreports casualties and depopulations
        /// </summary>
        Task EditDeathAndDepopulationQuantitiesByWarehouse(IEnumerable<CasualtiesAndDepopulations> casualtiesAndDepopulations);

        /// <summary>
        /// Removes the old hen report form the hen performnce, creates the new report and updates report rectification.
        /// </summary>
        Task ApproveRectificationAsync(HenReport henReport, Guid ReportRectificationId, Guid previousHenReportId, HenReportRectificationDTO differences);

        /// <summary>
        /// Creates all the hen reports from a warehouse and returns the Ids of the ones that have dead amounts declared
        /// </summary>
        Task<IEnumerable<Guid>> CreateWarehouseHenReports(List<HenBatchHenReportDTO> henReportDTOs, HenReport generalReport, List<HenReportEggsDTO> eggsDTO);

        /// <summary>
        /// Create warehouse reports async
        /// </summary>
        Task<IEnumerable<Guid>> CreateWarehouseHenReportsAsync(IEnumerable<Guid> reports, List<CreateFromWarehouseAPI> createFromWarehouseAPI);

        /// <summary>
        /// Function to create and approve an adjustment report when the user has the approver role 
        /// or when the original report is the latest  
        /// </summary>
        Task CreateAndApproveRectificationAsync(Guid henReportId, HenReport newHenReport, HenReportRectificationDTO differences, Draft draft);

        /// <summary>
        /// gets the last hen report for the corresponding hen batch and see if it is the same
        /// report that is being rectified
        /// </summary>
        bool LastReport(Guid henReportId, Guid henBatchId);

        /// <summary>
        /// Checks if a henreport has any pending adjustments
        /// </summary>
        bool HasPendingReports(Guid id);

        /// <summary>
        /// Imports henreports XLSX file and assign it to a new event with pending status.
        /// </summary>
        Task Import(IFormFile file);

        /// <summary>
        /// Creates a Report rectification to delete te hen report. 
        /// If the user has the corresponding roles the function deletes the hen report  
        /// </summary>
        Task CreateRequestToDelete(HenReport henReport, bool createAndApprove);

        /// <summary>
        /// Approves the delete request and deletes the hen report  
        /// </summary>
        Task ApproveDeleteRequest(Guid rectificationId, Guid henReportId);

        /// <summary>
        /// Process XLSX file from pendng status events.
        /// </summary>
        Task ProcessFiles();

        /// <summary>
        ///  Convert GAD to kilograms 
        /// </summary>
        decimal CalculateGAD(string value, Guid measure, Guid henBatchId, bool? female = null);

        /// <summary>
        /// Returns data to notify for exceeding threshold parameters
        /// </summary>
        Task<ParametersThatExceedThresholdDTO> GetDataThatExceedsThresholdParametersAsync(DateTime lastSuccessfulJobDate);

        /// <summary>
        /// Validates CreateFromWarehouseAPI and creates the HenBatchHenReportDTO.
        /// </summary>
        IEnumerable<HenBatchHenReportDTO> ValidateDTOAndCreateEntity(CreateFromWarehouseAPI dto);

        /// <summary>
        /// Validates List of CreateFromWarehouseAPI
        /// </summary>
        void ValidateDTO(List<CreateFromWarehouseAPI> dto);
        IEnumerable<HenBatchHenReportDTO> CreateEntity(CreateFromWarehouseAPI dto);

        void ValidateDate(DateTime reportDate, Guid henBatchId);
        void ValidateFirstProductionDate(DateTime reportDate, Guid? warehouseId = null, List<Guid> henbatches = null);
        Guid[] ValidateFormulasConsumed(Guid henBatchId);
        IQueryable<ContainerContainer> GetFeedIntakeOrigins(params Guid[] henBatches);

        /// <summary>
        /// Verified that the henBatchParent has a date of first production and a date of capitalization
        /// </summary>
        bool[] AlertPruductionDate(Guid henBatchId);

        /// <summary>
        /// Gets page of henreports by classified eggs with category code 
        /// </summary>
        Task<HenReportByCategoryPage> GetPageByCategory(DateTime from, DateTime? to, string farmCode, string categoryCode, int page, int pageSize);

        /// <summary>
        /// Gets page of henreport mortality by farm code
        /// </summary>
        Task<HenReportMortalityByHenbatchPage> GetPageByHenbatch(string farmCode, DateTime from, DateTime? to, int page, int pageSize);

        /// <summary>
        /// Gets GAD by farm
        /// </summary>
        Task<HenbatchReportGADPage> GetGAD(HenReportGADDTO henReportGADDTO);
    }
}