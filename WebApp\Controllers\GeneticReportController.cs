﻿using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs.DashboardDTOs;
using Domain.Logic.BusinessLogic.DTOs.Genetic;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using WebApp.Constants;
using WebApp.WebTools.Charts;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.GeneticReportController;
using Microsoft.EntityFrameworkCore;

namespace WebApp.Controllers
{
    [Authorize]
    public class GeneticReportController : Controller
    {
        private readonly IGeneticReportBusinessLogic geneticReportBusinessLogic;
        private readonly IHenBatchService henbatchService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IOperationContext operationContext;
        private readonly IFarmService farmService;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;
        private readonly IStatisticsBusinessLogic statisticsBusinessLogic;
        private readonly IExceptionManager exceptionManager;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IHappeningBusinessLogic happeningBusinessLogic;

        public GeneticReportController(
            IGeneticReportBusinessLogic geneticReportBusinessLogic,
            IHenBatchService henbatchService,
            IStringLocalizer<SharedResources> localizer,
            IOperationContext operationContext,
            IFarmService farmService,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IStatisticsBusinessLogic statisticsBusinessLogic,
            IExceptionManager exceptionManager,
            IService<TenantConfiguration> tenantConfigurationService,
            IHappeningBusinessLogic happeningBusinessLogic)
        {
            this.geneticReportBusinessLogic = geneticReportBusinessLogic;
            this.henbatchService = henbatchService;
            this.localizer = localizer;
            this.operationContext = operationContext;
            this.farmService = farmService;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
            this.statisticsBusinessLogic = statisticsBusinessLogic;
            this.exceptionManager = exceptionManager;
            this.tenantConfigurationService = tenantConfigurationService;
            this.happeningBusinessLogic = happeningBusinessLogic;
        }
        #region Genectics Chart
        public IActionResult Graphic(Guid? henBatchId, HenStage? henStage = null)
        {
            if (!CheckAuthorization(henStage))
                return Forbid();

            ViewData["Title"] = localizer[Lang.GeneticGraphicTitle] + GetHenStageString(henStage);

            SetViewDatasForGraphic(henStage);

            return View();
        }

        // this function sets the view data for combo boxes and preselected values
        public void SetViewDatasForGraphic(HenStage? henStage)
        {
            IQueryable<HenBatch> henBatches = this.henbatchService
                .GetAll()
                .Where(hb => hb.HenStage == henStage && !hb.DateEnd.HasValue);

            ViewData["HenStages"] = GetHenStages(active: null);
            ViewData["HenStage"] = henStage;

            IQueryable<Guid?> farmsId = henBatches.Select(hb => hb.FarmId).Distinct();
            IQueryable<Farm> farms = farmService.GetAll().Where(f => farmsId.Any(id => id == f.Id));
            bool farmIsUnique = farms.Count() == 1;

            List<SelectListItem> farmList = farms.Select(f => new SelectListItem($"{f.Code} | {f.Name}", f.Id.ToString(), farmIsUnique)).ToList();
            ViewData["Farms"] = farmList;

            if (farmIsUnique)
            {
                IQueryable<HenBatch> parentHenBatches = henBatches.Where(hb => hb.FarmId == farms.First().Id && !hb.ParentId.HasValue);
                bool parentHenBatchIsUnique = parentHenBatches.Count() == 1;

                ViewData["ParentHenBatches"] = parentHenBatches
                    .Select(phb => new SelectListItem(phb.Code, phb.Id.ToString(), parentHenBatchIsUnique)).ToList()
                    .OrderBy(sli => sli.Text).ToList();

                if (parentHenBatchIsUnique)
                {
                    henBatches = henBatches.Where(hb => hb.ParentId == parentHenBatches.First().Id);
                    bool henBatchIsUnique = henBatches.Count() == 1;

                    ViewData["HenBatches"] = henBatches
                    .Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString(), henBatchIsUnique)).ToList()
                    .OrderBy(sli => sli.Text).ToList();
                }
            }

            // view data required for the partial view
            ViewData["Chart"] = new PerformanceStandardsChart();

            ViewData["HenBatchStatus"] = GetStatusOptions();
        }

        private List<SelectListItem> GetStatusOptions()
        {
            List<SelectListItem> items = new List<SelectListItem>()
            {
                new SelectListItem(localizer[Lang.HenBatchStatusAll], ""),
                new SelectListItem(localizer[Lang.HenBatchStatusActive], "active"),
                new SelectListItem(localizer[Lang.HenBatchStatusClosed], "closed")
            };

            return items;
        }


        [HttpGet]
        public JsonResult GraphicBuilder(Guid henBatchId, int isParent)
        {
            bool isParentHenBatch = Convert.ToBoolean(isParent);
            HenBatch henBatch = henbatchService.Get(henBatchId);

            if (!CheckAuthorization(henBatch.HenStage))
                return Json(localizer[Lang.HandleUnauthorizedEx].Value);

            try
            {
                GeneticGraphicResponseDTO response = geneticReportBusinessLogic.GetGraphicData(henBatchId, isParentHenBatch);

                PerformanceStandardsChart chart = PerformanceStandardCharts(response);

                if (henBatch.FirstProductionDate.HasValue)
                {
                    chart.Annotations = new List<GeneticGraphicAnnotationsDTO>();
                    // Get warehouse happenings and label them

                    Guid warehouseId = Guid.NewGuid();

                    if (henBatch.LineId is null)
                    {
                        warehouseId = henbatchService.GetAll()
                            .Include(hb => hb.Line).ThenInclude(l => l.Warehouse)
                            .Where(hb => hb.ParentId == henBatch.Id).FirstOrDefault().Line.WarehouseId;
                    }
                    else
                        warehouseId = henBatch.Line.WarehouseId;

                    List<Happening> happenings = this.happeningBusinessLogic.GetHappeningsForContainer(warehouseId);

                    foreach (var happening in happenings)
                        happening.Name = "(" + this.localizer[Lang.ChartAnnotationWarehousePrefix] + ") " + happening.Name;

                    // Get hen batch happenings discarding warehouse happenings already contemplated
                    List<Happening> henBatchOnlyHappenings = this.happeningBusinessLogic.GetHappeningsForContainer(henBatchId, happenings.Select(h => h.Id).ToList());

                    // Append hen batch only happenings to all happenings
                    happenings.AddRange(henBatchOnlyHappenings);
                    AddAnnotations(chart, henBatch, happenings);
                }

                return new JsonResult(new
                {
                    chart,
                    henBatchStatus = !henBatch.DateEnd.HasValue ? "active" : "closed"
                });
            }
            catch (NotFoundException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    ex.Message
                });
            }
        }

        private PerformanceStandardsChart PerformanceStandardCharts(GeneticGraphicResponseDTO response)
        {
            //simple: one std value (no max and min). complex: two std values
            List<ChartDataPoint> simpleMortDataFemale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleMortCurrentDataFemale = new List<ChartDataPoint>();

            List<ChartDataPoint> simpleBodyWeightMaxDataFemale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleBodyWeightMaxDataMale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleBodyWeightCurrentDataFemale = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleBodyWeightCurrentDataMale = new List<ChartDataPoint>();

            List<ChartDataPoint> simpleTemperatureMaxData = new List<ChartDataPoint>();
            List<ChartDataPoint> simpleTemperatureMinData = new List<ChartDataPoint>();

            //keep track of largest and smallest values to build boundaries
            int minValueBodyWeight = 2800;
            int maxValueBodyWeight = 0;

            PerformanceStandardsChart chart = new PerformanceStandardsChart()
            {
                SimpleDatasets = new List<PerformanceStandardsSimpleDataset>(),
                HenStage = response.HenStage
            };

            if (response.HenStage == HenStage.Laying)
            {
                //I will also have graphic data related to egg production
                List<ChartDataPoint> simpleCurrentHatchableEggsWeekly = new List<ChartDataPoint>();
                List<ChartDataPoint> simpleCurrentFertileEggsWeekly = new List<ChartDataPoint>();
                List<ChartDataPoint> simpleCurrentTotalProducedEggsWeekly = new List<ChartDataPoint>();

                List<ChartDataPoint> simpleCurrentHatchableEggsWeeklySTD = new List<ChartDataPoint>();
                List<ChartDataPoint> simpleCurrentFertileEggsWeeklySTD = new List<ChartDataPoint>();
                List<ChartDataPoint> simpleTotalProducedEggsWeeklySTD = new List<ChartDataPoint>();


                foreach (GeneticGraphicDTO parameters in response.Data.OrderBy(x => x.WeekNumber))
                {
                    //Current
                    simpleCurrentTotalProducedEggsWeekly.Add(new ChartDataPoint(parameters.WeekNumber, parameters.TotalProducedEggsWeekly));
                    simpleCurrentHatchableEggsWeekly.Add(new ChartDataPoint(parameters.WeekNumber, parameters.HatchableEggsWeekly));
                    simpleCurrentFertileEggsWeekly.Add(new ChartDataPoint(parameters.WeekNumber, parameters.FertileEggsWeekly));
                    simpleBodyWeightCurrentDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightFemale));
                    simpleBodyWeightCurrentDataMale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightMale));
                    simpleMortCurrentDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MortalityFemale));
                    simpleTemperatureMaxData.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MaxTemp));
                    simpleTemperatureMinData.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MinTemp));
                    //Standard
                    simpleBodyWeightMaxDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightSTDMaxFemale));
                    simpleBodyWeightMaxDataMale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightSTDMaxMale));
                    simpleCurrentHatchableEggsWeeklySTD.Add(new ChartDataPoint(parameters.WeekNumber, parameters.HatchingEggsWeeklySTD));
                    simpleCurrentFertileEggsWeeklySTD.Add(new ChartDataPoint(parameters.WeekNumber, parameters.FertileEggsWeeklySTD));
                    simpleTotalProducedEggsWeeklySTD.Add(new ChartDataPoint(parameters.WeekNumber, parameters.TotalProducedEggsWeeklySTD));
                    simpleMortDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MortalitySTDFemale));

                    SetMinMaxValueBodyWeight(ref minValueBodyWeight, ref maxValueBodyWeight, parameters);
                }

                chart.SimpleDatasets.Add(
                    new PerformanceStandardsSimpleDataset()
                    {
                        Label = localizer[Lang.TotalProducedEggsWeekly],
                        YAxisID = "performance",
                        BorderColor = "rgba(255,0,0,0.9)",
                        Data = CorrectedData(simpleTotalProducedEggsWeeklySTD),
                        CurrentData = CorrectedData(simpleCurrentTotalProducedEggsWeekly)
                    });

                chart.SimpleDatasets.Add(
                    new PerformanceStandardsSimpleDataset()
                    {
                        Label = localizer[Lang.HatchableEggsWeekly],
                        YAxisID = "performance",
                        BorderColor = "rgba(68,194,37,0.9)",
                        Data = CorrectedData(simpleCurrentHatchableEggsWeeklySTD),
                        CurrentData = CorrectedData(simpleCurrentHatchableEggsWeekly)
                    });
                chart.SimpleDatasets.Add(
                    new PerformanceStandardsSimpleDataset()
                    {
                        Label = localizer[Lang.FertileEggsWeekly],
                        YAxisID = "performance",
                        BorderColor = "rgba(0,0,205,0.9)",
                        Data = CorrectedData(simpleCurrentFertileEggsWeeklySTD),
                        CurrentData = CorrectedData(simpleCurrentFertileEggsWeekly)
                    });

            }
            else
            {
                foreach (GeneticGraphicDTO parameters in response.Data.OrderBy(x => x.WeekNumber))
                {
                    //Current
                    simpleBodyWeightCurrentDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightFemale));
                    simpleBodyWeightCurrentDataMale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightMale));
                    simpleMortCurrentDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MortalityFemale));
                    simpleTemperatureMaxData.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MaxTemp));
                    simpleTemperatureMinData.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MinTemp));
                    //Standard
                    simpleBodyWeightMaxDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightSTDMaxFemale));
                    simpleBodyWeightMaxDataMale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.BodyWeightSTDMaxMale));
                    simpleMortDataFemale.Add(new ChartDataPoint(parameters.WeekNumber, parameters.MortalitySTDFemale));

                    SetMinMaxValueBodyWeight(ref minValueBodyWeight, ref maxValueBodyWeight, parameters);

                }
            }

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[Lang.BodyWeightDataFemale],
                    YAxisID = "bodyWeight",
                    BorderColor = "rgba(255,215,0,0.9)",
                    Data = CorrectedData(simpleBodyWeightMaxDataFemale),
                    CurrentData = CorrectedData(simpleBodyWeightCurrentDataFemale)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[Lang.BodyWeightDataMale],
                    YAxisID = "bodyWeight",
                    BorderColor = "rgba(255,140,0,0.9)",
                    Data = CorrectedData(simpleBodyWeightMaxDataMale),
                    CurrentData = CorrectedData(simpleBodyWeightCurrentDataMale)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = localizer[Lang.MortalityDataFemale],
                    YAxisID = "mortality",
                    BorderColor = "rgba(0,0,0,0.9)",
                    Data = CorrectedData(simpleMortDataFemale),
                    CurrentData = CorrectedData(simpleMortCurrentDataFemale)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = this.localizer[Lang.TempMaxData],
                    YAxisID = "performance",
                    BorderColor = "rgba(0,206,209,0.9)",
                    CurrentData = CorrectedData(simpleTemperatureMaxData)
                });

            chart.SimpleDatasets.Add(
                new PerformanceStandardsSimpleDataset()
                {
                    Label = this.localizer[Lang.TempMinData],
                    YAxisID = "performance",
                    BorderColor = "rgba(135,206,235,0.9)",
                    CurrentData = CorrectedData(simpleTemperatureMinData)
                });

            chart.BodyWeightAxesBoundaries = new int[] { (int)(minValueBodyWeight * 0.9), (int)(maxValueBodyWeight * 1.1) };

            return chart;
        }

        private List<ChartDataPoint> CorrectedData(List<ChartDataPoint> chartDataPoints)
        {
            List<ChartDataPoint> correctChartDataPoints = new List<ChartDataPoint>();
            double firstXWithValue = 0;
            double lastXWithValue = 0;
            firstXWithValue = chartDataPoints.Any(d => d.Y.HasValue && d.Y > 0) ? chartDataPoints.FirstOrDefault(d => d.Y.HasValue && d.Y > 0).X : firstXWithValue;
            lastXWithValue = chartDataPoints.Any(d => d.Y.HasValue && d.Y > 0) ? chartDataPoints.LastOrDefault(d => d.Y.HasValue && d.Y > 0).X : lastXWithValue;

            correctChartDataPoints = chartDataPoints.Select(d => d.Y == 0 && d.X > firstXWithValue && d.X < lastXWithValue ?
                new ChartDataPoint(d.X, (chartDataPoints.FirstOrDefault(s => s.X == d.X - 1).Y + chartDataPoints.FirstOrDefault(s => s.X == d.X + 1).Y) / 2, 0)
                :
                d.Y == 0 ? new ChartDataPoint(d.X, (double?)null)
                :
                d).ToList();

            return correctChartDataPoints;
        }

        private void SetMinMaxValueBodyWeight(ref int minValueBodyWeight, ref int maxValueBodyWeight, GeneticGraphicDTO parameters)
        {
            int bodyWeightFemale = parameters.BodyWeightFemale.HasValue ? (int)parameters.BodyWeightFemale : 0;
            int bodyWeightMale = parameters.BodyWeightMale.HasValue ? (int)parameters.BodyWeightMale : 0;
            int bodyWeightSTDMinFemale = parameters.BodyWeightSTDMinFemale.HasValue ? (int)parameters.BodyWeightSTDMinFemale : 0;
            int bodyWeightSTDMinMale = parameters.BodyWeightSTDMinMale.HasValue ? (int)parameters.BodyWeightSTDMinMale : 0;
            int bodyWeightSTDMaxFemale = parameters.BodyWeightSTDMaxFemale.HasValue ? (int)parameters.BodyWeightSTDMaxFemale : 0;
            int bodyWeightSTDMaxMale = parameters.BodyWeightSTDMaxMale.HasValue ? (int)parameters.BodyWeightSTDMaxMale : 0;

            var minValue = Math.Min(Math.Min(Math.Min(Math.Min(bodyWeightFemale, minValueBodyWeight), bodyWeightMale), bodyWeightSTDMinFemale), bodyWeightSTDMinMale);

            maxValueBodyWeight = Math.Max(Math.Max(Math.Max(Math.Max(bodyWeightFemale, bodyWeightMale), bodyWeightSTDMaxFemale), bodyWeightSTDMaxMale), maxValueBodyWeight);

            if (minValue > 0)
                minValueBodyWeight = minValue;
        }


        private void AddAnnotations(PerformanceStandardsChart chart, HenBatch henBatch, List<Happening> happenings)
        {
            DayOfWeek dayOfWeek = farmService.GetAll().Where(f => f.Id == henBatch.FarmId).First().DayOfWeek;
            static int GetCurrentWeekNumberForDate(HenBatch henBatch, DateTime date, DayOfWeek dayOfWeek)
            {
                DateTime weekStart = henBatch.DateStart.Value.GetPastSelectedDay(dayOfWeek);

                return henBatch.BatchWeekNumber + (int)((date - weekStart).Days / 7);
            }

            chart.Annotations.Add
            (
                new GeneticGraphicAnnotationsDTO()
                {
                    Type = "line",
                    Mode = "vertical",
                    Id = "firstProductionDate",
                    XScaleID = "weeksFromBirth",
                    Value = GetCurrentWeekNumberForDate(henBatch, DateTime.Today, dayOfWeek),
                    BorderColor = "rgba(0,0,0,1)",
                    Label = new GeneticGraphicAnnotationLabelDTO()
                    {
                        Content = this.localizer[Lang.ChartAnnotationToday],
                        Enabled = true,
                        Position = "center"
                    },
                }
            );

            List<double> weeks = chart.SimpleDatasets.FirstOrDefault().Data.Select(d => d.X).ToList();

            // Variables for styling annotations
            // Spread labels vertically
            int yAdjust = 0;
            int adjustStep = 50;
            int totalHappenings = happenings.Count;
            string position = "center";

            // Add annotations
            foreach (Happening happening in happenings)
            {
                string color = happening.HappeningType.Color;
                if (happening.HappeningType.ColorActivated)
                {
                    if (color.Length == 7)
                    {
                        // converting in decimal values
                        Int32 red10 = Convert.ToInt32(color.Substring(1, 2), 16);
                        Int32 green10 = Convert.ToInt32(color.Substring(3, 2), 16);
                        Int32 blue10 = Convert.ToInt32(color.Substring(5, 2), 16);

                        string rgb = red10.ToString() + ',' + green10.ToString() + ',' + blue10.ToString();

                        color = "rgba(" + rgb + ",0.7)";
                    }

                }
                else color = "rgba(0,0,0,1)";

                int start = GetCurrentWeekNumberForDate(henBatch, happening.DateStart, dayOfWeek);
                int? end = null;
                if (happening.DateEnd != null)
                    end = GetCurrentWeekNumberForDate(henBatch, happening.DateEnd.Value, dayOfWeek);

                // Check if happening fits in hen batch hen stage week span
                if ((weeks.Min() < start && start < weeks.Max()) || (end != null && (weeks.Min() < end.Value && end.Value < weeks.Max())))
                {
                    // Single line annotaions
                    if (end == null || start == end.Value)
                    {

                        chart.Annotations.Add
                        (
                            new GeneticGraphicAnnotationsDTO()
                            {
                                Type = "line",
                                Mode = "vertical",
                                Id = $"happening-{happening.Id}",
                                XScaleID = "weeksFromBirth",
                                Value = start,
                                BorderColor = color,
                                Label = new GeneticGraphicAnnotationLabelDTO()
                                {
                                    Content = happening.Name,
                                    Enabled = true,
                                    Position = position,
                                    YAdjust = yAdjust
                                },
                                RedirectionURL = happening.Id.ToString()
                            }
                        );
                    }
                    // Box annotations
                    else
                    {
                        chart.Annotations.Add
                        (
                            new GeneticGraphicAnnotationsDTO()
                            {
                                Type = "box",
                                Id = $"happening-{happening.Id}",
                                XScaleID = "weeksFromBirth",
                                YScaleID = "performance",
                                XMin = start,
                                XMax = end.Value,
                                YMin = 0,
                                YMax = 100,
                                BorderWidth = 2,
                                BackgroundColor = color,
                                BorderColor = color,
                                RedirectionURL = happening.Id.ToString()
                            }
                        );

                        // Workaround for adding an annotation to box styled ones. Annotations are not supported at the time being (21/09/2020)
                        chart.Annotations.Add
                        (
                            new GeneticGraphicAnnotationsDTO()
                            {
                                Type = "line",
                                Id = $"happening-{happening.Id}",
                                Mode = "vertical",
                                XScaleID = "weeksFromBirth",
                                Value = start,
                                BorderColor = color,
                                Label = new GeneticGraphicAnnotationLabelDTO()
                                {
                                    Content = happening.Name,
                                    Enabled = true,
                                    Position = position,
                                    YAdjust = yAdjust,
                                    XAdjust = (end.Value - start) / 2
                                },
                                RedirectionURL = happening.Id.ToString()
                            }
                        );
                    }
                }
                // spread labels vertically
                yAdjust += adjustStep;
            }
        }

        [HttpGet]
        public JsonResult GeneticModal(Guid henBatchId)
        {
            Genetic genetic = henbatchService.GetFull(henBatchId).Genetic;
            return Json(new { genetic.Name, Image = genetic.ImageId, File = genetic.ManualId });
        }
        #endregion

        #region HenBatch detail charts

        public IActionResult Benchmark(HenStage? henStage, Guid? henBatchId)
        {
            HenBatch henBatch = null;

            if (henBatchId.HasValue)
                henBatch = this.henbatchService.GetAllWithFarmAndGenetic(asNoTracking: true).FirstOrDefault(hb => hb.Id == henBatchId);

            if (!CheckAuthorization(henBatch != null ? henBatch.HenStage : henStage))
                return Forbid();

            ViewData["Title"] = localizer[Lang.DetailChartsTitle] + (henBatch != null ? (" | " + henBatch.DetailedName + " | " + henBatch.Genetic.Name) : "");
            ViewData["HenStage"] = henStage;
            ViewData["MinWeek"] = henBatch != null ? henBatch.BatchWeekNumber.ToString() : "";
            ViewData["MaxWeek"] = henBatch != null ? this.henbatchService.GetCurrentWeekNumber(henBatch.Id).ToString() : "";
            ViewData["HenBatchIds1"] = henBatch != null ? new List<SelectListItem>() { new SelectListItem() { Value = henBatch.Id.ToString(), Text = henBatch.Name, Selected = true } } : new List<SelectListItem>();
            ViewData["Genetics"] = this.henBatchBusinessLogic.GetAllGenetics();
            ViewData["Formulas"] = this.henBatchBusinessLogic.GetAllFormulasByHenStage(henStage.Value);
            ViewData["HasCluster"] = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["LoadAction"] = ControllerActions.GetGroupedStatistics;
            ViewData["PivotGridResources"] = JsLocalizer.GetLocalizedResources(JsLang.OperationsPivotGridTable, localizer);

            return View();
        }

        [HttpPost]
        public JsonResult GetComparisonSelectOptions(ComparisonChartFilterDTO filters)
        {
            var options = this.statisticsBusinessLogic.GetComparisonFilterOptions(
                filters.SelectedFarms,
                filters.SelectedClusters,
                filters.SelectedWarehouses,
                filters.SelectedLines,
                filters.SelectedBatches,
                filters.SelectedGenetics,
                filters.SelectedFormulas,
                filters.Active,
                filters.HenStage,
                filters.Term,
                filters.FilterKey
                );

            return new JsonResult(new { results = options.Select(o => new { id = o.Value, text = o.Text }) });
        }

        [HttpPost]
        public JsonResult GetHenBatchesForDetialCharts(List<Guid> geneticIds, HenStage? henStage, bool? active)
        {
            return new JsonResult(this.henBatchBusinessLogic.GetAllByGenetic(geneticIds, henStage, active));
        }

        private void ParseFilterDTO(ComparisonChartFilterDTO data)
        {
            if (data.MinWeek.HasValue && data.MaxWeek.HasValue && data.MinWeek.Value > data.MaxWeek.Value)
                throw exceptionManager.Handle(new ValidationException(this.localizer[Lang.DetailChartsFilterErrorMixedWeeks]));
        }

        [HttpPost]
        public IActionResult GetDataForHenBatchDetailTemperatureAndMortalityChart(ComparisonChartFilterDTO filters1, ComparisonChartFilterDTO filters2)
        {
            try
            {
                ParseFilterDTO(filters1);

                List<Guid> henBatchIds1 = this.statisticsBusinessLogic.GetComparisonFilteredHenBatches(
                filters1.SelectedFarms,
                filters1.SelectedClusters,
                filters1.SelectedWarehouses,
                filters1.SelectedLines,
                filters1.SelectedBatches,
                filters1.SelectedGenetics,
                filters1.SelectedFormulas,
                filters1.Active,
                filters1.HenStage
                );
                List<Guid> henBatchIds2 = this.statisticsBusinessLogic.GetComparisonFilteredHenBatches(
                    filters2.SelectedFarms,
                    filters2.SelectedClusters,
                    filters2.SelectedWarehouses,
                    filters2.SelectedLines,
                    filters2.SelectedBatches,
                    filters2.SelectedGenetics,
                    filters2.SelectedFormulas,
                    filters2.Active,
                    filters2.HenStage
                    );

                return new JsonResult(this.statisticsBusinessLogic.GetDataForHenBatchDetailTemperatureAndMortalityChart(henBatchIds1, henBatchIds2, filters1.MinWeek, filters1.MaxWeek));
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    ex.Message
                });
            }
            catch
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    Message = this.localizer[Lang.DetailChartsLoadingError]
                });
            }
        }

        [HttpPost]
        public IActionResult GetDataForHenBatchDetailWeightChart(ComparisonChartFilterDTO filters1, ComparisonChartFilterDTO filters2)
        {
            try
            {
                ParseFilterDTO(filters1);

                List<Guid> henBatchIds1 = this.statisticsBusinessLogic.GetComparisonFilteredHenBatches(
                filters1.SelectedFarms,
                filters1.SelectedClusters,
                filters1.SelectedWarehouses,
                filters1.SelectedLines,
                filters1.SelectedBatches,
                filters1.SelectedGenetics,
                filters1.SelectedFormulas,
                filters1.Active,
                filters1.HenStage
                );
                List<Guid> henBatchIds2 = this.statisticsBusinessLogic.GetComparisonFilteredHenBatches(
                    filters2.SelectedFarms,
                    filters2.SelectedClusters,
                    filters2.SelectedWarehouses,
                    filters2.SelectedLines,
                    filters2.SelectedBatches,
                    filters2.SelectedGenetics,
                    filters2.SelectedFormulas,
                    filters2.Active,
                    filters2.HenStage
                    );

                return new JsonResult(this.statisticsBusinessLogic.GetDataForHenBatchDetailWeightChart(henBatchIds1, henBatchIds2, filters1.MinWeek, filters1.MaxWeek));
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    ex.Message
                });
            }
            catch
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    Message = this.localizer[Lang.DetailChartsLoadingError]
                });
            }
        }

        [HttpPost]
        public IActionResult GetDataForHenBatchDetailComparisonChart(ComparisonChartFilterDTO filters1, ComparisonChartFilterDTO filters2)
        {
            List<Guid> henBatchIds1 = this.statisticsBusinessLogic.GetComparisonFilteredHenBatches(
                filters1.SelectedFarms,
                filters1.SelectedClusters,
                filters1.SelectedWarehouses,
                filters1.SelectedLines,
                filters1.SelectedBatches,
                filters1.SelectedGenetics,
                filters1.SelectedFormulas,
                filters1.Active,
                filters1.HenStage
                );
            List<Guid> henBatchIds2 = this.statisticsBusinessLogic.GetComparisonFilteredHenBatches(
                filters2.SelectedFarms,
                filters2.SelectedClusters,
                filters2.SelectedWarehouses,
                filters2.SelectedLines,
                filters2.SelectedBatches,
                filters2.SelectedGenetics,
                filters2.SelectedFormulas,
                filters2.Active,
                filters2.HenStage
                );
            try
            {
                return new JsonResult(this.statisticsBusinessLogic.GetDataForHenBatchDetailComparisonChart(henBatchIds1, henBatchIds2));
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    ex.Message
                });
            }
            catch (Exception e)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return new JsonResult(new
                {
                    Message = this.localizer[Lang.DetailChartsLoadingError]
                });
            }
        }

        #endregion

        #region GetEntities
        public List<SelectListItem> GetHenBatches(HenStage? henStage = null, bool? active = null)
        {
            IQueryable<HenBatch> henBatches = geneticReportBusinessLogic.GetHenbatches(active);
            if (henStage.HasValue)
            {
                if (!CheckAuthorization(henStage))
                    return new List<SelectListItem>();
                henBatches = henBatches.Where(hb => hb.HenStage == henStage.Value);
            }
            else
                if (!CheckAuthorization())
                return new List<SelectListItem>();
            return henBatches.OrderBy(hb => hb.DetailedName)
                .Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString())).ToList();
        }

        public List<SelectListItem> GetParentHenBatchesByFarm(HenStage henStage, Guid selectedFarm, bool? active)
        {
            IQueryable<HenBatch> parentHenBatches = this.henbatchService.GetAll()
                .Where(hb =>
                    hb.HenStage == henStage
                    && hb.FarmId == selectedFarm
                    && (!active.HasValue || (active.Value ? !hb.DateEnd.HasValue : hb.DateEnd.HasValue))
                    && !hb.ParentId.HasValue);


            var items = parentHenBatches.Select(hb =>
                new SelectListItem()
                {
                    Text = hb.Code,
                    Value = hb.Id.ToString()
                })
                .OrderBy(sli => sli.Text).ToList();

            if (items.Count == 1)
                items.First().Selected = true;

            return items;
        }

        public List<SelectListItem> GetHenBatchesByParent(HenStage henStage, Guid selectedParent)
        {
            IQueryable<HenBatch> henBatches = this.henbatchService.GetAll().Where(hb => hb.HenStage == henStage && !hb.DateEnd.HasValue && hb.ParentId == selectedParent);

            bool henBatchIsUnique = henBatches.Count() == 1;

            return henBatches.Select(hb => new SelectListItem(hb.DetailedName, hb.Id.ToString(), henBatchIsUnique)).ToList()
                .OrderBy(sli => sli.Text).ToList();
        }

        public List<string> GetHenBatch(Guid? henBatchId)
        {
            HenBatch henBatch = this.henbatchService.GetAllFull().Where(hb => hb.Id == henBatchId)
                .FirstOrDefault();
            if (!CheckAuthorization(henBatch.HenStage))
                return new List<string>();
            return new List<string>() { henBatch.Genetic.Name, henBatch.Id.ToString(), henBatch.DetailedName };
        }

        private List<SelectListItem> GetHenStages(bool? active = true)
        {
            List<SelectListItem> henStages = new List<SelectListItem>();
            foreach (HenStage henStage in geneticReportBusinessLogic.GetHenStages(active))
            {
                string name = EnumHelper<HenStage>.GetDisplayName(henStage, localizer);
                string id = ((int)henStage).ToString();
                henStages.Add(new SelectListItem(name, id));
            }
            return henStages.OrderBy(hs => hs.Text).ToList();
        }

        #endregion

        private bool CheckAuthorization(HenStage? henStage = null, string? action = null)
        {
            if (henStage.HasValue)
            {
                if (action != "Benchmark")
                {
                    switch (henStage)
                    {
                        case HenStage.Laying:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingAdministrator,
                                Roles.BackofficeLayingUser,
                                Roles.BackofficeLayingReportsAdministrator,
                                Roles.BackofficeLayingReportsUser,
                                Roles.BackofficeLayingGeneticReportAdministrator,
                                Roles.BackofficeLayingGeneticReportUser))
                                return false;
                            break;

                        case HenStage.Breeding:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeBreedingAdministrator,
                                Roles.BackofficeBreedingUser,
                                Roles.BackofficeBreedingReportsAdministrator,
                                Roles.BackofficeBreedingReportsUser,
                                Roles.BackofficeBreedingGeneticReportAdministrator,
                                Roles.BackofficeBreedingGeneticReportUser))
                                return false;
                            break;

                        default:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator))
                                return false;
                            break;
                    }
                    ;
                    return true;
                }
                else
                {
                    switch (henStage)
                    {
                        case HenStage.Laying:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeLayingGeneticReportAdministrator,
                                Roles.BackofficeLayingBenchmark))
                                return false;
                            break;

                        case HenStage.Breeding:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator,
                                Roles.BackofficeBreedingGeneticReportAdministrator,
                                Roles.BackofficeBreedingBenchmark))
                                return false;
                            break;

                        default:
                            if (!this.operationContext.UserIsInAnyRole(
                                Roles.BackofficeSuperAdministrator))
                                return false;
                            break;
                    }
                    ;
                    return true;
                }
            }
            else
            {
                if (action != "Benchmark")
                {
                    if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingUser,
                            Roles.BackofficeLayingReportsAdministrator,
                            Roles.BackofficeLayingReportsUser,
                            Roles.BackofficeLayingGeneticReportAdministrator,
                            Roles.BackofficeLayingGeneticReportUser,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingUser,
                            Roles.BackofficeBreedingReportsAdministrator,
                            Roles.BackofficeBreedingReportsUser,
                            Roles.BackofficeBreedingGeneticReportAdministrator,
                            Roles.BackofficeBreedingGeneticReportUser))
                        return false;
                    else
                        return true;
                }
                else
                {
                    if (!this.operationContext.UserIsInAnyRole(
                           Roles.BackofficeSuperAdministrator,
                           Roles.BackofficeBreedingGeneticReportAdministrator,
                           Roles.BackofficeLayingGeneticReportAdministrator,
                           Roles.BackofficeBreedingBenchmark,
                           Roles.BackofficeLayingBenchmark))
                        return false;
                    else
                        return true;
                }
            }
        }

        private string GetHenStageString(HenStage? henStage)
        {
            if (henStage.HasValue)
            {
                return henStage switch
                {
                    HenStage.Breeding => localizer[Lang.Breeding],
                    _ => localizer[Lang.Laying],
                };
            }
            else
                return "";
        }
    }
}