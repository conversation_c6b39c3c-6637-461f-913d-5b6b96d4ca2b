@model WebApp.Models.WarehouseHenReportViewModel;
@using Binit.Framework;
@using Microsoft.Extensions.Localization;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenReport.CreateOrEdit;
@inject IStringLocalizer<SharedResources> localizer

@{
    var farms = ViewData["Farms"] as List<SelectListItem>;
}

<link href="~/css/handsontable.css" rel="stylesheet" />
<style>
    #hot-display-license-info {
        display: none;
    }

    .ht_clone_top {
        z-index: 1
    }

    .htDimmed {
        background-color: #ddd !important;
    }

    #report-table {
        width: 100%;
        z-index: 1;
    }

    .table-wrapper {
        padding-top: 15px;
        z-index: 1;
        overflow: auto;
        /* background: #fff; */
        position: relative;
        /* ajuste conforme a largura da sua sidebar */
        /*margin-left: 250px; */
        /* ou padding-left: 250px; */
    }
</style>

<div class="row">
    <div class="col-md-6" id="FarmDiv" style="@(farms.Count() > 1 ? "" : "display:none" )">
        <label class="select">@localizer[Lang.LabelFarm]</label>
        <div class="form-group m-b-40">
            <select class="select2" id="FarmId" name="FarmId">
                <option value="">@localizer[Lang.PlaceholderSelectFarm]</option>
                @foreach (var item in farms)
                {
                    if (farms.Count() == 1 || item.Selected)
                    {
                        <option farm value="@item.Value" selected="@item.Selected">@item.Text</option>
                    }
                    else
                    {
                        <option farm value="@item.Value">@item.Text</option>
                    }
                }
            </select>
            <span asp-validation-for="FarmId" class="form-control-feedback"></span>
        </div>
    </div>

    <div class="col-md-6" id="HenBatchDiv">
        <label class="select">@localizer[Lang.LabelHenBatch]</label>
        <div class="form-group m-b-40">
            <select class="select2" id="HenBatchId" name="HenBatchId">
                <option value="">Selecione um lote</option>
            </select>
        </div>
    </div>
</div>

<div class="row floating-labels">
    <div class="col-md-6">
        <ignite-datetime-picker label="@localizer[Lang.LabelDate]" id="Date"></ignite-datetime-picker>
    </div>
</div>
<div class="row floating-labels">
    <div class="col-md-6 font-bold">
        <div id="HenBatchWeekNumber"></div>
    </div>
</div>

<div class="table-wrapper">
    <div id="report-table"></div>
</div>

<div class="d-flex justify-content-end mt-3">
    <button type="button" id="createButton" class="btn btn-themecolor">@localizer[Lang.BtnCreate]</button>
</div>

@section scripts {
    <script>
    </script>
    <script src="~/js/handsontable.js"></script>
    <script src="~/js/views/henReport/createLaying.js"></script>
}

<ignite-load plugins="select2,date-time-picker"></ignite-load>