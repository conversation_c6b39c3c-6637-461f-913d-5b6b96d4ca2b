using Domain.Entities.Model;
using Domain.Logic.BusinessLogic.DTOs.HenReportDTOs;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Logic.Interfaces
{
    public interface ICreateHenReports
    {
        public Task<IEnumerable<Guid>> CreateWarehouseHenReports(List<HenBatchHenReportDTO> henReportDTOs, HenReport generalReport, List<HenReportEggsDTO> eggsDTO);
        public Task<IEnumerable<HenReportResultDTO>> CreateHenReportsFromTable(DateTime reportDate, List<HenReportFromTableDTO> henReportDTOs);
        public Task<IEnumerable<HenReportResultDTO>> CreateBreedingReportsFromTable(DateTime reportDate, List<BreedingReportFromTableDTO> henReportDTOs);
        public Task<IEnumerable<HenReportResultDTO>> CreateLayingReportsFromTable(DateTime reportDate, List<LayingReportFromTableDTO> henReportDTOs);
        public Task<IEnumerable<HenReportResultDTO>> CreateWarehouseReportsFromTable(CreateWarehouseReportFromTableDTO createDto);
    }
}
