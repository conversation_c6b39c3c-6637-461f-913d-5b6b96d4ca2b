using Domain.Logic.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Binit.Framework;
using Microsoft.Extensions.Localization;
using Binit.Framework.ExceptionHandling.Types;
using Domain.Entities.Model;

using Binit.Framework.Helpers.Excel;
using OfficeOpenXml.Style;
using System.IO;
using OfficeOpenXml;
using Domain.Logic.BusinessLogic.DTOs;

namespace Domain.Logic.BusinessLogic
{
    public class ManagerialLayingReportBusinessLogic : IManagerialLayingReportBusinessLogic
    {
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly ISampleCageReportService sampleCageReportService;
        private readonly IGeneticsParameterService geneticsParameterService;
        private readonly IHenBatchService henBatchService;
        private readonly IHenReportService henReportService;
        private readonly IStringLocalizer<SharedResources> localizer;

        public ManagerialLayingReportBusinessLogic(
            IHenBatchPerformanceService henBatchPerformanceService,
            ISampleCageReportService sampleCageReportService,
            IGeneticsParameterService geneticsParameterService,
            IHenBatchService henBatchService,
            IHenReportService henReportService,
            IStringLocalizer<SharedResources> localizer)
        {
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.sampleCageReportService = sampleCageReportService;
            this.geneticsParameterService = geneticsParameterService;
            this.henBatchService = henBatchService;
            this.henReportService = henReportService;
            this.localizer = localizer;
        }

        public ExportResult GenerateReportInExcel(ManagerialLayingReportFilterDTO filters)
        {
            ManagerialLayingReportDTO report = GetReport(filters);
            return ExportExcel(report);
        }

        private ManagerialLayingReportDTO GetReport(ManagerialLayingReportFilterDTO filters)
        {
            var parentHenBatch = henBatchService.GetAll()
              .AsNoTracking()
              .Include(h => h.Farm)
              .FirstOrDefault(h => h.Id == filters.ParentHenBatch);

            if (parentHenBatch == null)
            {
                throw new NotFoundException(localizer["Nenhum lote encontrado"]);
            }

            var childHenBatches = henBatchService.GetAll()
                .AsNoTracking()
                .Where(h => h.ParentId == parentHenBatch.Id)
                .ToList();

            // if there are no child hen batches, data will be retrieved from parent hen batch
            if (childHenBatches.Count == 0)
            {
                childHenBatches.Add(parentHenBatch);
            }

            ManagerialLayingReportDTO report = new ManagerialLayingReportDTO()
            {
                Farm = parentHenBatch.Farm?.Name,
                ParentBatch = parentHenBatch.Code,
                ParentHenBatchPerformances = GetHenBatchPerformances(new List<Guid> { parentHenBatch.Id }),
                HenBatchPerformances = GetHenBatchPerformances(childHenBatches.Select(hb => hb.Id).ToList()),
                GeneticsParameters = GetGeneticsParameters(childHenBatches.Select(hb => hb.GeneticId).ToList())
            };

            return report;
        }

        private ExportResult ExportExcel(ManagerialLayingReportDTO report)
        {
            var stream = new MemoryStream();
            var output = new MemoryStream();

            using (ExcelPackage package = new ExcelPackage(stream))
            {
                ExcelWorksheet sheet = package.Workbook.Worksheets.Add("Produção de ovos");
                
                var reportSheet = new ReportSheet(sheet);
                
                const string yellowHex = "#FFFF00";
                var weeks = report.HenBatchPerformances.Select(hbp => hbp.WeekNumber).Distinct().ToList();
                
                reportSheet.SetWeeks(weeks, headerHexColor: yellowHex);
                reportSheet.RenderDataSeries(GetHenAmountMaleDataSeries(report.HenBatchPerformances), headerHexColor: yellowHex);
                reportSheet.RenderDataSeries(GetHenAmountFemaleDataSeries(report.HenBatchPerformances), headerHexColor: yellowHex);
                reportSheet.RenderDataGroup(new DataGroup("Viabilidade F (%)", new List<DataSeries> {
                    GetFemaleViabilityStandardSeries(report.GeneticsParameters),
                    GetFemaleViabilityActualSeries(report.HenBatchPerformances)
                }), yellowHex);
                reportSheet.RenderDataGroup(new DataGroup("Peso - Fêmeas (g)", new List<DataSeries> {
                    GetFemaleWeightStandardSeries(report.GeneticsParameters),
                    GetFemaleWeightActualSeries(report.HenBatchPerformances),
                    GetFemaleWeightDeviationSeries(report.GeneticsParameters, report.HenBatchPerformances)
                }), yellowHex);
                reportSheet.RenderDataSeries(GetFemaleUniformitySeries(report.HenBatchPerformances), headerHexColor: yellowHex);
                reportSheet.RenderDataGroup(new DataGroup("GAD Fêmeas (g)", new List<DataSeries> {
                    GetFemaleGADStandardSeries(report.GeneticsParameters),
                    GetFemaleGADActualSeries(report.HenBatchPerformances),
                }), yellowHex);
                reportSheet.RenderDataGroup(new DataGroup("Peso - Machos (g)", new List<DataSeries> {
                    GetMaleWeightStandardSeries(report.GeneticsParameters),
                    GetMaleWeightActualSeries(report.HenBatchPerformances),
                    GetMaleWeightDeviationSeries(report.GeneticsParameters, report.HenBatchPerformances)
                }), yellowHex);

                reportSheet.RenderDataSeries(GetMaleUniformitySeries(report.HenBatchPerformances, "Unif. M (%)"), headerHexColor: yellowHex);
                reportSheet.RenderDataGroup(new DataGroup("GAD Machos (g)", new List<DataSeries> {
                    GetMaleGADStandardSeries(report.GeneticsParameters),
                    GetMaleGADActualSeries(report.HenBatchPerformances),
                }), yellowHex);

                var childBatchIds = report.HenBatchPerformances.OrderBy(hbp => hbp.HenBatch.Line.Warehouse.Name).ThenBy(hbp => hbp.HenBatch.Line.Name).Select(hbp => hbp.HenBatchId).Distinct().ToList();
                
                string[] batchColors = {"#d9e5f6", "#fbd28b", "#d6eadf", "#FBE3D6"};
                
                for(var index = 0; index < childBatchIds.Count; index++)
                {
                    var colorIndex = index % batchColors.Length;
                    var batchId = childBatchIds[index];
                    var batchPerformances = report.HenBatchPerformances
                        .Where(hbp => hbp.HenBatchId == batchId)
                        .Where(hbp => hbp.HenBatch.InitialHenAmountFemale > 0)
                        .ToList();

                    var batch = batchPerformances.FirstOrDefault().HenBatch;

                    reportSheet.RenderDataGroup(new DataGroup(batch.Line.Warehouse.Name + " - " + batch.Line.Name, new List<DataSeries> {
                        GetFemaleWeightStandardSeries(report.GeneticsParameters, "Peso Padrão"),
                        GetFemaleWeightActualSeries(batchPerformances, "Peso Real"),
                        GetFemaleWeightDeviationSeries(report.GeneticsParameters, batchPerformances, "Dif (%)"),
                        GetFemaleUniformitySeries(batchPerformances, "Unif. F (%)"),
                        GetFemaleGADStandardSeries(report.GeneticsParameters, "GAD Padrão"),
                        GetFemaleGADActualSeries(batchPerformances, "GAD Real"),
                    }), batchColors[colorIndex]);
                }

                reportSheet.RenderDataGroup(new DataGroup("Peso - Machos (g)", new List<DataSeries> {
                    GetMaleWeightStandardSeries(report.GeneticsParameters),
                    GetMaleWeightActualSeries(report.HenBatchPerformances),
                    GetMaleWeightDeviationSeries(report.GeneticsParameters, report.HenBatchPerformances)
                }));

                reportSheet.RenderDataGroup(new DataGroup("Produção ovos (%)", new List<DataSeries> {
                    GetEggProductionStandardSeries(report.GeneticsParameters),
                    GetEggProductionActualSeries(report.HenBatchPerformances),
                }));

                reportSheet.RenderDataGroup(new DataGroup("Ovos Cama (%)", new List<DataSeries> {
                    // GetBedEggProductionStandardSeries(report.GeneticsParameters),
                    GetBedEggProductionActualSeries(report.HenBatchPerformances),
                }));

                reportSheet.RenderDataGroup(new DataGroup("Ovo Incubáveis / Fêmea Cap", new List<DataSeries> {
                    GetOIFCStandardSeries(report.GeneticsParameters),
                    GetOIFCActualSeries(report.HenBatchPerformances),
                }));

                reportSheet.RenderDataGroup(new DataGroup("Eclosão (%)", new List<DataSeries> {
                    GetHatchingPercentageStandardSeries(report.GeneticsParameters),
                    GetHatchingPercentageActualSeries(report.ParentHenBatchPerformances),
                }));

                reportSheet.RenderDataGroup(new DataGroup("Fertilidade (%)", new List<DataSeries> {
                    GetFertileEggsStandardSeries(report.GeneticsParameters),
                    GetFertileEggsActualSeries(report.ParentHenBatchPerformances),
                }));

                reportSheet.RenderDataGroup(new DataGroup("Relação Macho/Fêmea (%)", new List<DataSeries> {
                    GetMaleFemaleRatioStandardSeries(report.GeneticsParameters),
                    GetMaleFemaleRatioActualSeries(report.HenBatchPerformances),
                }));

                reportSheet.Style();

                package.SaveAs(output);
            }
            output.Position = 0;

            return new ExportResult(output, "Report.xlsx");
        }

        public ManagerialLayingReportDataDTO GetReportData(ManagerialLayingReportFilterDTO filters)
        {
            var report = GetReport(filters);
            return new ManagerialLayingReportDataDTO()
            {
                Weeks = report.HenBatchPerformances.Select(hbp => hbp.WeekNumber).Distinct().ToList(),
                HenAmountMale = GetHenAmountMaleDataSeries(report.HenBatchPerformances),
                HenAmountFemale = GetHenAmountFemaleDataSeries(report.HenBatchPerformances),
                FemaleViability = new DataGroup("Viabilidade F (%)", new List<DataSeries> {
                    GetFemaleViabilityStandardSeries(report.GeneticsParameters),
                    GetFemaleViabilityActualSeries(report.HenBatchPerformances)
                }),
                FemaleWeight = new DataGroup("Peso - Fêmeas (g)", new List<DataSeries> {
                    GetFemaleWeightStandardSeries(report.GeneticsParameters),
                    GetFemaleWeightActualSeries(report.HenBatchPerformances),
                    GetFemaleWeightDeviationSeries(report.GeneticsParameters, report.HenBatchPerformances)
                }),
                FemaleUniformity = GetFemaleUniformitySeries(report.HenBatchPerformances),
                FemaleGAD = new DataGroup("GAD Fêmeas (g)", new List<DataSeries> {
                    GetFemaleGADStandardSeries(report.GeneticsParameters),
                    GetFemaleGADActualSeries(report.HenBatchPerformances)
                }),
                MaleWeight = new DataGroup("Peso - Machos (g)", new List<DataSeries> {
                    GetMaleWeightStandardSeries(report.GeneticsParameters),
                    GetMaleWeightActualSeries(report.HenBatchPerformances),
                    GetMaleWeightDeviationSeries(report.GeneticsParameters, report.HenBatchPerformances)
                }),
                MaleUniformity = GetMaleUniformitySeries(report.HenBatchPerformances, "Unif. M (%)"),
                MaleGAD = new DataGroup("GAD Machos (g)", new List<DataSeries> {
                    GetMaleGADStandardSeries(report.GeneticsParameters),
                    GetMaleGADActualSeries(report.HenBatchPerformances)
                }),
                ChildBatchesSeries = report.HenBatchPerformances
                    .OrderBy(hbp => hbp.HenBatch.Line.Warehouse.Name)
                    .ThenBy(hbp => hbp.HenBatch.Line.Name)
                    .Select(hbp => hbp.HenBatchId)
                    .Distinct()
                    .Select(batchId =>
                    {
                        var batchPerformances = report.HenBatchPerformances
                            .Where(hbp => hbp.HenBatchId == batchId)
                            .Where(hbp => hbp.HenBatch.InitialHenAmountFemale > 0)
                            .ToList();
                        var batch = batchPerformances.FirstOrDefault().HenBatch;
                        return new DataGroup(batch.Line.Warehouse.Name + " - " + batch.Line.Name, new List<DataSeries> {
                            GetFemaleWeightStandardSeries(report.GeneticsParameters, "Peso Padrão"),
                            GetFemaleWeightActualSeries(batchPerformances, "Peso Real"),
                            GetFemaleWeightDeviationSeries(report.GeneticsParameters, batchPerformances, "Dif (%)"),
                            GetFemaleUniformitySeries(batchPerformances, "Unif. F (%)"),
                            GetFemaleGADStandardSeries(report.GeneticsParameters, "GAD Padrão"),
                            GetFemaleGADActualSeries(batchPerformances, "GAD Real")
                        });
                    }).ToList(),
                EggProduction = new DataGroup("Produção ovos (%)", new List<DataSeries> {
                    GetEggProductionStandardSeries(report.GeneticsParameters),
                    GetEggProductionActualSeries(report.HenBatchPerformances)
                }),
                BedEggProduction = new DataGroup("Ovos Cama (%)", new List<DataSeries> {
                    GetBedEggProductionActualSeries(report.HenBatchPerformances)
                }),
                OIFC = new DataGroup("Ovo Incubáveis / Fêmea Cap", new List<DataSeries> {
                    GetOIFCStandardSeries(report.GeneticsParameters),
                    GetOIFCActualSeries(report.HenBatchPerformances)
                }),
                HatchingPercentage = new DataGroup("Eclosão (%)", new List<DataSeries> {
                    GetHatchingPercentageStandardSeries(report.GeneticsParameters),
                    GetHatchingPercentageActualSeries(report.ParentHenBatchPerformances)
                }),
                FertileEggs = new DataGroup("Fertilidade (%)", new List<DataSeries> {
                    GetFertileEggsStandardSeries(report.GeneticsParameters),
                    GetFertileEggsActualSeries(report.ParentHenBatchPerformances)
                }),
                MaleFemaleRatio = new DataGroup("Relação Macho/Fêmea (%)", new List<DataSeries> {
                    GetMaleFemaleRatioStandardSeries(report.GeneticsParameters),
                    GetMaleFemaleRatioActualSeries(report.HenBatchPerformances)
                })
            };
        }

        #region Data retrieval
        public List<HenBatchPerformance> GetHenBatchPerformances(List<Guid> childHenBatchesIds)
        {
            return henBatchPerformanceService.GetAll()
                .Include(hbr => hbr.HenBatch)
                .Include(hbr => hbr.HenBatch.Line)
                .Include(hbr => hbr.HenBatch.Line.Warehouse)
                .Include(hbr => hbr.HenBatch.Category)
                .Where(hbr => childHenBatchesIds.Contains(hbr.HenBatchId))
                .Where(hbr => hbr.HenBatch.HenStage == HenStage.Laying)
                .Where(hbr => hbr.HenAmountMale > 0 || hbr.HenAmountFemale > 0)
                .Where(hbr => hbr.WeekInitialHenAmountFemale > 0 || hbr.WeekInitialHenAmountMale > 0)
                .OrderBy(hbr => hbr.WeekNumber)
                .ToList();
        }

        public List<GeneticsParametersReference> GetGeneticsParameters(List<Guid> geneticsIds)
        {
            return geneticsParameterService.GetAll()
                .Where(gp => geneticsIds.Contains(gp.GeneticsId))
                .Where(gp => gp.HenStage == HenStage.Laying)
                .AsNoTracking()
                .ToList();
        }
        #endregion

        #region Data processing
        private DataSeries GetHenAmountMaleDataSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Saldo de Machos")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g => new DataPoint(g.Key, g.Sum(hbp => hbp.HenAmountMale))).ToList(), 0);
        }

        private DataSeries GetHenAmountFemaleDataSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Saldo de Fêmeas")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g => new DataPoint(g.Key, g.Sum(hbp => hbp.HenAmountFemale))).ToList(), 0);
        }

        private DataSeries GetFemaleViabilityStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(g => new DataPoint(g.TimePeriodValue, g.ViableFemalesPercentage)).ToList());
        }

        private DataSeries GetFemaleViabilityActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validViabilities = g.Where(hbp => hbp.HenBatchLifeTimeFemaleViability > 0);
                return new DataPoint(g.Key, validViabilities.Any() ? validViabilities.Average(hbp => hbp.HenBatchLifeTimeFemaleViability) : 0);
            }).ToList());
        }

        private DataSeries GetFemaleWeightStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.WeightFemale * 1000)).ToList());
        }

        private DataSeries GetMaleWeightStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.WeightMale * 1000)).ToList());
        }

        private DataSeries GetFemaleWeightActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validWeights = g.Where(hbp => hbp.AvgFemaleBirdWeight > 0).Select(hbp => new { AvgFemaleBirdWeight = hbp.AvgFemaleBirdWeight.Value, hbp.HenAmountFemale });
                var totalWeight = validWeights.Sum(v => v.AvgFemaleBirdWeight * v.HenAmountFemale) * 1000;
                var totalHenAmount = validWeights.Sum(v => v.HenAmountFemale);
                return new DataPoint(g.Key, totalHenAmount > 0 ? totalWeight / totalHenAmount : 0);
            }).ToList());
        }

        private DataSeries GetMaleWeightActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validWeights = g.Where(hbp => hbp.AvgMaleBirdWeight > 0).Select(hbp => new { AvgMaleBirdWeight = hbp.AvgMaleBirdWeight.Value, hbp.HenAmountMale });
                var totalWeight = validWeights.Sum(v => v.AvgMaleBirdWeight * v.HenAmountMale) * 1000;
                var totalHenAmount = validWeights.Sum(v => v.HenAmountMale);
                return new DataPoint(g.Key, totalHenAmount > 0 ? totalWeight / totalHenAmount : 0);
            }).ToList());
        }

        private DataSeries GetFemaleWeightDeviationSeries(List<GeneticsParametersReference> geneticsParametersList, List<HenBatchPerformance> henBatchPerformances, string title = "Dif (%)")
        {
            var standardSeries = GetFemaleWeightStandardSeries(geneticsParametersList);

            var actualSeries = GetFemaleWeightActualSeries(henBatchPerformances);

            var deviationSeries = new List<DataPoint>();

            foreach (var dataPoint in actualSeries.Data)
            {
                var standardValue = standardSeries.Data.FirstOrDefault(s => s.Week == dataPoint.Week)?.Value;
                if (standardValue.HasValue)
                {
                    deviationSeries.Add(new DataPoint(dataPoint.Week, (dataPoint.Value - standardValue.Value) / standardValue.Value * 100));
                }
            }

            return new DataSeries(title, deviationSeries);
        }

        private DataSeries GetMaleWeightDeviationSeries(List<GeneticsParametersReference> geneticsParametersList, List<HenBatchPerformance> henBatchPerformances, string title = "Dif (%)")
        {
            var standardSeries = GetMaleWeightStandardSeries(geneticsParametersList);

            var actualSeries = GetMaleWeightActualSeries(henBatchPerformances);

            var deviationSeries = new List<DataPoint>();

            foreach (var dataPoint in actualSeries.Data)
            {
                var standardValue = standardSeries.Data.FirstOrDefault(s => s.Week == dataPoint.Week)?.Value;
                if (standardValue.HasValue)
                {
                    deviationSeries.Add(new DataPoint(dataPoint.Week, (dataPoint.Value - standardValue.Value) / standardValue.Value * 100));
                }
            }

            return new DataSeries(title, deviationSeries);
        }

        private DataSeries GetFemaleUniformitySeries(List<HenBatchPerformance> henBatchPerformances, string title = "Unif. F (%)")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validUniformities = g.Where(hbp => hbp.AvgUniformityFemale > 0).Select(hbp => new { hbp.AvgUniformityFemale, hbp.HenAmountFemale });
                var totalUniformity = validUniformities.Sum(v => v.AvgUniformityFemale * v.HenAmountFemale);
                var totalHenAmount = validUniformities.Sum(v => v.HenAmountFemale);
                return new DataPoint(g.Key, totalHenAmount > 0 ? totalUniformity / totalHenAmount : 0);
            }).ToList());
        }

        private DataSeries GetMaleUniformitySeries(List<HenBatchPerformance> henBatchPerformances, string title = "Unif. M (%)")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validUniformities = g.Where(hbp => hbp.AvgUniformityMale > 0).Select(hbp => new { hbp.AvgUniformityMale, hbp.HenAmountMale });
                var totalUniformity = validUniformities.Sum(v => v.AvgUniformityMale * v.HenAmountMale);
                var totalHenAmount = validUniformities.Sum(v => v.HenAmountMale);
                return new DataPoint(g.Key, totalHenAmount > 0 ? totalUniformity / totalHenAmount : 0);
            }).ToList());
        }

        private DataSeries GetFemaleGADStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.FeedIntakeFemale * 1000 / 7)).ToList());
        }

        private DataSeries GetMaleGADStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.FeedIntakeMale * 1000 / 7)).ToList());
        }

        private DataSeries GetFemaleGADActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validGADs = g.Where(hbp => hbp.FeedIntakeFemale > 0).Select(hbp => new { hbp.FeedIntakeFemale, hbp.HenAmountFemale });
                var totalGAD = validGADs.Sum(v => v.FeedIntakeFemale * v.HenAmountFemale);
                var totalHenAmount = validGADs.Sum(v => v.HenAmountFemale);
                return new DataPoint(g.Key, totalHenAmount > 0 ? totalGAD / totalHenAmount / 7 : 0);
            }).ToList());
        }

        private DataSeries GetMaleGADActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validGADs = g.Where(hbp => hbp.FeedIntakeMale > 0).Select(hbp => new { hbp.FeedIntakeMale, hbp.HenAmountMale });
                var totalGAD = validGADs.Sum(v => v.FeedIntakeMale * v.HenAmountMale);
                var totalHenAmount = validGADs.Sum(v => v.HenAmountMale);
                return new DataPoint(g.Key, totalHenAmount > 0 ? totalGAD / totalHenAmount / 7 : 0);
            }).ToList());
        }

        private DataSeries GetEggProductionStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.TotalProducedEggsWeeklyPercentage)).ToList());
        }

        private DataSeries GetEggProductionActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var totalEggs = (decimal)g.Sum(hbp => hbp.TotalEggs) / 7;
                var initialHenAmountFemale = (decimal)g.Sum(hbp => hbp.WeekInitialHenAmountFemale);
                return new DataPoint(g.Key, initialHenAmountFemale > 0 ? 100 * totalEggs / initialHenAmountFemale : 0);
            }).ToList());
        }

        private DataSeries GetBedEggProductionStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, 0)).ToList());
        }

        private DataSeries GetBedEggProductionActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            var reportsQuery = henReportService.GetAll(asNoTracking: true)
               .Include(hr => hr.ClassifiedEggs)
               .ThenInclude(ce => ce.Material)
               .Where(hr => henBatchPerformances.Select(hbp => hbp.HenBatchId).Distinct().Contains(hr.HenBatchId))
               .ToList();

            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var reports = reportsQuery.Where(hr => g.Select(hbp => hbp.Id).ToList().Contains(hr.HenBatchPerformanceId.Value)).ToList();

                var bedEggs = (decimal)reports.Sum(hr => hr.ClassifiedEggs.Where(ce => ce.Material.Name == "Cama").Sum(ce => ce.Quantity));
                var totalEggs = (decimal)g.Sum(hbp => hbp.TotalEggs);
                return new DataPoint(g.Key, totalEggs > 0 ? 100 * bedEggs / totalEggs : 0);
            }).ToList());
        }

        private DataSeries GetOIFCStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.CumulativeIncubatedEggsByHen)).ToList());
        }

        private DataSeries GetOIFCActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var hatchableEggsAccum = (decimal)g.Sum(hbp => hbp.HatchableEggsAccum);
                var totalFemales = (decimal)g.Sum(hbp => hbp.FemalesOnFirstProductionDate);
                return new DataPoint(g.Key, totalFemales > 0 ? hatchableEggsAccum / totalFemales : 0);
            }).ToList());
        }

        private DataSeries GetHatchingPercentageStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.HatchingWeeklyPercentage)).ToList());
        }

        private DataSeries GetHatchingPercentageActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var hatchedEggs = (decimal)g.Sum(hbp => hbp.HatchedEggs);
                var incubatedEggs = (decimal)g.Sum(hbp => hbp.IncubatedEggs);
                return new DataPoint(g.Key, incubatedEggs > 0 ? 100 * hatchedEggs / incubatedEggs : 0);
            }).ToList());
        }

        private DataSeries GetFertileEggsStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.FertileEggsPercentage)).ToList());
        }

        private DataSeries GetFertileEggsActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            decimal latestPercentage = 0;
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var validPercentages = g.Where(hbp => hbp.FertileEggsPercentage > 0);
                var averagePercentage = validPercentages.Any() ? validPercentages.Average(hbp => hbp.FertileEggsPercentage) : 0;
                if (averagePercentage == 0)
                {
                    averagePercentage = latestPercentage;
                }
                latestPercentage = averagePercentage;
                return new DataPoint(g.Key, averagePercentage);
            }).ToList());
        }

        private DataSeries GetMaleFemaleRatioStandardSeries(List<GeneticsParametersReference> geneticsParameters, string title = "Padrão")
        {
            return new DataSeries(title, geneticsParameters.Select(gpr => new DataPoint(gpr.TimePeriodValue, gpr.MaleFemaleRatio)).ToList());
        }

        private DataSeries GetMaleFemaleRatioActualSeries(List<HenBatchPerformance> henBatchPerformances, string title = "Real")
        {
            return new DataSeries(title, henBatchPerformances.GroupBy(hbp => hbp.WeekNumber).Select(g =>
            {
                var totalMales = (decimal)g.Sum(hbp => hbp.HenAmountMale);
                var totalFemales = (decimal)g.Sum(hbp => hbp.HenAmountFemale);

                var maleFemaleRatio = totalFemales > 0 ? totalMales / totalFemales : 0;
                return new DataPoint(g.Key, maleFemaleRatio * 100);
            }).ToList());
        }

        #endregion

        #region Support classes
        private class ReportSheet
        {
            ExcelWorksheet worksheet;
            Dictionary<int, int> weekRowMap;

            int currentColumn = 1;

            public ReportSheet(ExcelWorksheet worksheet)
            {
                this.worksheet = worksheet;
            }

            public void SetWeeks(List<int> weeks, string? headerHexColor = null)
            {
                // Create a map of week to row
                weekRowMap = weeks.Select((week, index) => new { week, index }).ToDictionary(x => x.week, x => x.index + 3);

                // Render the weeks header and data
                RenderDataSeries(
                    new DataSeries("Idade Semanas", weeks.Select(week => new DataPoint(week, week)).ToList(), 0), 
                    headerHexColor: headerHexColor
                );
            }

            public void RenderDataSeries(DataSeries dataSeries, bool isPartOfGroup = false, string? headerHexColor = null)
            {
                // Render header
                if (!isPartOfGroup)
                {
                    var range = worksheet.Cells[1, currentColumn, 2, currentColumn];
                        
                    // Merge header and subheader cells
                    range.Merge = true;
                    range.Style.Border.BorderAround(ExcelBorderStyle.Medium);
                    
                    worksheet.Cells[1, currentColumn].Value = dataSeries.Name;
                    worksheet.Cells[1, currentColumn, weekRowMap.Count + 2, currentColumn].Style.Border.BorderAround(ExcelBorderStyle.Medium);
                    
                    if(headerHexColor != null) 
                        SetColor(range, headerHexColor);
                }
                else
                {
                    // Only set subheader value
                    worksheet.Cells[2, currentColumn].Value = dataSeries.Name;
                    worksheet.Cells[2, currentColumn].Style.Border.BorderAround(ExcelBorderStyle.Medium);
                    
                    if(headerHexColor != null) 
                        SetColor(worksheet.Cells[2, currentColumn], headerHexColor);
                }

                // Render data
                foreach (var dataPoint in dataSeries.Data)
                {
                    if (weekRowMap.ContainsKey(dataPoint.Week))
                    {
                        worksheet.Cells[weekRowMap[dataPoint.Week], currentColumn].Value = dataPoint.Value;
                        if (dataSeries.DecimalPlaces > 0)
                        {
                            worksheet.Cells[weekRowMap[dataPoint.Week], currentColumn].Style.Numberformat.Format = $"0.{new string('0', dataSeries.DecimalPlaces)}";
                        }
                    }
                }

                currentColumn++;
            }

            private static void SetColor(ExcelRange range, string color)
            {
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(color)); 
            }

            public void RenderDataGroup(DataGroup dataGroup, string? headerHexColor = null)
            {
                var range = worksheet.Cells[1, currentColumn, 1, currentColumn + dataGroup.Series.Count - 1];
                
                // Render superheader
                range.Merge = true;
                range.Style.Border.BorderAround(ExcelBorderStyle.Medium);
                worksheet.Cells[1, currentColumn].Value = dataGroup.Name;
                worksheet.Cells[1, currentColumn, weekRowMap.Count + 2, currentColumn + dataGroup.Series.Count - 1].Style.Border.BorderAround(ExcelBorderStyle.Medium);

                if(headerHexColor != null) 
                    SetColor(range, headerHexColor);

                // Render subheader and data
                foreach (var series in dataGroup.Series)
                {
                    RenderDataSeries(series, true, headerHexColor);
                }
            }

            public void Style()
            {
                worksheet.Cells[1, 1, 2, currentColumn].Style.WrapText = true;
                worksheet.Cells[1, 1, 2, currentColumn].Style.Font.Bold = true;
                worksheet.Cells[1, 1, weekRowMap.Count + 2, currentColumn].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[1, 1, weekRowMap.Count + 2, currentColumn].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            }
        }

        public class ManagerialLayingReportDataDTO
        {
            public List<int> Weeks;
            public DataSeries HenAmountMale;
            public DataSeries HenAmountFemale;
            public DataGroup FemaleViability;
            public DataGroup FemaleWeight;
            public DataSeries FemaleUniformity;
            public DataGroup FemaleGAD;
            public DataGroup MaleWeight;
            public List<DataGroup> ChildBatchesSeries;
            public DataSeries MaleUniformity;
            public DataGroup MaleGAD;
            public DataGroup EggProduction;
            public DataGroup BedEggProduction;
            public DataGroup OIFC;
            public DataGroup HatchingPercentage;
            public DataGroup FertileEggs;
            public DataGroup MaleFemaleRatio;
        }

        public class DataPoint
        {
            public int Week;
            public decimal Value;

            public DataPoint(int week, decimal value)
            {
                Week = week;
                Value = value;
            }
        }

        public class DataSeries
        {
            public string Name;
            public List<DataPoint> Data;

            public int DecimalPlaces;

            public DataSeries(string name, List<DataPoint> data, int decimalPlaces = 2)
            {
                Name = name;
                Data = data;
                DecimalPlaces = decimalPlaces;
            }
        }

        public class DataGroup
        {
            public string Name;
            public List<DataSeries> Series;

            public DataGroup(string name, List<DataSeries> series)
            {
                Name = name;
                Series = series;
            }
        }
        #endregion
    }


}
