let currentSort = "days"; // Default sort value
let currentRequest = null;
let isFeedView = true; // Toggle between feed and other materials view

$(document).ready(function () {
  loadCards();

  // Initialize user-based auto-filtering
  initializeUserContextFilters();

  // Initialize material type filter with "Formula" selected
  $("#materialType").on("change", function () {
    const materialTypeId = $(this).val();
    loadMaterialsByType(materialTypeId);
    clearAllCards();
    loadCards();
  });

  // Toggle between feed view and other materials
  $("#viewToggle").on("change", function () {
    isFeedView = $(this).prop("checked");
    clearAllCards();
    loadCards();
  });

  $(".sort-button").on("click", function () {
    currentSort = $(this).data("sort"); // Update sort type
    $(".sort-button").removeClass("active");
    $(this).addClass("active");
    loadCards(); // Reload cards with new sort
  });
});

function loadMaterialsByType(materialTypeId) {
  if (!materialTypeId) {
    $("#material").empty();
    $("#material").append($("<option>", { value: "", text: "Todos" }));
    return;
  }

  $.ajax({
    url: `/ManagerialBreeding/GetMaterialsByTypeAjax?materialTypeId=${materialTypeId}`,
    type: "GET",
  }).done(function (materials) {
    let $materialFilter = $("#material");
    $materialFilter.empty();
    $materialFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(materials, function (index, option) {
      $materialFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

function loadCards() {
  // Get filter values and sort
  const body = {
    supervisor: $("#supervisorFilter").val(),
    extensionist: $("#extensionistFilter").val(),
    productor: $("#productorFilter").val(),
    regional: $("#regionalFilter").val(),
    unit: $("#unitFilter").val(),
    materialType: $("#materialType").val(),
    material: $("#material").val(),
    sortBy: currentSort,
  };

  if (isFeedView) {
    fetchFeedCards(body);
  } else {
    fetchCards(body);
  }
}

function fetchFeedCards(body) {
  if (currentRequest) {
    currentRequest.abort();
  }

  const $container = $("#stockCardsContainer");
  $container.empty(); // Clear container before adding spinner

  $container.append(
    `<div class="loading-spinner">
      <div class="spinner-border" role="status">
      </div>
    </div>`
  );

  currentRequest = $.ajax({
    url: "/ManagerialBreeding/GetFeedStockIndicators",
    method: "GET",
    data: body,
    success: function (data) {
      currentRequest = null;
      renderFeedCards(data); // Render feed cards
    },
    error: function (error) {
      currentRequest = null;
      $container.empty(); // Clear spinner

      if (error.status === 500) {
        $container.append(`
          <div class="error-message">
            <strong>Erro</strong>
          </div>
        `);
      }
    },
  });
}

function fetchCards(body) {
  if (currentRequest) {
    currentRequest.abort();
  }

  const $container = $("#stockCardsContainer");
  $container.empty(); // Clear container before adding spinner

  $container.append(
    `<div class="loading-spinner">
      <div class="spinner-border" role="status">
      </div>
    </div>`
  );

  currentRequest = $.ajax({
    url: "/ManagerialBreeding/GetInventoryCards",
    method: "GET",
    data: body,
    success: function (data) {
      currentRequest = null;
      renderCards(data); // Render regular cards
    },
    error: function (error) {
      currentRequest = null;
      $container.empty(); // Clear spinner

      if (error.status === 500) {
        $container.append(`
          <div class="error-message">
            <strong>Erro</strong>
          </div>
        `);
      }
    },
  });
}

function renderFeedCards(cardData) {
  const $container = $("#stockCardsContainer");
  $container.empty();

  if (!cardData?.length) {
    $container.append(`
      <div id="emptyDataMessage">
        <strong>${localizedStrings.emptyDataMessage}</strong>
      </div>`);
    return;
  }

  cardData.forEach((item) => {
    // Feed card with: Name, Days, Stock, Unit, Available Until Date, Producer Name
    const $card = $("<div></div>")
      .addClass("card")
      .addClass(`status-${item.status || "success"}`) // Add fallback status
      .css({
        backgroundColor: calculateColor(item.daysRemaining, item.totalStock),
      }).html(`
          <p class="material-name">${item.materialName}</p>
          <p class="producer-name">${item.producerName || "Unknown"}</p>
          <p class="cardDays">${formatDaysRemaining(item.daysRemaining)}</p>
          <p>${localizedStrings.stock}: ${formatNumber(item.totalStock)} ${
      item.unity || ""
    }</p>
          <p class="available-until">${"Disponível até"}: ${
      item.availableUntilDate || "N/A"
    }</p>
      `);

    $container.append($card);
  });
}

function renderCards(cardData) {
  const $container = $("#stockCardsContainer");
  $container.empty();

  if (!cardData?.length) {
    $container.append(`
      <div id="emptyDataMessage">
        <strong>${localizedStrings.emptyDataMessage}</strong>
      </div>`);
    return;
  }

  cardData.forEach((item) => {
    // Regular card with: Name, Producer, Days, Stock, Unit
    const $card = $("<div></div>")
      .addClass("card")
      .css({
        backgroundColor: calculateColor(item.daysRemaining, item.totalStock),
      }).html(`
          <p class="material-name">${item.materialName}</p>
          <p class="producer-name">${item.producerName || "Unknown"}</p>
          <p class="cardDays">${formatDaysRemaining(
            item.daysRemaining,
            item.consumptionDailyAVG
          )}</p>
          <p>${localizedStrings.stock}: ${formatNumber(item.totalStock)} ${
      item.unity
    }</p>
      `);

    $container.append($card);
  });
}

function formatDaysRemaining(daysRemaining, consumptionDailyAVG) {
  // if(consumptionDailyAVG === 0)
  //   return `${localizedStrings.noConsumption}`;

  return `${Math.floor(daysRemaining) || "0"} ${localizedStrings.days}`;
}

function calculateColor(daysRemaining, stock) {
  if (stock === 0) return "#dcdcdc"; // Gray for no stock
  if (daysRemaining < 2) return "#dc8b89"; // Red for critical days
  if (daysRemaining < 7) return "#ecf38d"; // Yellow for warning
  return "#9add8e"; // Green for sufficient days
}

function formatNumber(value) {
  const language = navigator.language || "pt-BR";

  return value.toLocaleString(language, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

function clearAllCards() {
  const $container = $("#stockCardsContainer");
  $container.empty();
}

// Filtros ------------------
$("#regionalFilter").on("change", function () {
  clearAllCards();
  var regionalId = $(this).val();
  $.ajax({
    url: `/ManagerialBreeding/GetUnitsByRegionalAjax?regionalId=${regionalId}`,
    type: "GET",
  }).done(function (units) {
    let $unitFilter = $("#unitFilter");
    $unitFilter.empty();

    reloadExtensionists();
    reloadSupervisors();

    $unitFilter.append($("<option>", { value: "", text: "Todas" }));
    $.each(units, function (index, option) {
      $unitFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });

    $unitFilter.select2("open");
    loadCards();
  });
});

$("#unitFilter").on("change", function () {
  clearAllCards();
  var unitId = $(this).val();
  //Reload Produtor/Fazenda
  reloadProductors(unitId);
  reloadExtensionists(unitId);
  reloadSupervisors(unitId);
  loadCards();
});

$("#supervisorFilter").on("change", function () {
  clearAllCards();
  reloadExtensionists($("#unitFilter").val());
  if ($(this).val() != "") {
    applyDateFilters = true;
  }
  loadCards();
});

$("#extensionistFilter").on("change", function () {
  clearAllCards();
  if ($(this).val() != "") {
    applyDateFilters = true;
  }
  loadCards();
});

$("#productorFilter").on("change", function () {
  clearAllCards();
  var farmId = $(this).val();
  var henBatchStatus = $("#henbatch-status").val();
  const henBatchStatusDictionary = {
    active: true,
    closed: false,
  };
  const henBatchStatusParam = henBatchStatusDictionary[henBatchStatus] ?? null;

  $.ajax({
    url: `/ManagerialBreeding/GetHenBatchListByFarmIdAjax?farmId=${
      farmId ?? null
    }&henBatchStatus=${henBatchStatusParam}`,
    type: "GET",
  }).done(function (henBatchList) {
    let $parentHenBatchMainFilter = $("#parentHenBatchMainFilter");
    $parentHenBatchMainFilter.empty();
    $parentHenBatchMainFilter.append(
      $("<option>", { value: "", text: "Todos" })
    );
    $.each(henBatchList, function (index, option) {
      $parentHenBatchMainFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
  loadCards();
});

$("#material").on("change", function () {
  clearAllCards();
  loadCards();
});

function reloadSupervisors(unitId) {
  let $supervisorFilter = $("#supervisorFilter");
  if ([null, undefined].includes(unitId)) {
    $supervisorFilter.empty();
    $supervisorFilter.append($("<option>", { value: "", text: "Todos" }));
    return;
  }
  $.ajax({
    url: `/ManagerialBreeding/GetSupervisorByUnitAjax?unitId=${unitId}`,
    type: "GET",
  }).done(function (supervisors) {
    $supervisorFilter.empty();
    $supervisorFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(supervisors, function (index, option) {
      $supervisorFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

function reloadExtensionists(unitId) {
  let $extensionistFilter = $("#extensionistFilter");
  const supervisorId = $("#supervisorFilter").val();

  if ([null, undefined].includes(unitId)) {
    $extensionistFilter.empty();
    $extensionistFilter.append($("<option>", { value: "", text: "Todos" }));

    return;
  }
  $.ajax({
    url: `/ManagerialBreeding/GetExtensionistsByUnitAjax?unitId=${unitId}&supervisorId=${supervisorId}`,
    type: "GET",
  }).done(function (extensionists) {
    $extensionistFilter.empty();
    $extensionistFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(extensionists, function (index, option) {
      $extensionistFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

function reloadProductors(unitId) {
  let $productorFilter = $("#productorFilter");
  if ([null, undefined].includes(unitId)) {
    $productorFilter.empty();
    $productorFilter.append($("<option>", { value: "", text: "Todos" }));
    return;
  }
  $.ajax({
    url: `/ManagerialBreeding/GetFarmsByUnitIdAjax?unitId=${unitId}`,
    type: "GET",
  }).done(function (extensionists) {
    $productorFilter.empty();
    $productorFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(extensionists, function (index, option) {
      $productorFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

// Função para atualizar a ordenação
function updateSort(sortBy) {
  // Remove a classe active de todos os botões
  $(".sort-button").removeClass("active");

  // Adiciona a classe active ao botão clicado
  $(`.sort-button[data-sort="${sortBy}"]`).addClass("active");

  // Atualiza a variável de ordenação
  currentSort = sortBy;

  // Recarrega os dados com a nova ordenação
  loadCards();
}

// User Context Auto-Filtering Functions
async function initializeUserContextFilters() {
  try {
    const userFilters = await getUserContextFilters();

    if (userFilters) {
      await applyUserContextFilters(userFilters);
    }
  } catch (error) {
    console.warn("Could not load user context filters:", error);
    // Continue with normal operation if user context fails
  }
}

async function getUserContextFilters() {
  try {
    const response = await fetch(
      `${location.origin}/ManagerialBreeding/GetUserContextFiltersAjax`
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching user context filters:", error);
    return null;
  }
}

async function applyUserContextFilters(userFilters) {
  // Apply regional filter
  if (userFilters.regional && userFilters.regional.length > 0) {
    await applyFilterWithAutoSelection(
      "#regionalFilter",
      userFilters.regional,
      "regional"
    );
  }

  // Apply unit filter
  if (userFilters.unidade && userFilters.unidade.length > 0) {
    await applyFilterWithAutoSelection(
      "#unitFilter",
      userFilters.unidade,
      "unidade"
    );
  }

  // Apply produtor filter
  if (userFilters.produtor && userFilters.produtor.length > 0) {
    await applyFilterWithAutoSelection(
      "#productorFilter",
      userFilters.produtor,
      "produtor"
    );
  }

  // Apply supervisor filter
  if (userFilters.supervisor && userFilters.supervisor.length > 0) {
    await applyFilterWithAutoSelection(
      "#supervisorFilter",
      userFilters.supervisor,
      "supervisor"
    );
  }

  // Apply extensionist filter
  if (userFilters.extensionista && userFilters.extensionista.length > 0) {
    await applyFilterWithAutoSelection(
      "#extensionistFilter",
      userFilters.extensionista,
      "extensionista"
    );
  }
}

async function applyFilterWithAutoSelection(selector, options, filterType) {
  const $filter = $(selector);

  if (!$filter.length) {
    console.warn(`Filter ${selector} not found`);
    return;
  }

  // Clear existing options except the first one (usually "Todas" or default)
  const defaultOption = $filter.find("option:first");
  $filter.empty();
  if (defaultOption.length) {
    $filter.append(defaultOption);
  }

  // Add user-specific options
  options.forEach((option) => {
    $filter.append(new Option(option.text, option.value));
  });

  // Auto-select and disable if only one option (plus default)
  if (options.length === 1) {
    $filter.val(options[0].value);
    $filter.prop("disabled", true);

    // Trigger change event to update dependent filters
    $filter.trigger("change");

    console.log(`Auto-selected and disabled ${filterType}: ${options[0].text}`);
  } else if (options.length > 1) {
    // If multiple options, keep enabled but don't auto-select
    console.log(`Multiple ${filterType} options available: ${options.length}`);
  }
}
