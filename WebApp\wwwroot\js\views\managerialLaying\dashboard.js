// Initialize style, fetch data and draw charts
$(document).ready(function () {
  // Initialize
  InitializeColors();

  window.geneticChart = null;

  // Clear the genetic chart to ensure it's not displayed on initial load
  if (geneticChart) {
    geneticChart.destroy();
    geneticChart = null;
  }

  // Clear the genetic canvas element
  const geneticCanvas = $("#Genetic");
  if (geneticCanvas.length) {
    const parent = geneticCanvas.parent();
    const canvasHtml = `<canvas class="ignite-chart performance-standards-chart"
                          data-performance-label="Rendimento (%)"
                          data-weeks-from-birth-label="Semanas de idade"
                          data-body-weight-label="Peso corporal (g)"
                          data-mortality-label="Mortalidade (%)"
                          id="Genetic">
                        </canvas>`;
    geneticCanvas.remove();
    parent.html(canvasHtml);
    parent.hide(); // Hide the genetic chart container initially
  }

  clearAllCharts();

  loadHenBatchesByStatus("active");
  loadBedEggsChart();
  handleFilterButtons();
  handleWeekAndDateFilters();
  handleGenderChange();

  // Initialize user-based auto-filtering after initial chart loading
  setTimeout(() => {
    initializeUserContextFilters();
  }, 1000);
});

var geneticChart;

function handleWeekAndDateFilters() {
  const startWeek = $("#startWeek");
  const endWeek = $("#endWeek");
  const fromDate = $("#filter-from-date");
  const toDate = $("#filter-to-date");

  startWeek.on("blur", function () {
    if (
      startWeek.val() &&
      endWeek.val() &&
      parseInt(startWeek.val()) > parseInt(endWeek.val())
    ) {
      swal(
        "",
        "A semana inicial não pode ser maior que a semana final",
        "warning"
      );
      startWeek.val("");
    }
  });

  endWeek.on("blur", function () {
    if (
      startWeek.val() &&
      endWeek.val() &&
      parseInt(endWeek.val()) < parseInt(startWeek.val())
    ) {
      swal(
        "",
        "A semana final não pode ser menor que a semana inicial",
        "warning"
      );
      endWeek.val("");
    }
  });

  fromDate.on("change", function () {
    if (fromDate.val() && toDate.val()) {
      const fromDateObj = new Date(
        fromDate.val().split("/").reverse().join("-")
      );
      const toDateObj = new Date(toDate.val().split("/").reverse().join("-"));
      if (fromDateObj > toDateObj) {
        swal(
          "",
          "A data inicial não pode ser maior que a data final",
          "warning"
        );
        fromDate.val("");
      }
    }
  });

  toDate.on("change", function () {
    if (fromDate.val() && toDate.val()) {
      const fromDateObj = new Date(
        fromDate.val().split("/").reverse().join("-")
      );
      const toDateObj = new Date(toDate.val().split("/").reverse().join("-"));
      if (toDateObj < fromDateObj) {
        swal(
          "",
          "A data final não pode ser menor que a data inicial",
          "warning"
        );
        toDate.val("");
      }
    }
  });
}

// ---------------------------------------- Get data for charts ---------------------------------------- //
function GetSearaCards() {
  clearAllCards();
  disableChartButtons();
  showLoadingSpinner();
  isSearaCardLoading = true;
  $.ajax({
    url: `/ManagerialLaying/GetSearaCards`,
    type: "POST",
    data: getAllFilters(),
  })
    .done(function (result) {
      if (result != null) {
        result.viewList.forEach(function (elem) {
          const $wrap = $('<div style="width:1600px"></div>');
          $wrap.html(elem.item1);
          $("#sampleCard").append($wrap);
        });
      }
    })
    .then(function () {
      isSearaCardLoading = false;
      if (isSearaCardLoading == false && isChartLoading == false) {
        enableChartButtons();
        hideLoadingSpinner();
      }
    });
}

function getSelectedGender() {
  const genderValue = $("input[name='gender']:checked").val();
  return genderValue;
}

function handleGenderChange() {
  $("input[name='gender']").on("change", function () {
    const activeChartButton = $(".btn-report.active");
    if (activeChartButton.length) {
      activeChartButton.trigger("click");
    } else {
      loadBedEggsChart();
    }
  });
}

function loadBedEggsChart() {
  setTimeout(() => {
    $(".btn-report[data-report-type='bed_eggs']").trigger("click");
  }, 300);
}

function loadHenBatchesByStatus(status) {
  var farmId = $("#productorFilter").val();
  const henBatchStatusDictionary = {
    active: true,
    closed: false,
  };
  const henBatchStatusParam = henBatchStatusDictionary[status] ?? null;

  $.ajax({
    url: `/ManagerialLaying/GetHenBatchListByFarmIdAjax?farmId=${
      farmId ?? null
    }&henBatchStatus=${henBatchStatusParam}`,
    type: "GET",
  }).done(function (henBatchList) {
    let $parentHenBatchMainFilter = $("#parentHenBatchMainFilter");
    $parentHenBatchMainFilter.empty();
    $parentHenBatchMainFilter.append(
      $("<option>", { value: "", text: "Todos" })
    );
    $.each(henBatchList, function (index, option) {
      $parentHenBatchMainFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

function GetDataForPerformanceChart(data, asyncAjax) {
  $.ajax({
    type: "POST",
    url: `/ManagerialLaying/GetDataForLayingDashboardPerformanceChart`,
    data: data,
    async: asyncAjax,
    success: function (resultObject) {
      timeFilteredPerformanceData = resultObject.data;
      originalChartConfiguration = jQuery.extend(
        true,
        {},
        resultObject.chartDTO
      );
      var chartConfiguration = resultObject.chartDTO;
      performanceChart = $("#Performance").DrawGenericHighChart(
        chartConfiguration,
        dashboardPerformanceChartToolTipFormatter
      );
      if (
        resultObject.chartDTO.series[0].data == null &&
        resultObject.chartDTO.series.length == 1
      ) {
        swal(
          "",
          DashboardsResources[
            "WebApp.Js.LayingDashboard.NoDataPerformanceChart"
          ],
          "warning"
        );
      }
    },
    error: function (result) {
      swal("", result.responseJSON.message.value, "warning");
    },
  });
}

function GetSaleableChicksChart(asyncAjax, filters) {
  isChartLoading = true;
  $.ajax({
    type: "POST",
    url: `/ManagerialLaying/GetSaleableChicksChart`,
    data: filters,
    async: asyncAjax,
    success: function (resultObject) {
      if (resultObject.message) {
        swal("", resultObject.message, "warning");
        return;
      }
      if (!resultObject.chartDTO || !resultObject.chartDTO.series) {
        swal(
          "",
          "Não há dados para exibir no gráfico de vendáveis.",
          "warning"
        );
        return;
      }
      $("#Saleable").DrawGenericHighChart(
        resultObject.chartDTO,
        dashboardSaleableChicksChartToolTipFormatter,
        dashboardSaleableChicksChartPlotOptions
      );
    },
    error: function (result) {
      swal("", result.responseJSON.message.value, "warning");
    },
  }).then(function () {
    isChartLoading = false;
    if (isSearaCardLoading == false && isChartLoading == false) {
      enableChartButtons();
      hideLoadingSpinner();
    }
  });
}

function GetBedEggsChart(asyncAjax, filters) {
  isChartLoading = true;
  $.ajax({
    type: "POST",
    url: `/ManagerialLaying/GetBedEggsChart`,
    data: filters,
    async: asyncAjax,
    success: function (resultObject) {
      if (resultObject.message) {
        swal("", resultObject.message, "warning");
        return;
      }
      if (!resultObject.chartDTO || !resultObject.chartDTO.series) {
        swal(
          "",
          "Não há dados para exibir no gráfico de ovos cama.",
          "warning"
        );
        return;
      }
      $("#BedEggs").DrawGenericHighChart(
        resultObject.chartDTO,
        dashboardSaleableChicksChartToolTipFormatter,
        dashboardSaleableChicksChartPlotOptions
      );
    },
    error: function (result) {
      swal("", result.responseJSON.message.value, "warning");
    },
  }).then(function () {
    isChartLoading = false;
    if (isSearaCardLoading == false && isChartLoading == false) {
      enableChartButtons();
      hideLoadingSpinner();
    }
  });
}

function LoadSearaLayingFeedIntakeGADChart(asyncAjax, filters) {
  isChartLoading = true;
  $.ajax({
    type: "POST",
    url: `/ManagerialLaying/GetSearaLayingFeedIntakeGADChart`,
    data: filters,
    async: asyncAjax,
    success: function (resultObject) {
      if (
        (resultObject.chartDTO?.series.length == 1 &&
          resultObject.chartDTO?.series[0].data == null) ||
        resultObject.chartDTO?.series.length == 0 ||
        resultObject.message
      ) {
        swal(
          "",
          DashboardsResources[
            "WebApp.Js.LayingDashboard.NoDataFeedIntakeChart"
          ],
          "warning"
        );
        return;
      }
      $("#FeedIntakeGAD").DrawGenericHighChart(
        resultObject.chartDTO,
        dashboardBreedingGADChartToolTipFormatter,
        dashboardBreedingGADChartPlotOptions
      );
    },
    error: function (result) {
      swal("", result.responseJSON.message.value, "warning");
    },
  }).then(function () {
    isChartLoading = false;
    if (isSearaCardLoading == false && isChartLoading == false) {
      enableChartButtons();
      hideLoadingSpinner();
    }
  });
}

function LoadSearaMaleFemaleDataChart(isAsync, filters) {
  isChartLoading = true;
  $.ajax({
    type: "POST",
    url: `/ManagerialLaying/GetSearaMaleFemaleChart`,
    data: filters,
    async: isAsync,
    success: function (resultObject) {
      if (
        !resultObject.chartDTO ||
        !resultObject.chartDTO.series ||
        resultObject.chartDTO.series.length === 0 ||
        (resultObject.chartDTO.series.length === 1 &&
          resultObject.chartDTO.series[0].data == null)
      ) {
        swal("", "Não há dados para exibir no gráfico M/F.", "warning");
        return;
      }
      $("#MaleFemale").DrawGenericHighChart(
        resultObject.chartDTO,
        maleFemaleDistributionChartToolTipFormatter,
        maleFemaleDistributionChartPlotOptions
      );
    },
    error: function (error) {
      swal(
        "",
        error.responseJSON?.message?.value ?? "Erro ao carregar gráfico M/F",
        "warning"
      );
    },
  }).then(function () {
    isChartLoading = false;
    if (isSearaCardLoading == false && isChartLoading == false) {
      enableChartButtons();
      hideLoadingSpinner();
    }
  });
}

function LoadSearaGeneticDataChart(asyncAjax, filters) {
  isChartLoading = true;

  // Make sure to destroy any existing chart
  if (geneticChart) {
    geneticChart.destroy();
    geneticChart = null;
  }

  // Show the genetic chart container when loading this chart
  $("#Genetic").parent().show();

  // Clear the genetic canvas element
  const geneticCanvas = $("#Genetic");
  if (geneticCanvas.length) {
    const parent = geneticCanvas.parent();
    const canvasHtml = `<canvas class="ignite-chart performance-standards-chart"
                          data-performance-label="Rendimento (%)"
                          data-weeks-from-birth-label="Semanas de idade"
                          data-body-weight-label="Peso corporal (g)"
                          data-mortality-label="Mortalidade (%)"
                          id="Genetic">
                        </canvas>`;
    geneticCanvas.remove();
    parent.html(canvasHtml);
  }

  $.ajax({
    type: "POST",
    url: `/ManagerialLaying/GetGeneticChart`,
    data: filters,
    async: asyncAjax,
    success: function (resultObject) {
      if (resultObject.message) {
        swal("", resultObject.message, "warning");
        $("#Genetic").parent().hide(); // Hide on error
        return;
      }

      if (geneticCanvas.length && charts) {
        geneticChart = charts.newPerformanceStandardsChart(
          $("#Genetic"),
          resultObject.chartDTO
        );
      } else {
        console.error("charts is not defined or Genetic canvas not found");
        $("#Genetic").parent().hide();
      }
    },
    error: function (result) {
      swal(
        "",
        result.responseJSON?.message || "Erro ao carregar o gráfico genético",
        "warning"
      );
      $("#Genetic").parent().hide();
    },
  }).then(function () {
    isChartLoading = false;
    if (isSearaCardLoading == false && isChartLoading == false) {
      enableChartButtons();
      hideLoadingSpinner();
    }
  });
}

function GetOIFCChart(isAsync, filters) {
  isChartLoading = true;
  $.ajax({
    url: `/ManagerialLaying/GetOIFCChart`,
    type: "POST",
    data: filters,
    async: isAsync,
    success: function (resultObject) {
      if (!resultObject.chartDTO || !resultObject.chartDTO.series) {
        swal("", "Não há dados de OIFC para exibir.", "warning");
        return;
      }
      $("#OIFC").DrawGenericHighChart(
        resultObject.chartDTO,
        dashboardManagerialOIFCChartToolTipFormatter,
        dashboardViabilityChartPlotOptions
      );
    },
    error: function (error) {
      swal(
        "",
        error.responseJSON?.message.value || "Erro ao carregar OIFC",
        "warning"
      );
    },
  }).then(function () {
    isChartLoading = false;
    if (!isSearaCardLoading && !isChartLoading) {
      enableChartButtons();
      hideLoadingSpinner();
    }
  });
}

// ----------------------- Variables and methods for performance chart filters ----------------------- //

// Variables to handle chart filtering
var performanceChart = {};
var timeFilteredPerformanceData = [];
var originalChartConfiguration = {};
var henAmountList = [];
var deadList = [];
var feedIntakeList = [];
var eggsList = [];
var performanceFromDate = $("#main-date-filter-from-date").val();
var performanceToDate = $("#main-date-filter-to-date").val();
var initialIteration = true;

const getDateFilterValues = () => {
  const data = {
    MinDate: $("#main-date-filter-from-date").val(),
    MaxDate: $("#main-date-filter-to-date").val(),
  };

  return data;
};

const getUserFilterValue = () => {
  var userFilterId = null;

  // PRODUCTOR é FAZENDA
  // var filterProd = $(`#productorFilter`).val();
  var filterSup = $(`#supervisorFilter`).val();
  var filterExt = $(`#extensionistFilter`).val();

  // if (filterProd != "") {
  //   userFilterId = filterProd;
  // } else
  if (filterSup != "") {
    userFilterId = filterSup;
  } else if (filterExt != "") {
    userFilterId = filterExt;
  }

  return userFilterId;
};

const getFiltersForBirdWeightChart = () => {
  const data = {
    warehouseId: $("#warehouseFilter").val(),
    lineId: $("#lineFilter").val(),
    henBatchId: $("#parentHenBatchFilter").val(),
    userId: getUserFilterValue(),
  };

  return data;
};

function applyFiltersToFeedIntakeGADChart(isAsync, containerFilters) {
  LoadSearaLayingFeedIntakeGADChart(isAsync, containerFilters);
}

function applyFiltersToSaleableChicksChart(isAsync, containerFilters) {
  containerFilters.hasAuthorizationSearaCharts =
    hasAuthorizationSearaCharts.toLowerCase();

  GetSaleableChicksChart(isAsync, containerFilters);
}

function applyFiltersToBedEggsChart(isAsync, containerFilters) {
  containerFilters.hasAuthorizationSearaCharts =
    hasAuthorizationSearaCharts.toLowerCase();

  GetBedEggsChart(isAsync, containerFilters);
}

function applyFiltersToMaleFemaleChart(isAsync, containerFilters) {
  containerFilters.hasAuthorizationSearaCharts =
    hasAuthorizationSearaCharts.toLowerCase();
  LoadSearaMaleFemaleDataChart(isAsync, containerFilters);
}

function applyFiltersToSearaPerformanceCharts(isAsync, containerFilters) {
  LoadSearaLayingViabilityChart(isAsync, containerFilters);
}

function applyFiltersToGeneticChart(isAsync, containerFilters) {
  containerFilters.hasAuthorizationSearaCharts =
    hasAuthorizationSearaCharts.toLowerCase();
  LoadSearaGeneticDataChart(isAsync, containerFilters);
}

function applyFiltersToOIFCChart(isAsync, containerFilters) {
  containerFilters.hasAuthorizationSearaCharts =
    hasAuthorizationSearaCharts.toLowerCase();
  GetOIFCChart(isAsync, containerFilters);
}

// Global variables
let farm = document.querySelector("#farm");
let farmDiv = document.querySelector("#FarmDiv");
let warehouse = document.querySelector("#warehouse");
let WHDiv = document.querySelector("#WHDiv");
let line = document.querySelector("#line");
let LineDiv = document.querySelector("#LineDiv");
let henBatchValue = document.querySelector("#HenBatchId");
var controller = "ManagerialLaying";

if (!LineDiv) {
  console.warn("LineDiv element not found in the DOM");
  LineDiv = document.createElement("div");
  LineDiv.id = "LineDiv";
  document.body.appendChild(LineDiv);
}

if (!WHDiv) {
  console.warn("WHDiv element not found in the DOM");
  WHDiv = document.createElement("div");
  WHDiv.id = "WHDiv";
  document.body.appendChild(WHDiv);
}

const createOption = (e, attr) => {
  let option = document.createElement("option");
  option.text = e.text;
  option.value = e.value;
  option.setAttribute(attr, "");
  return option;
};

// Get and refresh lists
const getWarehousesByHenBatch = (parentHenBatchId) => {
  return $.ajax({
    url: `/ManagerialLaying/GetWarehousesByHenBatch?selectedHenBatch=${parentHenBatchId}`,
    type: "GET",
    dataType: "json",
  });
};

function refreshWarehouses(warehouses) {
  const $warehouse = $("#warehouse");
  $warehouse.find("option").not(":first").remove();

  warehouses.forEach((e) => {
    $warehouse.append(new Option(e.text, e.value));
  });

  $warehouse.trigger("change.select2");
}

const getLines = (warehouseId, henBatchId) => {
  return $.ajax({
    url: `/ManagerialLaying/GetLinesByWarehouse?warehouseId=${warehouseId}&henBatchId=${henBatchId}`,
    type: "GET",
    dataType: "json",
  });
};

function refreshLines(lines) {
  const $line = $("#line");
  $line.find("option").not(":first").remove();

  lines.forEach((e) => {
    $line.append(new Option(e.text, e.value));
  });

  $line.trigger("change.select2");
}

// Filter on change actions
async function updateFiltersByParentHenBatch() {
  let parentHenBatchId = $("#parentHenBatchMainFilter").val();

  if (parentHenBatchId !== "") {
    const warehouses = await getWarehousesByHenBatch(parentHenBatchId);
    WHDiv.style.display = "block";
    if (warehouses.length > 1) {
      refreshWarehouses(warehouses);
    } else {
      warehouse.value = warehouses[0].value;
      const lines = await getLines(warehouses[0].value, parentHenBatchId);
      if (lines.length > 1) {
        refreshLines(lines);
        LineDiv.style.display = "block";
      } else {
        LineDiv.style.display = "none";
        line.value = lines[0].value;
      }
    }
  }
}

let applyDateFilters = false;

$("#filter-from-date").on("dp.change", function () {
  if ($(this).val() != "" && applyDateFilters) {
  }
});

$("#regionalFilter").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();
  var regionalId = $(this).val();
  $.ajax({
    url: `/ManagerialLaying/GetUnitsByRegionalAjax?regionalId=${regionalId}`,
    type: "GET",
  }).done(function (units) {
    let $unitFilter = $("#unitFilter");
    $unitFilter.empty();

    reloadExtensionists();
    reloadSupervisors();

    $unitFilter.append($("<option>", { value: "", text: "Todas" }));
    $.each(units, function (index, option) {
      $unitFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });

    $unitFilter.select2("open");
    loadBedEggsChart();
  });
});

$("#henbatch-status").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();
  var henBatchStatus = $(this).val();
  var farmId = $("#productorFilter").val();
  const henBatchStatusDictionary = {
    active: true,
    closed: false,
  };
  const henBatchStatusParam = henBatchStatusDictionary[henBatchStatus] ?? null;
  $.ajax({
    url: `/ManagerialLaying/GetHenBatchListByFarmIdAjax?farmId=${
      farmId ?? null
    }&henBatchStatus=${henBatchStatusParam}`,
    type: "GET",
  }).done(function (henBatchList) {
    let $parentHenBatchMainFilter = $("#parentHenBatchMainFilter");
    $parentHenBatchMainFilter.empty();
    $parentHenBatchMainFilter.append(
      $("<option>", { value: "", text: "Todos" })
    );
    $.each(henBatchList, function (index, option) {
      $parentHenBatchMainFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
});

$("#productorFilter").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();
  var farmId = $(this).val();
  var henBatchStatus = $("#henbatch-status").val();
  const henBatchStatusDictionary = {
    active: true,
    closed: false,
  };
  const henBatchStatusParam = henBatchStatusDictionary[henBatchStatus] ?? null;

  $.ajax({
    url: `/ManagerialLaying/GetHenBatchListByFarmIdAjax?farmId=${
      farmId ?? null
    }&henBatchStatus=${henBatchStatusParam}`,
    type: "GET",
  }).done(function (henBatchList) {
    let $parentHenBatchMainFilter = $("#parentHenBatchMainFilter");
    $parentHenBatchMainFilter.empty();
    $parentHenBatchMainFilter.append(
      $("<option>", { value: "", text: "Todos" })
    );
    $.each(henBatchList, function (index, option) {
      $parentHenBatchMainFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
  loadBedEggsChart();
});

$("#unitFilter").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();
  var unitId = $(this).val();
  //Reload Produtor/Fazenda
  reloadProductors(unitId);
  reloadExtensionists(unitId);
  reloadSupervisors(unitId);
  loadBedEggsChart();
});

$("#filter-to-date").on("dp.change", function () {
  if ($(this).val() != "" && applyDateFilters) {
    reloadSearaCards();
  }
});

$("#parentHenBatchMainFilter").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();

  const parentHenBatchId = $(this).val();
  const $warehouseDropdown = $("#warehouse");

  $warehouseDropdown.empty().append('<option value="">Todos</option>');
  $("#line").empty().append('<option value="">Todos</option>');

  WHDiv.style.display = "none";
  LineDiv.style.display = "none";

  getWarehousesByHenBatch(parentHenBatchId)
    .done(function (warehouses) {
      if (warehouses && warehouses.length > 0) {
        refreshWarehouses(warehouses);
        WHDiv.style.display = "block";

        if (warehouses.length === 1) {
          $warehouseDropdown.val(warehouses[0].value);

          getLines(warehouses[0].value, parentHenBatchId)
            .done(function (lines) {
              if (lines && lines.length > 0) {
                refreshLines(lines);
                LineDiv.style.display = "block";

                if (lines.length === 1) {
                  $("#line").val(lines[0].value);
                }
              }
            })
            .fail(function (error) {
              console.error("Error loading lines:", error);
            });
        }
        $warehouseDropdown.select2("open");
      }
    })
    .fail(function (error) {
      console.error("Error loading warehouses:", error);
      swal("Erro", "Falha ao carregar aviários para este lote", "error");
    })
    .always(function () {
      $warehouseDropdown.prop("disabled", false);
      applyDateFilters = true;
    });
});

$("#warehouse").on("change", function () {
  const warehouseId = $(this).val();
  const parentHenBatchId = $("#parentHenBatchMainFilter").val();
  const $lineDropdown = $("#line");

  $("#line").empty().append('<option value="">Todos</option>');
  LineDiv.style.display = "none";

  if (!warehouseId || !parentHenBatchId) {
    return;
  }

  getLines(warehouseId, parentHenBatchId)
    .done(function (lines) {
      if (lines && lines.length > 0) {
        refreshLines(lines);

        if (lines.length === 1) {
          $lineDropdown.val(lines[0].value);
        }

        setTimeout(function () {
          $lineDropdown.select2("open");
        }, 100);
      }
    })
    .fail(function (error) {
      console.error("Error loading lines:", error);
      swal("Erro", "Falha ao carregar linhas para este aviário", "error");
    })
    .always(function () {
      const activeChartButton = $(".btn-report.active");
      if (activeChartButton.length) {
        activeChartButton.trigger("click");
      }
    });
});

function fetchDataForDropdown(dropdownId) {
  const data = [];
  $(`${dropdownId} option`).each(function () {
    data.push({
      value: $(this).val(),
      text: $(this).text(),
      selected: $(this).val() == "",
    });
  });
  return data;
}

function reloadOtherDropdowns(changedDropdownId) {
  const dropdownIds = [
    "#productorFilter",
    "#extensionistFilter",
    "#supervisorFilter",
    "#parentHenBatchMainFilter",
  ];

  dropdownIds.forEach(function (dropdownId) {
    if (dropdownId !== changedDropdownId) {
      const data = fetchDataForDropdown(dropdownId); // Obtiene los datos actualizados
      loadDropdown(data, dropdownId); // Recarga el dropdown
    }
  });
}

function reloadSearaCards() {
  GetSearaCards();
}

function reloadSupervisors(unitId) {
  let $supervisorFilter = $("#supervisorFilter");
  if ([null, undefined].includes(unitId)) {
    $supervisorFilter.empty();
    $supervisorFilter.append($("<option>", { value: "", text: "Todos" }));
    return;
  }
  $.ajax({
    url: `/ManagerialLaying/GetSupervisorByUnitAjax?unitId=${unitId}`,
    type: "GET",
  }).done(function (supervisors) {
    $supervisorFilter.empty();
    $supervisorFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(supervisors, function (index, option) {
      $supervisorFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

function reloadExtensionists(unitId) {
  let $extensionistFilter = $("#extensionistFilter");
  const supervisorId = $("#supervisorFilter").val();

  if ([null, undefined].includes(unitId)) {
    $extensionistFilter.empty();
    $extensionistFilter.append($("<option>", { value: "", text: "Todos" }));

    return;
  }
  $.ajax({
    url: `/ManagerialLaying/GetExtensionistsByUnitAjax?unitId=${unitId}&supervisorId=${supervisorId}`,
    type: "GET",
  }).done(function (extensionists) {
    $extensionistFilter.empty();
    $extensionistFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(extensionists, function (index, option) {
      $extensionistFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

function reloadProductors(unitId) {
  let $productorFilter = $("#productorFilter");
  if ([null, undefined].includes(unitId)) {
    $productorFilter.empty();
    $productorFilter.append($("<option>", { value: "", text: "Todos" }));
    return;
  }
  $.ajax({
    url: `/ManagerialLaying/GetFarmsByUnitIdAjax?unitId=${unitId}`,
    type: "GET",
  }).done(function (extensionists) {
    $productorFilter.empty();
    $productorFilter.append($("<option>", { value: "", text: "Todos" }));
    $.each(extensionists, function (index, option) {
      $productorFilter.append(
        $("<option>", { value: option.value, text: option.text })
      );
    });
  });
}

const reportTypes = {
  saleable: (filters) => {
    applyFiltersToSaleableChicksChart(true, filters);
    reloadSearaCards();
  },
  mf_ratio: function (filters) {
    applyFiltersToMaleFemaleChart(true, filters);
    reloadSearaCards();
  },
  gad: function (filters) {
    applyFiltersToFeedIntakeGADChart(true, filters);
    reloadSearaCards();
  },
  bed_eggs: function (filters) {
    applyFiltersToBedEggsChart(true, filters);
    reloadSearaCards();
  },
  genetic: function (filters) {
    // Ensure the genetic chart is properly cleared before applying filters
    if (geneticChart) {
      geneticChart.destroy();
      geneticChart = null;
    }

    // Clear the genetic canvas element
    const geneticCanvas = $("#Genetic");
    if (geneticCanvas.length) {
      const parent = geneticCanvas.parent();
      const canvasHtml = `<canvas class="ignite-chart performance-standards-chart"
                            data-performance-label="Rendimento (%)"
                            data-weeks-from-birth-label="Semanas de idade"
                            data-body-weight-label="Peso corporal (g)"
                            data-mortality-label="Mortalidade (%)"
                            id="Genetic">
                          </canvas>`;
      geneticCanvas.remove();
      parent.html(canvasHtml);
    }

    applyFiltersToGeneticChart(true, filters);
    reloadSearaCards();
  },
  oifc: function (filters) {
    applyFiltersToOIFCChart(true, filters);
    reloadSearaCards();
  },
};

function getAllFilters() {
  const startWeek = $("#startWeek").val();
  const endWeek = $("#endWeek").val();
  const monthYear = $("#monthYearFilter").val();

  const filters = {
    // Filtros de Data
    minDate: $("#filter-from-date").val(),
    maxDate: $("#filter-to-date").val(),

    // Filtro de Mês/Ano
    monthYear: monthYear,

    // Filtros de Semana
    startWeek: startWeek,
    endWeek: endWeek,

    // Filtros de Usuários
    productorId: $("#productorFilter").val(),
    supervisorId: $("#supervisorFilter").val(),
    extensionistId: $("#extensionistFilter").val(),
    userId: getUserFilterValue(),

    // Filtros de Status e Lotes
    henBatchStatus: $("#henbatch-status").val(),
    henBatchId: $("#parentHenBatchMainFilter").val(),

    // Filtros de Localização
    warehouseId: $("#warehouse").val(),
    lineId: $("#line").val(),

    // Filtros de Regional e Unidade
    regionalId: $("#regionalFilter").val(),
    unitId: $("#unitFilter").val(),

    // Filtro de Genética
    geneticsId: $("#geneticFilter").val(),

    // Filtros de Radio Buttons
    gender: getSelectedGender(),
    reportType: $("input[name='reportType']:checked").val(),
  };

  return filters;
}

function clearAllCards() {
  $("#sampleCard").html("");
}

function clearAllCharts() {
  // Lista de IDs de todos os gráficos que precisam ser limpos
  const chartIds = [
    "Saleable",
    "ViabilityFemale",
    "BirdWeight",
    "BirdWeightGain",
    "FeedIntakeGAD",
    "MaleFemale",
    "OIFC",
    "BedEggs",
  ];

  // Limpa cada gráfico
  chartIds.forEach((id) => {
    const chart = $(`#${id}`);
    if (chart.length) {
      chart.empty();
    }
  });

  if (geneticChart) {
    geneticChart.destroy();
    geneticChart = null;
  }
  // Clear and hide the genetic chart container
  const geneticCanvas = $("#Genetic");
  if (geneticCanvas.length) {
    const parent = geneticCanvas.parent();
    const canvasHtml = `<canvas class="ignite-chart performance-standards-chart"
                          data-performance-label="Rendimento (%)"
                          data-weeks-from-birth-label="Semanas de idade"
                          data-body-weight-label="Peso corporal (g)"
                          data-mortality-label="Mortalidade (%)"
                          id="Genetic">
                        </canvas>`;
    geneticCanvas.remove();
    parent.html(canvasHtml);
    parent.hide(); // Hide the genetic chart container
  }
}

function unselectChartButtons() {
  $(".btn-group .btn-report").removeClass("active");
}

function disableChartButtons() {
  // Desabilita todos os botões de gráfico
  $(".btn-group .btn-report").prop("disabled", true);
}

function showLoadingSpinner() {
  // Mostra o spinner de carregamento
  $(".spinner-border").css("display", "inline-block");
}

function enableChartButtons() {
  // Habilita todos os botões de gráfico
  $(".btn-group .btn-report").prop("disabled", false);
}

function hideLoadingSpinner() {
  // Esconde o spinner de carregamento
  $(".spinner-border").css("display", "none");
}

function handleFilterButtons() {
  $(".btn-group").on("click", ".btn-report", function () {
    const $button = $(this);
    const reportType = $button.data("report-type");

    // Atualiza estado visual dos botões
    $(".btn-group .btn").removeClass("active");
    $button.addClass("active");

    // Obtém todos os filtros
    const filters = getAllFilters();

    // Validação de datas ou semanas
    const hasDateFilter = filters.minDate && filters.maxDate;
    const hasWeekFilter = filters.startWeek && filters.endWeek;

    if (!hasDateFilter && !hasWeekFilter) {
      swal(
        "",
        "Selecione pelo menos um período válido (datas ou semanas)",
        "warning"
      );
      return;
    }

    // Validação de semanas
    if (
      filters.endWeek &&
      filters.startWeek &&
      parseInt(filters.endWeek) < parseInt(filters.startWeek)
    ) {
      swal(
        "",
        "A semana final não pode ser menor que a semana inicial",
        "warning"
      );
      return;
    }

    // Validação de datas
    if (
      filters.minDate &&
      filters.maxDate &&
      new Date(filters.minDate.split("/").reverse().join("-")) >
        new Date(filters.maxDate.split("/").reverse().join("-"))
    ) {
      swal("", "A data inicial não pode ser maior que a data final", "warning");
      return;
    }

    if (reportTypes[reportType]) {
      // Make sure to clear all charts, especially the genetic chart
      if (geneticChart) {
        geneticChart.destroy();
        geneticChart = null;
      }

      // Hide the genetic chart container if we're not showing the genetic chart
      if (reportType !== "genetic") {
        $("#Genetic").parent().hide();
      }

      clearAllCharts();
      reportTypes[reportType](filters);
    }
  });

  $("input[name='reportType']").on("change", function () {
    loadBedEggsChart();
  });
}

$("#extensionistFilter").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();
  if ($(this).val() != "") {
    applyDateFilters = true;
  }
  loadBedEggsChart();
});

$("#supervisorFilter").on("change", function () {
  clearAllCards();
  clearAllCharts();
  unselectChartButtons();
  reloadExtensionists($("#unitFilter").val());
  if ($(this).val() != "") {
    applyDateFilters = true;
  }
  loadBedEggsChart();
});

function loadDropdown(data, dropdownId) {
  $(dropdownId).empty(); // Vacía las opciones actuales
  data.forEach(function (value) {
    const opt = document.createElement("option");
    opt.value = value.value;
    opt.text = value.text;
    opt.selected = value.selected;
    $(dropdownId).append(opt); // Agrega la nueva opción
  });
}

$("#warehouse").on("change", async function () {
  const warehouseId = $(this).val();
  const parentHenBatchId = $("#parentHenBatchMainFilter").val();

  $("#line").empty().append('<option value="">Todos</option>');

  if (LineDiv) {
    LineDiv.style.display = "none";
  }

  if (!warehouseId || !parentHenBatchId) {
    return;
  }

  getLines(warehouseId, parentHenBatchId)
    .done(function (lines) {
      if (lines && lines.length > 0) {
        refreshLines(lines);
        if (LineDiv) {
          LineDiv.style.display = "block";
        }

        if (lines.length === 1) {
          $("#line").val(lines[0].value);
        }
      }
    })
    .fail(function (error) {
      console.error("Error loading lines:", error);
      swal("Erro", "Falha ao carregar linhas para este aviário", "error");
    })
    .always(function () {
      $("#line").prop("disabled", false);

      const activeChartButton = $(".btn-report.active");
      if (activeChartButton.length) {
        activeChartButton.trigger("click");
      }
    });
});

// User Context Auto-Filtering Functions
async function initializeUserContextFilters() {
  try {
    const userFilters = await getUserContextFilters();

    if (userFilters) {
      await applyUserContextFilters(userFilters);
    }
  } catch (error) {
    console.warn("Could not load user context filters:", error);
    // Continue with normal operation if user context fails
  }
}

async function getUserContextFilters() {
  try {
    const response = await fetch(
      `${location.origin}/ManagerialLaying/GetUserContextFiltersAjax`
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching user context filters:", error);
    return null;
  }
}

async function applyUserContextFilters(userFilters) {
  // Apply regional filter
  if (userFilters.regional && userFilters.regional.length > 0) {
    await applyFilterWithAutoSelection(
      "#regionalFilter",
      userFilters.regional,
      "regional"
    );
  }

  // Apply unit filter
  if (userFilters.unidade && userFilters.unidade.length > 0) {
    await applyFilterWithAutoSelection(
      "#unitFilter",
      userFilters.unidade,
      "unidade"
    );
  }

  // Apply produtor filter
  if (userFilters.produtor && userFilters.produtor.length > 0) {
    await applyFilterWithAutoSelection(
      "#productorFilter",
      userFilters.produtor,
      "produtor"
    );
  }

  // Apply supervisor filter
  if (userFilters.supervisor && userFilters.supervisor.length > 0) {
    await applyFilterWithAutoSelection(
      "#supervisorFilter",
      userFilters.supervisor,
      "supervisor"
    );
  }

  // Apply extensionist filter
  if (userFilters.extensionista && userFilters.extensionista.length > 0) {
    await applyFilterWithAutoSelection(
      "#extensionistFilter",
      userFilters.extensionista,
      "extensionista"
    );
  }
}

async function applyFilterWithAutoSelection(selector, options, filterType) {
  const $filter = $(selector);

  if (!$filter.length) {
    console.warn(`Filter ${selector} not found`);
    return;
  }

  // Store current value to avoid unnecessary changes
  const currentValue = $filter.val();

  // Temporarily disable change events to prevent chart interference
  $filter.off("change.autoFilter");

  // Check if we need to update the options
  const existingOptions = $filter
    .find("option")
    .map(function () {
      return { value: this.value, text: this.text };
    })
    .get();

  // Only update if the options are different
  const needsUpdate = !arraysEqual(existingOptions.slice(1), options); // Skip first default option

  if (needsUpdate) {
    // Preserve the default option
    const defaultOption = $filter.find("option:first").clone();
    $filter.empty();
    if (defaultOption.length) {
      $filter.append(defaultOption);
    }

    // Add user-specific options
    options.forEach((option) => {
      $filter.append(new Option(option.text, option.value));
    });
  }

  // Auto-select and disable if only one option (plus default)
  if (options.length === 1) {
    const newValue = options[0].value;

    // Only update if value actually changed
    if (currentValue !== newValue) {
      $filter.val(newValue);

      // Update Select2 display if it's a Select2 dropdown
      if ($filter.hasClass("select2-hidden-accessible")) {
        $filter.trigger("change.select2");
      }

      console.log(`Auto-selected ${filterType}: ${options[0].text}`);
    }

    $filter.prop("disabled", true);
  } else if (options.length > 1) {
    // If multiple options, keep enabled but don't auto-select
    console.log(`Multiple ${filterType} options available: ${options.length}`);
  }

  // Re-enable change events after a short delay
  setTimeout(() => {
    $filter.on("change.autoFilter", function () {
      // This will be the normal change handler
    });
  }, 100);
}

// Helper function to compare arrays
function arraysEqual(a, b) {
  if (a.length !== b.length) return false;
  for (let i = 0; i < a.length; i++) {
    if (a[i].value !== b[i].value || a[i].text !== b[i].text) return false;
  }
  return true;
}
